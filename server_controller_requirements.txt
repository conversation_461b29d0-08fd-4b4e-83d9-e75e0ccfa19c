# متطلبات أداة التحكم في السيرفر - Server Controller Requirements

# مكتبات Python الأساسية
requests>=2.28.0
tkinter  # مدمجة مع Python

# مكتبات إضافية (اختيارية)
Pillow>=9.0.0  # لمعالجة الصور إذا احتجنا لها
urllib3>=1.26.0  # للطلبات المتقدمة

# ملاحظات:
# - tkinter مدمجة مع Python ولا تحتاج تثبيت منفصل
# - requests هي المكتبة الوحيدة المطلوبة للعمل الأساسي
# - باقي المكتبات اختيارية للميزات المتقدمة

# تثبيت المتطلبات:
# pip install -r server_controller_requirements.txt
