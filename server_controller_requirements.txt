# متطلبات أداة التحكم في السيرفر الاحترافية - Server Controller Professional Requirements

# مكتبات Python الأساسية
requests>=2.28.0

# مكتبات متقدمة للأداة الاحترافية
psutil>=5.8.0

# مكتبات مدمجة مع Python (لا تحتاج تثبيت)
# tkinter - واجهة المستخدم الرسومية
# sqlite3 - قاعدة البيانات المحلية
# hashlib - التشفير والأمان
# base64 - تشفير البيانات
# zipfile - ضغط الملفات والنسخ الاحتياطية
# shutil - عمليات الملفات المتقدمة
# subprocess - تشغيل العمليات الخارجية
# threading - المعالجة المتوازية
# json - معالجة البيانات
# logging - نظام السجلات المتقدم
# datetime - التعامل مع التاريخ والوقت
# pathlib - إدارة المسارات
# webbrowser - فتح المتصفح
# urllib.parse - معالجة الروابط
# typing - تحديد أنواع البيانات
# asyncio - البرمجة غير المتزامنة
# concurrent.futures - المعالجة المتوازية المتقدمة
# configparser - إدارة ملفات الإعدادات
# platform - معلومات النظام
# os - عمليات نظام التشغيل
# sys - معلومات Python
# time - التعامل مع الوقت

# مكتبات إضافية (اختيارية)
Pillow>=9.0.0  # لمعالجة الصور إذا احتجنا لها
urllib3>=1.26.0  # للطلبات المتقدمة

# ملاحظات:
# - tkinter مدمجة مع Python ولا تحتاج تثبيت منفصل
# - requests هي المكتبة الوحيدة المطلوبة للعمل الأساسي
# - باقي المكتبات اختيارية للميزات المتقدمة

# تثبيت المتطلبات:
# pip install -r server_controller_requirements.txt
