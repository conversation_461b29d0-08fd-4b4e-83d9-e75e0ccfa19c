#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎨 مدير الواجهة الاحترافية - Professional UI Manager
مسؤول عن إنشاء وإدارة الواجهة الاحترافية للأداة
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
from datetime import datetime

class UIManager:
    """مدير الواجهة الاحترافية"""

    def __init__(self, parent):
        """تهيئة مدير الواجهة"""
        self.parent = parent
        self.root = parent.root
        self.colors = parent.colors
        self.fonts = parent.fonts

        # متغيرات الواجهة
        self.current_section = "dashboard"
        self.sidebar_expanded = True
        self.widgets = {}

        self.parent.logger.info("🎨 تم تهيئة مدير الواجهة الاحترافية")

    def create_professional_interface(self):
        """إنشاء الواجهة الاحترافية الكاملة"""
        # إنشاء الهيكل الرئيسي
        self.create_main_layout()

        # إنشاء شريط العنوان المخصص
        self.create_custom_title_bar()

        # إنشاء الشريط الجانبي
        self.create_professional_sidebar()

        # إنشاء المنطقة الرئيسية
        self.create_main_content_area()

        # إنشاء شريط الحالة المتقدم
        self.create_advanced_status_bar()

        # تطبيق التأثيرات البصرية
        self.apply_visual_effects()

        self.parent.logger.info("✅ تم إنشاء الواجهة الاحترافية")

    def create_main_layout(self):
        """إنشاء التخطيط الرئيسي"""
        # الإطار الرئيسي
        self.main_container = tk.Frame(self.root, bg=self.colors['primary'])
        self.main_container.pack(fill=tk.BOTH, expand=True)

        # شريط العنوان
        self.title_bar_frame = tk.Frame(self.main_container,
                                       bg=self.colors['secondary'],
                                       height=40)
        self.title_bar_frame.pack(fill=tk.X)
        self.title_bar_frame.pack_propagate(False)

        # الجسم الرئيسي
        self.body_frame = tk.Frame(self.main_container, bg=self.colors['primary'])
        self.body_frame.pack(fill=tk.BOTH, expand=True)

        # الشريط الجانبي
        self.sidebar_frame = tk.Frame(self.body_frame,
                                     bg=self.colors['secondary'],
                                     width=250)
        self.sidebar_frame.pack(side=tk.LEFT, fill=tk.Y)
        self.sidebar_frame.pack_propagate(False)

        # المنطقة الرئيسية
        self.content_frame = tk.Frame(self.body_frame, bg=self.colors['primary'])
        self.content_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # شريط الحالة
        self.status_bar_frame = tk.Frame(self.main_container,
                                        bg=self.colors['secondary'],
                                        height=30)
        self.status_bar_frame.pack(fill=tk.X, side=tk.BOTTOM)
        self.status_bar_frame.pack_propagate(False)

    def create_custom_title_bar(self):
        """إنشاء شريط العنوان المخصص"""
        # أيقونة التطبيق
        self.app_icon = tk.Label(self.title_bar_frame,
                                text="🎮",
                                font=('Segoe UI', 16),
                                bg=self.colors['secondary'],
                                fg=self.colors['text'])
        self.app_icon.pack(side=tk.LEFT, padx=10, pady=5)

        # عنوان التطبيق
        title_text = f"{self.parent.app_info['name']} v{self.parent.app_info['version']}"
        self.app_title = tk.Label(self.title_bar_frame,
                                 text=title_text,
                                 font=self.fonts['heading'],
                                 bg=self.colors['secondary'],
                                 fg=self.colors['text'])
        self.app_title.pack(side=tk.LEFT, pady=5)

        # أزرار التحكم في النافذة
        self.create_window_controls()

        # معلومات الحالة
        self.connection_status = tk.Label(self.title_bar_frame,
                                         text="❌ غير متصل",
                                         font=self.fonts['small'],
                                         bg=self.colors['secondary'],
                                         fg=self.colors['error'])
        self.connection_status.pack(side=tk.RIGHT, padx=10, pady=5)

    def create_window_controls(self):
        """إنشاء أزرار التحكم في النافذة"""
        controls_frame = tk.Frame(self.title_bar_frame, bg=self.colors['secondary'])
        controls_frame.pack(side=tk.RIGHT, padx=5)

        # زر تصغير
        minimize_btn = tk.Button(controls_frame,
                                text="🗕",
                                font=('Segoe UI', 10),
                                bg=self.colors['secondary'],
                                fg=self.colors['text'],
                                bd=0,
                                padx=10,
                                command=self.minimize_window)
        minimize_btn.pack(side=tk.LEFT)

        # زر تكبير/استعادة
        self.maximize_btn = tk.Button(controls_frame,
                                     text="🗖",
                                     font=('Segoe UI', 10),
                                     bg=self.colors['secondary'],
                                     fg=self.colors['text'],
                                     bd=0,
                                     padx=10,
                                     command=self.toggle_maximize)
        self.maximize_btn.pack(side=tk.LEFT)

        # زر إغلاق
        close_btn = tk.Button(controls_frame,
                             text="🗙",
                             font=('Segoe UI', 10),
                             bg=self.colors['error'],
                             fg=self.colors['text'],
                             bd=0,
                             padx=10,
                             command=self.parent.on_closing)
        close_btn.pack(side=tk.LEFT)

    def create_professional_sidebar(self):
        """إنشاء الشريط الجانبي الاحترافي"""
        # عنوان الشريط الجانبي
        sidebar_title = tk.Label(self.sidebar_frame,
                                text="🎛️ لوحة التحكم",
                                font=self.fonts['heading'],
                                bg=self.colors['secondary'],
                                fg=self.colors['text'],
                                pady=15)
        sidebar_title.pack(fill=tk.X)

        # أقسام التحكم
        self.create_sidebar_sections()

        # زر طي/توسيع الشريط الجانبي
        toggle_btn = tk.Button(self.sidebar_frame,
                              text="◀",
                              font=self.fonts['normal'],
                              bg=self.colors['accent'],
                              fg=self.colors['text'],
                              bd=0,
                              pady=5,
                              command=self.toggle_sidebar)
        toggle_btn.pack(side=tk.BOTTOM, fill=tk.X)

    def create_sidebar_sections(self):
        """إنشاء أقسام الشريط الجانبي"""
        sections = [
            {
                'name': 'dashboard',
                'title': '📊 لوحة المعلومات',
                'icon': '📊',
                'description': 'نظرة عامة على النظام'
            },
            {
                'name': 'connection',
                'title': '🔗 إدارة الاتصال',
                'icon': '🔗',
                'description': 'إعدادات الاتصال بالسيرفر'
            },
            {
                'name': 'server_control',
                'title': '🛠️ التحكم في السيرفر',
                'icon': '🛠️',
                'description': 'تشغيل وإيقاف الخدمات'
            },
            {
                'name': 'gfx_management',
                'title': '🎮 إدارة GFX',
                'icon': '🎮',
                'description': 'التحكم في برنامج GFX'
            },
            {
                'name': 'file_management',
                'title': '📁 إدارة الملفات',
                'icon': '📁',
                'description': 'رفع وتحديث الملفات'
            },
            {
                'name': 'monitoring',
                'title': '📈 المراقبة والأداء',
                'icon': '📈',
                'description': 'مراقبة الأداء المباشر'
            },
            {
                'name': 'security',
                'title': '🔒 الأمان والحماية',
                'icon': '🔒',
                'description': 'إعدادات الأمان'
            },
            {
                'name': 'backup',
                'title': '💾 النسخ الاحتياطية',
                'icon': '💾',
                'description': 'إدارة النسخ الاحتياطية'
            },
            {
                'name': 'reports',
                'title': '📋 التقارير والإحصائيات',
                'icon': '📋',
                'description': 'تقارير مفصلة'
            },
            {
                'name': 'settings',
                'title': '⚙️ الإعدادات',
                'icon': '⚙️',
                'description': 'إعدادات الأداة'
            }
        ]

        # إنشاء أزرار الأقسام
        self.section_buttons = {}
        for section in sections:
            self.create_section_button(section)

    def create_section_button(self, section):
        """إنشاء زر قسم في الشريط الجانبي"""
        # إطار الزر
        btn_frame = tk.Frame(self.sidebar_frame, bg=self.colors['secondary'])
        btn_frame.pack(fill=tk.X, padx=5, pady=2)

        # الزر الرئيسي
        btn = tk.Button(btn_frame,
                       text=f"{section['icon']} {section['title']}",
                       font=self.fonts['normal'],
                       bg=self.colors['secondary'],
                       fg=self.colors['text'],
                       bd=0,
                       anchor='w',
                       padx=15,
                       pady=8,
                       command=lambda: self.switch_section(section['name']))
        btn.pack(fill=tk.X)

        # تأثيرات التمرير
        def on_enter(e):
            btn.config(bg=self.colors['hover'])

        def on_leave(e):
            if self.current_section != section['name']:
                btn.config(bg=self.colors['secondary'])

        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)

        # حفظ مرجع الزر
        self.section_buttons[section['name']] = btn

    def create_main_content_area(self):
        """إنشاء المنطقة الرئيسية للمحتوى"""
        # عنوان القسم الحالي
        self.section_title = tk.Label(self.content_frame,
                                     text="📊 لوحة المعلومات",
                                     font=self.fonts['title'],
                                     bg=self.colors['primary'],
                                     fg=self.colors['text'],
                                     pady=20)
        self.section_title.pack(fill=tk.X, padx=20)

        # منطقة المحتوى القابلة للتمرير
        self.create_scrollable_content()

        # تحميل القسم الافتراضي
        self.load_dashboard_section()

    def create_scrollable_content(self):
        """إنشاء منطقة محتوى قابلة للتمرير"""
        # إطار التمرير
        self.scroll_frame = tk.Frame(self.content_frame, bg=self.colors['primary'])
        self.scroll_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(self.scroll_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # منطقة المحتوى
        self.content_canvas = tk.Canvas(self.scroll_frame,
                                       bg=self.colors['primary'],
                                       highlightthickness=0,
                                       yscrollcommand=scrollbar.set)
        self.content_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        scrollbar.config(command=self.content_canvas.yview)

        # إطار المحتوى الداخلي
        self.inner_content = tk.Frame(self.content_canvas, bg=self.colors['primary'])
        self.content_canvas.create_window((0, 0), window=self.inner_content, anchor='nw')

        # ربط أحداث التمرير
        def configure_scroll_region(event):
            self.content_canvas.configure(scrollregion=self.content_canvas.bbox("all"))

        self.inner_content.bind('<Configure>', configure_scroll_region)

    def create_advanced_status_bar(self):
        """إنشاء شريط الحالة المتقدم"""
        # معلومات الحالة
        self.status_info = tk.Label(self.status_bar_frame,
                                   text="🟢 جاهز",
                                   font=self.fonts['small'],
                                   bg=self.colors['secondary'],
                                   fg=self.colors['success'])
        self.status_info.pack(side=tk.LEFT, padx=10)

        # معلومات الأداء
        self.performance_info = tk.Label(self.status_bar_frame,
                                        text="💾 الذاكرة: 0 MB",
                                        font=self.fonts['small'],
                                        bg=self.colors['secondary'],
                                        fg=self.colors['text'])
        self.performance_info.pack(side=tk.LEFT, padx=10)

        # الوقت الحالي
        self.time_info = tk.Label(self.status_bar_frame,
                                 text="",
                                 font=self.fonts['small'],
                                 bg=self.colors['secondary'],
                                 fg=self.colors['text'])
        self.time_info.pack(side=tk.RIGHT, padx=10)

        # تحديث الوقت
        self.update_time()

    def apply_visual_effects(self):
        """تطبيق التأثيرات البصرية"""
        # تأثيرات الظلال والحدود
        # سيتم إضافة المزيد من التأثيرات لاحقاً
        pass

    def load_dashboard_section(self):
        """تحميل قسم لوحة المعلومات"""
        # مسح المحتوى السابق
        for widget in self.inner_content.winfo_children():
            widget.destroy()

        # إنشاء بطاقات المعلومات
        self.create_info_cards()

        # إنشاء الرسوم البيانية
        self.create_charts_section()

        # إنشاء قائمة الأنشطة الأخيرة
        self.create_recent_activities()

    def create_info_cards(self):
        """إنشاء بطاقات المعلومات"""
        cards_frame = tk.Frame(self.inner_content, bg=self.colors['primary'])
        cards_frame.pack(fill=tk.X, pady=10)

        # بيانات البطاقات
        cards_data = [
            {'title': 'حالة السيرفر', 'value': 'غير متصل', 'icon': '🔴', 'color': self.colors['error']},
            {'title': 'حالة GFX', 'value': 'متوقف', 'icon': '⏹️', 'color': self.colors['warning']},
            {'title': 'الأجهزة المتصلة', 'value': '0', 'icon': '📱', 'color': self.colors['info']},
            {'title': 'وقت التشغيل', 'value': '00:00:00', 'icon': '⏱️', 'color': self.colors['success']}
        ]

        # إنشاء البطاقات
        for i, card in enumerate(cards_data):
            self.create_info_card(cards_frame, card, i)

    def create_info_card(self, parent, card_data, index):
        """إنشاء بطاقة معلومات واحدة"""
        # إطار البطاقة
        card_frame = tk.Frame(parent,
                             bg=self.colors['secondary'],
                             relief='raised',
                             bd=1)
        card_frame.grid(row=0, column=index, padx=10, pady=5, sticky='ew')

        # تكوين الأعمدة
        parent.grid_columnconfigure(index, weight=1)

        # أيقونة البطاقة
        icon_label = tk.Label(card_frame,
                             text=card_data['icon'],
                             font=('Segoe UI', 24),
                             bg=self.colors['secondary'],
                             fg=card_data['color'])
        icon_label.pack(pady=10)

        # عنوان البطاقة
        title_label = tk.Label(card_frame,
                              text=card_data['title'],
                              font=self.fonts['normal'],
                              bg=self.colors['secondary'],
                              fg=self.colors['text'])
        title_label.pack()

        # قيمة البطاقة
        value_label = tk.Label(card_frame,
                              text=card_data['value'],
                              font=self.fonts['heading'],
                              bg=self.colors['secondary'],
                              fg=card_data['color'])
        value_label.pack(pady=5)

    def create_charts_section(self):
        """إنشاء قسم الرسوم البيانية"""
        charts_frame = tk.Frame(self.inner_content, bg=self.colors['primary'])
        charts_frame.pack(fill=tk.X, pady=20)

        # عنوان القسم
        charts_title = tk.Label(charts_frame,
                               text="📈 الرسوم البيانية والإحصائيات",
                               font=self.fonts['heading'],
                               bg=self.colors['primary'],
                               fg=self.colors['text'])
        charts_title.pack(anchor='w', pady=10)

        # منطقة الرسوم البيانية
        chart_area = tk.Frame(charts_frame,
                             bg=self.colors['secondary'],
                             height=200)
        chart_area.pack(fill=tk.X, pady=10)
        chart_area.pack_propagate(False)

        # رسالة مؤقتة
        chart_placeholder = tk.Label(chart_area,
                                    text="📊 سيتم إضافة الرسوم البيانية قريباً",
                                    font=self.fonts['normal'],
                                    bg=self.colors['secondary'],
                                    fg=self.colors['text'])
        chart_placeholder.pack(expand=True)

    def create_recent_activities(self):
        """إنشاء قائمة الأنشطة الأخيرة"""
        activities_frame = tk.Frame(self.inner_content, bg=self.colors['primary'])
        activities_frame.pack(fill=tk.X, pady=20)

        # عنوان القسم
        activities_title = tk.Label(activities_frame,
                                   text="📋 الأنشطة الأخيرة",
                                   font=self.fonts['heading'],
                                   bg=self.colors['primary'],
                                   fg=self.colors['text'])
        activities_title.pack(anchor='w', pady=10)

        # قائمة الأنشطة
        activities_list = tk.Frame(activities_frame, bg=self.colors['secondary'])
        activities_list.pack(fill=tk.X, pady=10)

        # أنشطة تجريبية
        sample_activities = [
            "🚀 تم تشغيل أداة التحكم الاحترافية",
            "📊 تم تحميل لوحة المعلومات",
            "⚙️ تم إعداد النظام الأساسي",
            "🎨 تم تطبيق السمة الاحترافية"
        ]

        for activity in sample_activities:
            activity_label = tk.Label(activities_list,
                                     text=f"• {activity}",
                                     font=self.fonts['small'],
                                     bg=self.colors['secondary'],
                                     fg=self.colors['text'],
                                     anchor='w')
            activity_label.pack(fill=tk.X, padx=10, pady=2)

    # ===== وظائف التحكم في النافذة =====

    def minimize_window(self):
        """تصغير النافذة"""
        self.root.iconify()

    def toggle_maximize(self):
        """تبديل تكبير النافذة"""
        if self.root.state() == 'zoomed':
            self.root.state('normal')
            self.maximize_btn.config(text="🗖")
        else:
            self.root.state('zoomed')
            self.maximize_btn.config(text="🗗")

    def toggle_sidebar(self):
        """تبديل عرض الشريط الجانبي"""
        if self.sidebar_expanded:
            self.sidebar_frame.pack_forget()
            self.sidebar_expanded = False
        else:
            self.sidebar_frame.pack(side=tk.LEFT, fill=tk.Y)
            self.sidebar_expanded = True

    def switch_section(self, section_name):
        """التبديل بين الأقسام"""
        # تحديث الزر النشط
        for name, btn in self.section_buttons.items():
            if name == section_name:
                btn.config(bg=self.colors['selected'])
            else:
                btn.config(bg=self.colors['secondary'])

        # تحديث القسم الحالي
        self.current_section = section_name

        # تحميل محتوى القسم
        self.load_section_content(section_name)

    def load_section_content(self, section_name):
        """تحميل محتوى القسم المحدد"""
        # تحديث عنوان القسم
        section_titles = {
            'dashboard': '📊 لوحة المعلومات',
            'connection': '🔗 إدارة الاتصال',
            'server_control': '🛠️ التحكم في السيرفر',
            'gfx_management': '🎮 إدارة GFX',
            'file_management': '📁 إدارة الملفات',
            'monitoring': '📈 المراقبة والأداء',
            'security': '🔒 الأمان والحماية',
            'backup': '💾 النسخ الاحتياطية',
            'reports': '📋 التقارير والإحصائيات',
            'settings': '⚙️ الإعدادات'
        }

        self.section_title.config(text=section_titles.get(section_name, section_name))

        # تحميل محتوى القسم المناسب
        if section_name == 'dashboard':
            self.load_dashboard_section()
        else:
            self.load_placeholder_section(section_name)

    def load_placeholder_section(self, section_name):
        """تحميل قسم حقيقي أو مؤقت"""
        # مسح المحتوى السابق
        for widget in self.inner_content.winfo_children():
            widget.destroy()

        # تحميل المحتوى الحقيقي حسب القسم
        if section_name == 'connection':
            self.load_connection_section()
        elif section_name == 'server_control':
            self.load_server_control_section()
        elif section_name == 'gfx_management':
            self.load_gfx_management_section()
        elif section_name == 'monitoring':
            self.load_monitoring_section()
        else:
            # رسالة مؤقتة للأقسام غير المكتملة
            placeholder = tk.Label(self.inner_content,
                                  text=f"🚧 قسم {section_name} قيد التطوير\nسيتم إضافة المحتوى قريباً",
                                  font=self.fonts['heading'],
                                  bg=self.colors['primary'],
                                  fg=self.colors['text'],
                                  justify=tk.CENTER)
            placeholder.pack(expand=True, pady=50)

    def load_connection_section(self):
        """تحميل قسم الاتصال الحقيقي"""
        # إطار الاتصال
        connection_frame = tk.Frame(self.inner_content, bg=self.colors['primary'])
        connection_frame.pack(fill=tk.X, padx=20, pady=10)

        # معلومات الاتصال
        info_frame = tk.Frame(connection_frame, bg=self.colors['secondary'], relief='raised', bd=2)
        info_frame.pack(fill=tk.X, pady=10)

        tk.Label(info_frame, text="🔗 إعدادات الاتصال",
                font=self.fonts['heading'], bg=self.colors['secondary'],
                fg=self.colors['text']).pack(pady=10)

        # عنوان السيرفر
        server_frame = tk.Frame(info_frame, bg=self.colors['secondary'])
        server_frame.pack(fill=tk.X, padx=20, pady=5)

        tk.Label(server_frame, text="عنوان السيرفر:",
                font=self.fonts['normal'], bg=self.colors['secondary'],
                fg=self.colors['text']).pack(side=tk.LEFT)

        self.server_url_entry = tk.Entry(server_frame, font=self.fonts['normal'], width=40)
        self.server_url_entry.pack(side=tk.RIGHT, padx=10)
        self.server_url_entry.insert(0, self.parent.connection_config['server_url'])

        # البريد الإلكتروني
        email_frame = tk.Frame(info_frame, bg=self.colors['secondary'])
        email_frame.pack(fill=tk.X, padx=20, pady=5)

        tk.Label(email_frame, text="البريد الإلكتروني:",
                font=self.fonts['normal'], bg=self.colors['secondary'],
                fg=self.colors['text']).pack(side=tk.LEFT)

        self.email_entry = tk.Entry(email_frame, font=self.fonts['normal'], width=40)
        self.email_entry.pack(side=tk.RIGHT, padx=10)
        self.email_entry.insert(0, "<EMAIL>")

        # كلمة المرور
        password_frame = tk.Frame(info_frame, bg=self.colors['secondary'])
        password_frame.pack(fill=tk.X, padx=20, pady=5)

        tk.Label(password_frame, text="كلمة المرور:",
                font=self.fonts['normal'], bg=self.colors['secondary'],
                fg=self.colors['text']).pack(side=tk.LEFT)

        self.password_entry = tk.Entry(password_frame, font=self.fonts['normal'],
                                      width=40, show="*")
        self.password_entry.pack(side=tk.RIGHT, padx=10)
        self.password_entry.insert(0, "admin123")

        # أزرار التحكم
        buttons_frame = tk.Frame(info_frame, bg=self.colors['secondary'])
        buttons_frame.pack(pady=20)

        connect_btn = tk.Button(buttons_frame, text="🔗 اتصال",
                               font=self.fonts['normal'], bg=self.colors['accent'],
                               fg=self.colors['text'], padx=20, pady=5,
                               command=self.connect_to_server)
        connect_btn.pack(side=tk.LEFT, padx=10)

        disconnect_btn = tk.Button(buttons_frame, text="❌ قطع الاتصال",
                                  font=self.fonts['normal'], bg=self.colors['error'],
                                  fg=self.colors['text'], padx=20, pady=5,
                                  command=self.disconnect_from_server)
        disconnect_btn.pack(side=tk.LEFT, padx=10)

        test_btn = tk.Button(buttons_frame, text="🔍 اختبار الاتصال",
                            font=self.fonts['normal'], bg=self.colors['info'],
                            fg=self.colors['text'], padx=20, pady=5,
                            command=self.test_connection)
        test_btn.pack(side=tk.LEFT, padx=10)

        # حالة الاتصال
        self.connection_status_label = tk.Label(info_frame,
                                               text="❌ غير متصل",
                                               font=self.fonts['heading'],
                                               bg=self.colors['secondary'],
                                               fg=self.colors['error'])
        self.connection_status_label.pack(pady=10)

    def connect_to_server(self):
        """الاتصال بالسيرفر"""
        try:
            server_url = self.server_url_entry.get()
            email = self.email_entry.get()
            password = self.password_entry.get()

            if not all([server_url, email, password]):
                self.show_notification("خطأ", "يرجى ملء جميع الحقول", "error")
                return

            # تحديث حالة الاتصال
            self.connection_status_label.config(text="🔄 جاري الاتصال...", fg=self.colors['warning'])
            self.root.update()

            # محاولة الاتصال
            import requests
            response = requests.post(f"{server_url}/api/auth/login",
                                   json={"email": email, "password": password},
                                   timeout=10)

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    # نجح الاتصال
                    self.parent.system_state['is_connected'] = True
                    self.parent.system_state['auth_token'] = data.get('token')
                    self.parent.connection_config['server_url'] = server_url

                    self.connection_status_label.config(text="✅ متصل بنجاح", fg=self.colors['success'])
                    self.update_connection_status(True, server_url)
                    self.show_notification("نجح", "تم الاتصال بالسيرفر بنجاح", "info")
                else:
                    raise Exception(data.get('message', 'فشل تسجيل الدخول'))
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.connection_status_label.config(text="❌ فشل الاتصال", fg=self.colors['error'])
            self.show_notification("خطأ", f"فشل الاتصال بالسيرفر:\n{str(e)}", "error")

    def disconnect_from_server(self):
        """قطع الاتصال من السيرفر"""
        self.parent.system_state['is_connected'] = False
        self.parent.system_state['auth_token'] = None

        self.connection_status_label.config(text="❌ غير متصل", fg=self.colors['error'])
        self.update_connection_status(False)
        self.show_notification("معلومات", "تم قطع الاتصال", "info")

    def test_connection(self):
        """اختبار الاتصال"""
        try:
            server_url = self.server_url_entry.get()
            if not server_url:
                self.show_notification("خطأ", "يرجى إدخال عنوان السيرفر", "error")
                return

            import requests
            import time

            start_time = time.time()
            response = requests.get(server_url, timeout=5)
            end_time = time.time()

            response_time = (end_time - start_time) * 1000

            if response.status_code == 200:
                self.show_notification("نجح", f"السيرفر متاح\nزمن الاستجابة: {response_time:.0f} ms", "info")
            else:
                self.show_notification("تحذير", f"السيرفر يستجيب لكن بحالة: {response.status_code}", "warning")

        except Exception as e:
            self.show_notification("خطأ", f"فشل الاتصال بالسيرفر:\n{str(e)}", "error")

    def load_server_control_section(self):
        """تحميل قسم التحكم في السيرفر"""
        # إطار التحكم
        control_frame = tk.Frame(self.inner_content, bg=self.colors['primary'])
        control_frame.pack(fill=tk.X, padx=20, pady=10)

        # حالة السيرفر
        status_frame = tk.Frame(control_frame, bg=self.colors['secondary'], relief='raised', bd=2)
        status_frame.pack(fill=tk.X, pady=10)

        tk.Label(status_frame, text="🛠️ حالة السيرفر",
                font=self.fonts['heading'], bg=self.colors['secondary'],
                fg=self.colors['text']).pack(pady=10)

        self.server_status_label = tk.Label(status_frame, text="❓ غير معروف",
                                           font=self.fonts['heading'],
                                           bg=self.colors['secondary'],
                                           fg=self.colors['warning'])
        self.server_status_label.pack(pady=5)

        # أزرار التحكم
        buttons_frame = tk.Frame(status_frame, bg=self.colors['secondary'])
        buttons_frame.pack(pady=20)

        start_btn = tk.Button(buttons_frame, text="▶️ تشغيل الأداة",
                             font=self.fonts['normal'], bg=self.colors['success'],
                             fg=self.colors['text'], padx=20, pady=5,
                             command=self.start_tool)
        start_btn.pack(side=tk.LEFT, padx=10)

        stop_btn = tk.Button(buttons_frame, text="⏹️ إيقاف الأداة",
                            font=self.fonts['normal'], bg=self.colors['error'],
                            fg=self.colors['text'], padx=20, pady=5,
                            command=self.stop_tool)
        stop_btn.pack(side=tk.LEFT, padx=10)

        refresh_btn = tk.Button(buttons_frame, text="🔄 تحديث الحالة",
                               font=self.fonts['normal'], bg=self.colors['info'],
                               fg=self.colors['text'], padx=20, pady=5,
                               command=self.refresh_server_status)
        refresh_btn.pack(side=tk.LEFT, padx=10)

    def load_gfx_management_section(self):
        """تحميل قسم إدارة GFX"""
        # إطار GFX
        gfx_frame = tk.Frame(self.inner_content, bg=self.colors['primary'])
        gfx_frame.pack(fill=tk.X, padx=20, pady=10)

        # حالة GFX
        status_frame = tk.Frame(gfx_frame, bg=self.colors['secondary'], relief='raised', bd=2)
        status_frame.pack(fill=tk.X, pady=10)

        tk.Label(status_frame, text="🎮 حالة برنامج GFX",
                font=self.fonts['heading'], bg=self.colors['secondary'],
                fg=self.colors['text']).pack(pady=10)

        self.gfx_status_label = tk.Label(status_frame, text="⏹️ متوقف",
                                        font=self.fonts['heading'],
                                        bg=self.colors['secondary'],
                                        fg=self.colors['error'])
        self.gfx_status_label.pack(pady=5)

        # أزرار التحكم في GFX
        gfx_buttons_frame = tk.Frame(status_frame, bg=self.colors['secondary'])
        gfx_buttons_frame.pack(pady=20)

        start_gfx_btn = tk.Button(gfx_buttons_frame, text="▶️ تشغيل GFX",
                                 font=self.fonts['normal'], bg=self.colors['success'],
                                 fg=self.colors['text'], padx=20, pady=5,
                                 command=self.start_gfx)
        start_gfx_btn.pack(side=tk.LEFT, padx=10)

        stop_gfx_btn = tk.Button(gfx_buttons_frame, text="⏹️ إيقاف GFX",
                                font=self.fonts['normal'], bg=self.colors['error'],
                                fg=self.colors['text'], padx=20, pady=5,
                                command=self.stop_gfx)
        stop_gfx_btn.pack(side=tk.LEFT, padx=10)

        restart_gfx_btn = tk.Button(gfx_buttons_frame, text="🔄 إعادة تشغيل",
                                   font=self.fonts['normal'], bg=self.colors['warning'],
                                   fg=self.colors['text'], padx=20, pady=5,
                                   command=self.restart_gfx)
        restart_gfx_btn.pack(side=tk.LEFT, padx=10)

        # الأجهزة المتصلة
        devices_frame = tk.Frame(gfx_frame, bg=self.colors['secondary'], relief='raised', bd=2)
        devices_frame.pack(fill=tk.X, pady=10)

        tk.Label(devices_frame, text="📱 الأجهزة المتصلة",
                font=self.fonts['heading'], bg=self.colors['secondary'],
                fg=self.colors['text']).pack(pady=10)

        self.devices_label = tk.Label(devices_frame, text="0 جهاز متصل",
                                     font=self.fonts['normal'],
                                     bg=self.colors['secondary'],
                                     fg=self.colors['text'])
        self.devices_label.pack(pady=5)

        refresh_devices_btn = tk.Button(devices_frame, text="🔄 تحديث الأجهزة",
                                       font=self.fonts['normal'], bg=self.colors['info'],
                                       fg=self.colors['text'], padx=20, pady=5,
                                       command=self.refresh_devices)
        refresh_devices_btn.pack(pady=10)

    def load_monitoring_section(self):
        """تحميل قسم المراقبة"""
        # إطار المراقبة
        monitoring_frame = tk.Frame(self.inner_content, bg=self.colors['primary'])
        monitoring_frame.pack(fill=tk.X, padx=20, pady=10)

        # حالة المراقبة
        status_frame = tk.Frame(monitoring_frame, bg=self.colors['secondary'], relief='raised', bd=2)
        status_frame.pack(fill=tk.X, pady=10)

        tk.Label(status_frame, text="📈 مراقبة الأداء",
                font=self.fonts['heading'], bg=self.colors['secondary'],
                fg=self.colors['text']).pack(pady=10)

        # معلومات الأداء
        self.cpu_label = tk.Label(status_frame, text="💻 المعالج: 0%",
                                 font=self.fonts['normal'],
                                 bg=self.colors['secondary'],
                                 fg=self.colors['text'])
        self.cpu_label.pack(pady=2)

        self.memory_label = tk.Label(status_frame, text="💾 الذاكرة: 0%",
                                    font=self.fonts['normal'],
                                    bg=self.colors['secondary'],
                                    fg=self.colors['text'])
        self.memory_label.pack(pady=2)

        self.network_label = tk.Label(status_frame, text="🌐 الشبكة: 0 ms",
                                     font=self.fonts['normal'],
                                     bg=self.colors['secondary'],
                                     fg=self.colors['text'])
        self.network_label.pack(pady=2)

        # أزرار المراقبة
        monitoring_buttons_frame = tk.Frame(status_frame, bg=self.colors['secondary'])
        monitoring_buttons_frame.pack(pady=20)

        start_monitoring_btn = tk.Button(monitoring_buttons_frame, text="▶️ بدء المراقبة",
                                        font=self.fonts['normal'], bg=self.colors['success'],
                                        fg=self.colors['text'], padx=20, pady=5,
                                        command=self.start_monitoring)
        start_monitoring_btn.pack(side=tk.LEFT, padx=10)

        stop_monitoring_btn = tk.Button(monitoring_buttons_frame, text="⏹️ إيقاف المراقبة",
                                       font=self.fonts['normal'], bg=self.colors['error'],
                                       fg=self.colors['text'], padx=20, pady=5,
                                       command=self.stop_monitoring)
        stop_monitoring_btn.pack(side=tk.LEFT, padx=10)

    # ===== وظائف التحكم الحقيقية =====

    def start_tool(self):
        """تشغيل الأداة"""
        if not self.parent.system_state['is_connected']:
            self.show_notification("تحذير", "يجب الاتصال بالسيرفر أولاً", "warning")
            return

        try:
            import requests
            response = requests.post(
                f"{self.parent.connection_config['server_url']}/api/tool/start",
                headers={'Authorization': f"Bearer {self.parent.system_state['auth_token']}"},
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.server_status_label.config(text="✅ الأداة تعمل", fg=self.colors['success'])
                    self.show_notification("نجح", "تم تشغيل الأداة بنجاح", "info")
                else:
                    raise Exception(data.get('message', 'فشل تشغيل الأداة'))
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.show_notification("خطأ", f"فشل تشغيل الأداة:\n{str(e)}", "error")

    def stop_tool(self):
        """إيقاف الأداة"""
        if not self.parent.system_state['is_connected']:
            self.show_notification("تحذير", "يجب الاتصال بالسيرفر أولاً", "warning")
            return

        try:
            import requests
            response = requests.post(
                f"{self.parent.connection_config['server_url']}/api/tool/stop",
                headers={'Authorization': f"Bearer {self.parent.system_state['auth_token']}"},
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.server_status_label.config(text="⏹️ الأداة متوقفة", fg=self.colors['error'])
                    self.show_notification("نجح", "تم إيقاف الأداة بنجاح", "info")
                else:
                    raise Exception(data.get('message', 'فشل إيقاف الأداة'))
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.show_notification("خطأ", f"فشل إيقاف الأداة:\n{str(e)}", "error")

    def refresh_server_status(self):
        """تحديث حالة السيرفر"""
        if not self.parent.system_state['is_connected']:
            self.show_notification("تحذير", "يجب الاتصال بالسيرفر أولاً", "warning")
            return

        try:
            import requests
            response = requests.get(
                f"{self.parent.connection_config['server_url']}/api/tool/status",
                headers={'Authorization': f"Bearer {self.parent.system_state['auth_token']}"},
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    tool = data.get('tool', {})
                    status = tool.get('status', 'unknown')

                    if status == 'running':
                        self.server_status_label.config(text="✅ الأداة تعمل", fg=self.colors['success'])
                    elif status == 'stopped':
                        self.server_status_label.config(text="⏹️ الأداة متوقفة", fg=self.colors['error'])
                    else:
                        self.server_status_label.config(text="❓ حالة غير معروفة", fg=self.colors['warning'])

                    self.show_notification("نجح", "تم تحديث حالة السيرفر", "info")
                else:
                    raise Exception(data.get('message', 'فشل الحصول على الحالة'))
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.show_notification("خطأ", f"فشل تحديث حالة السيرفر:\n{str(e)}", "error")

    def start_gfx(self):
        """تشغيل GFX"""
        if not self.parent.system_state['is_connected']:
            self.show_notification("تحذير", "يجب الاتصال بالسيرفر أولاً", "warning")
            return

        try:
            import requests
            response = requests.post(
                f"{self.parent.connection_config['server_url']}/api/tool/gfx/start",
                headers={'Authorization': f"Bearer {self.parent.system_state['auth_token']}"},
                timeout=15
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.gfx_status_label.config(text="✅ GFX يعمل", fg=self.colors['success'])
                    self.show_notification("نجح", "تم تشغيل GFX بنجاح", "info")
                else:
                    raise Exception(data.get('message', 'فشل تشغيل GFX'))
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.show_notification("خطأ", f"فشل تشغيل GFX:\n{str(e)}", "error")

    def stop_gfx(self):
        """إيقاف GFX"""
        if not self.parent.system_state['is_connected']:
            self.show_notification("تحذير", "يجب الاتصال بالسيرفر أولاً", "warning")
            return

        try:
            import requests
            response = requests.post(
                f"{self.parent.connection_config['server_url']}/api/tool/gfx/stop",
                headers={'Authorization': f"Bearer {self.parent.system_state['auth_token']}"},
                timeout=15
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.gfx_status_label.config(text="⏹️ GFX متوقف", fg=self.colors['error'])
                    self.show_notification("نجح", "تم إيقاف GFX بنجاح", "info")
                else:
                    raise Exception(data.get('message', 'فشل إيقاف GFX'))
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.show_notification("خطأ", f"فشل إيقاف GFX:\n{str(e)}", "error")

    def restart_gfx(self):
        """إعادة تشغيل GFX"""
        self.stop_gfx()
        self.root.after(2000, self.start_gfx)  # انتظار ثانيتين ثم تشغيل

    def refresh_devices(self):
        """تحديث الأجهزة"""
        if not self.parent.system_state['is_connected']:
            self.show_notification("تحذير", "يجب الاتصال بالسيرفر أولاً", "warning")
            return

        try:
            import requests
            response = requests.get(
                f"{self.parent.connection_config['server_url']}/api/tool/adb/devices",
                headers={'Authorization': f"Bearer {self.parent.system_state['auth_token']}"},
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    devices = data.get('devices', [])
                    count = len(devices)
                    self.devices_label.config(text=f"{count} جهاز متصل")
                    self.show_notification("نجح", f"تم العثور على {count} جهاز", "info")
                else:
                    raise Exception(data.get('message', 'فشل الحصول على الأجهزة'))
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.show_notification("خطأ", f"فشل تحديث الأجهزة:\n{str(e)}", "error")

    def start_monitoring(self):
        """بدء المراقبة"""
        if hasattr(self.parent, 'monitoring_manager'):
            self.parent.monitoring_manager.start_monitoring()
            self.show_notification("نجح", "تم بدء المراقبة", "info")
            self.update_monitoring_display()
        else:
            self.show_notification("خطأ", "مدير المراقبة غير متوفر", "error")

    def stop_monitoring(self):
        """إيقاف المراقبة"""
        if hasattr(self.parent, 'monitoring_manager'):
            self.parent.monitoring_manager.stop_monitoring()
            self.show_notification("نجح", "تم إيقاف المراقبة", "info")
        else:
            self.show_notification("خطأ", "مدير المراقبة غير متوفر", "error")

    def update_monitoring_display(self):
        """تحديث عرض المراقبة"""
        try:
            import psutil

            # تحديث معلومات الأداء
            cpu_percent = psutil.cpu_percent(interval=1)
            memory_percent = psutil.virtual_memory().percent

            self.cpu_label.config(text=f"💻 المعالج: {cpu_percent:.1f}%")
            self.memory_label.config(text=f"💾 الذاكرة: {memory_percent:.1f}%")

            # قياس زمن الاستجابة
            if self.parent.system_state['is_connected']:
                try:
                    import requests
                    import time
                    start_time = time.time()
                    requests.get(self.parent.connection_config['server_url'], timeout=2)
                    end_time = time.time()
                    latency = (end_time - start_time) * 1000
                    self.network_label.config(text=f"🌐 الشبكة: {latency:.0f} ms")
                except:
                    self.network_label.config(text="🌐 الشبكة: غير متاح")
            else:
                self.network_label.config(text="🌐 الشبكة: غير متصل")

            # جدولة التحديث التالي
            if hasattr(self.parent, 'monitoring_manager') and self.parent.monitoring_manager.monitoring_active:
                self.root.after(5000, self.update_monitoring_display)

        except Exception as e:
            print(f"خطأ في تحديث المراقبة: {str(e)}")

    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_info.config(text=f"🕐 {current_time}")

        # جدولة التحديث التالي
        self.root.after(1000, self.update_time)

    def update_connection_status(self, connected, server_url=""):
        """تحديث حالة الاتصال"""
        if connected:
            self.connection_status.config(text=f"✅ متصل - {server_url}",
                                         fg=self.colors['success'])
        else:
            self.connection_status.config(text="❌ غير متصل",
                                         fg=self.colors['error'])

    def show_notification(self, title, message, type="info"):
        """عرض إشعار"""
        if type == "error":
            messagebox.showerror(title, message)
        elif type == "warning":
            messagebox.showwarning(title, message)
        else:
            messagebox.showinfo(title, message)
