#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎨 مدير الواجهة الاحترافية - Professional UI Manager
مسؤول عن إنشاء وإدارة الواجهة الاحترافية للأداة
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
from datetime import datetime

class UIManager:
    """مدير الواجهة الاحترافية"""
    
    def __init__(self, parent):
        """تهيئة مدير الواجهة"""
        self.parent = parent
        self.root = parent.root
        self.colors = parent.colors
        self.fonts = parent.fonts
        
        # متغيرات الواجهة
        self.current_section = "dashboard"
        self.sidebar_expanded = True
        self.widgets = {}
        
        self.parent.logger.info("🎨 تم تهيئة مدير الواجهة الاحترافية")
        
    def create_professional_interface(self):
        """إنشاء الواجهة الاحترافية الكاملة"""
        # إنشاء الهيكل الرئيسي
        self.create_main_layout()
        
        # إنشاء شريط العنوان المخصص
        self.create_custom_title_bar()
        
        # إنشاء الشريط الجانبي
        self.create_professional_sidebar()
        
        # إنشاء المنطقة الرئيسية
        self.create_main_content_area()
        
        # إنشاء شريط الحالة المتقدم
        self.create_advanced_status_bar()
        
        # تطبيق التأثيرات البصرية
        self.apply_visual_effects()
        
        self.parent.logger.info("✅ تم إنشاء الواجهة الاحترافية")
        
    def create_main_layout(self):
        """إنشاء التخطيط الرئيسي"""
        # الإطار الرئيسي
        self.main_container = tk.Frame(self.root, bg=self.colors['primary'])
        self.main_container.pack(fill=tk.BOTH, expand=True)
        
        # شريط العنوان
        self.title_bar_frame = tk.Frame(self.main_container, 
                                       bg=self.colors['secondary'], 
                                       height=40)
        self.title_bar_frame.pack(fill=tk.X)
        self.title_bar_frame.pack_propagate(False)
        
        # الجسم الرئيسي
        self.body_frame = tk.Frame(self.main_container, bg=self.colors['primary'])
        self.body_frame.pack(fill=tk.BOTH, expand=True)
        
        # الشريط الجانبي
        self.sidebar_frame = tk.Frame(self.body_frame, 
                                     bg=self.colors['secondary'], 
                                     width=250)
        self.sidebar_frame.pack(side=tk.LEFT, fill=tk.Y)
        self.sidebar_frame.pack_propagate(False)
        
        # المنطقة الرئيسية
        self.content_frame = tk.Frame(self.body_frame, bg=self.colors['primary'])
        self.content_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # شريط الحالة
        self.status_bar_frame = tk.Frame(self.main_container, 
                                        bg=self.colors['secondary'], 
                                        height=30)
        self.status_bar_frame.pack(fill=tk.X, side=tk.BOTTOM)
        self.status_bar_frame.pack_propagate(False)
        
    def create_custom_title_bar(self):
        """إنشاء شريط العنوان المخصص"""
        # أيقونة التطبيق
        self.app_icon = tk.Label(self.title_bar_frame,
                                text="🎮",
                                font=('Segoe UI', 16),
                                bg=self.colors['secondary'],
                                fg=self.colors['text'])
        self.app_icon.pack(side=tk.LEFT, padx=10, pady=5)
        
        # عنوان التطبيق
        title_text = f"{self.parent.app_info['name']} v{self.parent.app_info['version']}"
        self.app_title = tk.Label(self.title_bar_frame,
                                 text=title_text,
                                 font=self.fonts['heading'],
                                 bg=self.colors['secondary'],
                                 fg=self.colors['text'])
        self.app_title.pack(side=tk.LEFT, pady=5)
        
        # أزرار التحكم في النافذة
        self.create_window_controls()
        
        # معلومات الحالة
        self.connection_status = tk.Label(self.title_bar_frame,
                                         text="❌ غير متصل",
                                         font=self.fonts['small'],
                                         bg=self.colors['secondary'],
                                         fg=self.colors['error'])
        self.connection_status.pack(side=tk.RIGHT, padx=10, pady=5)
        
    def create_window_controls(self):
        """إنشاء أزرار التحكم في النافذة"""
        controls_frame = tk.Frame(self.title_bar_frame, bg=self.colors['secondary'])
        controls_frame.pack(side=tk.RIGHT, padx=5)
        
        # زر تصغير
        minimize_btn = tk.Button(controls_frame,
                                text="🗕",
                                font=('Segoe UI', 10),
                                bg=self.colors['secondary'],
                                fg=self.colors['text'],
                                bd=0,
                                padx=10,
                                command=self.minimize_window)
        minimize_btn.pack(side=tk.LEFT)
        
        # زر تكبير/استعادة
        self.maximize_btn = tk.Button(controls_frame,
                                     text="🗖",
                                     font=('Segoe UI', 10),
                                     bg=self.colors['secondary'],
                                     fg=self.colors['text'],
                                     bd=0,
                                     padx=10,
                                     command=self.toggle_maximize)
        self.maximize_btn.pack(side=tk.LEFT)
        
        # زر إغلاق
        close_btn = tk.Button(controls_frame,
                             text="🗙",
                             font=('Segoe UI', 10),
                             bg=self.colors['error'],
                             fg=self.colors['text'],
                             bd=0,
                             padx=10,
                             command=self.parent.on_closing)
        close_btn.pack(side=tk.LEFT)
        
    def create_professional_sidebar(self):
        """إنشاء الشريط الجانبي الاحترافي"""
        # عنوان الشريط الجانبي
        sidebar_title = tk.Label(self.sidebar_frame,
                                text="🎛️ لوحة التحكم",
                                font=self.fonts['heading'],
                                bg=self.colors['secondary'],
                                fg=self.colors['text'],
                                pady=15)
        sidebar_title.pack(fill=tk.X)
        
        # أقسام التحكم
        self.create_sidebar_sections()
        
        # زر طي/توسيع الشريط الجانبي
        toggle_btn = tk.Button(self.sidebar_frame,
                              text="◀",
                              font=self.fonts['normal'],
                              bg=self.colors['accent'],
                              fg=self.colors['text'],
                              bd=0,
                              pady=5,
                              command=self.toggle_sidebar)
        toggle_btn.pack(side=tk.BOTTOM, fill=tk.X)
        
    def create_sidebar_sections(self):
        """إنشاء أقسام الشريط الجانبي"""
        sections = [
            {
                'name': 'dashboard',
                'title': '📊 لوحة المعلومات',
                'icon': '📊',
                'description': 'نظرة عامة على النظام'
            },
            {
                'name': 'connection',
                'title': '🔗 إدارة الاتصال',
                'icon': '🔗',
                'description': 'إعدادات الاتصال بالسيرفر'
            },
            {
                'name': 'server_control',
                'title': '🛠️ التحكم في السيرفر',
                'icon': '🛠️',
                'description': 'تشغيل وإيقاف الخدمات'
            },
            {
                'name': 'gfx_management',
                'title': '🎮 إدارة GFX',
                'icon': '🎮',
                'description': 'التحكم في برنامج GFX'
            },
            {
                'name': 'file_management',
                'title': '📁 إدارة الملفات',
                'icon': '📁',
                'description': 'رفع وتحديث الملفات'
            },
            {
                'name': 'monitoring',
                'title': '📈 المراقبة والأداء',
                'icon': '📈',
                'description': 'مراقبة الأداء المباشر'
            },
            {
                'name': 'security',
                'title': '🔒 الأمان والحماية',
                'icon': '🔒',
                'description': 'إعدادات الأمان'
            },
            {
                'name': 'backup',
                'title': '💾 النسخ الاحتياطية',
                'icon': '💾',
                'description': 'إدارة النسخ الاحتياطية'
            },
            {
                'name': 'reports',
                'title': '📋 التقارير والإحصائيات',
                'icon': '📋',
                'description': 'تقارير مفصلة'
            },
            {
                'name': 'settings',
                'title': '⚙️ الإعدادات',
                'icon': '⚙️',
                'description': 'إعدادات الأداة'
            }
        ]
        
        # إنشاء أزرار الأقسام
        self.section_buttons = {}
        for section in sections:
            self.create_section_button(section)
            
    def create_section_button(self, section):
        """إنشاء زر قسم في الشريط الجانبي"""
        # إطار الزر
        btn_frame = tk.Frame(self.sidebar_frame, bg=self.colors['secondary'])
        btn_frame.pack(fill=tk.X, padx=5, pady=2)
        
        # الزر الرئيسي
        btn = tk.Button(btn_frame,
                       text=f"{section['icon']} {section['title']}",
                       font=self.fonts['normal'],
                       bg=self.colors['secondary'],
                       fg=self.colors['text'],
                       bd=0,
                       anchor='w',
                       padx=15,
                       pady=8,
                       command=lambda: self.switch_section(section['name']))
        btn.pack(fill=tk.X)
        
        # تأثيرات التمرير
        def on_enter(e):
            btn.config(bg=self.colors['hover'])
            
        def on_leave(e):
            if self.current_section != section['name']:
                btn.config(bg=self.colors['secondary'])
                
        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
        
        # حفظ مرجع الزر
        self.section_buttons[section['name']] = btn
        
    def create_main_content_area(self):
        """إنشاء المنطقة الرئيسية للمحتوى"""
        # عنوان القسم الحالي
        self.section_title = tk.Label(self.content_frame,
                                     text="📊 لوحة المعلومات",
                                     font=self.fonts['title'],
                                     bg=self.colors['primary'],
                                     fg=self.colors['text'],
                                     pady=20)
        self.section_title.pack(fill=tk.X, padx=20)
        
        # منطقة المحتوى القابلة للتمرير
        self.create_scrollable_content()
        
        # تحميل القسم الافتراضي
        self.load_dashboard_section()
        
    def create_scrollable_content(self):
        """إنشاء منطقة محتوى قابلة للتمرير"""
        # إطار التمرير
        self.scroll_frame = tk.Frame(self.content_frame, bg=self.colors['primary'])
        self.scroll_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(self.scroll_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # منطقة المحتوى
        self.content_canvas = tk.Canvas(self.scroll_frame,
                                       bg=self.colors['primary'],
                                       highlightthickness=0,
                                       yscrollcommand=scrollbar.set)
        self.content_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        scrollbar.config(command=self.content_canvas.yview)
        
        # إطار المحتوى الداخلي
        self.inner_content = tk.Frame(self.content_canvas, bg=self.colors['primary'])
        self.content_canvas.create_window((0, 0), window=self.inner_content, anchor='nw')
        
        # ربط أحداث التمرير
        def configure_scroll_region(event):
            self.content_canvas.configure(scrollregion=self.content_canvas.bbox("all"))
            
        self.inner_content.bind('<Configure>', configure_scroll_region)
        
    def create_advanced_status_bar(self):
        """إنشاء شريط الحالة المتقدم"""
        # معلومات الحالة
        self.status_info = tk.Label(self.status_bar_frame,
                                   text="🟢 جاهز",
                                   font=self.fonts['small'],
                                   bg=self.colors['secondary'],
                                   fg=self.colors['success'])
        self.status_info.pack(side=tk.LEFT, padx=10)
        
        # معلومات الأداء
        self.performance_info = tk.Label(self.status_bar_frame,
                                        text="💾 الذاكرة: 0 MB",
                                        font=self.fonts['small'],
                                        bg=self.colors['secondary'],
                                        fg=self.colors['text'])
        self.performance_info.pack(side=tk.LEFT, padx=10)
        
        # الوقت الحالي
        self.time_info = tk.Label(self.status_bar_frame,
                                 text="",
                                 font=self.fonts['small'],
                                 bg=self.colors['secondary'],
                                 fg=self.colors['text'])
        self.time_info.pack(side=tk.RIGHT, padx=10)
        
        # تحديث الوقت
        self.update_time()
        
    def apply_visual_effects(self):
        """تطبيق التأثيرات البصرية"""
        # تأثيرات الظلال والحدود
        # سيتم إضافة المزيد من التأثيرات لاحقاً
        pass
        
    def load_dashboard_section(self):
        """تحميل قسم لوحة المعلومات"""
        # مسح المحتوى السابق
        for widget in self.inner_content.winfo_children():
            widget.destroy()
            
        # إنشاء بطاقات المعلومات
        self.create_info_cards()
        
        # إنشاء الرسوم البيانية
        self.create_charts_section()
        
        # إنشاء قائمة الأنشطة الأخيرة
        self.create_recent_activities()
        
    def create_info_cards(self):
        """إنشاء بطاقات المعلومات"""
        cards_frame = tk.Frame(self.inner_content, bg=self.colors['primary'])
        cards_frame.pack(fill=tk.X, pady=10)
        
        # بيانات البطاقات
        cards_data = [
            {'title': 'حالة السيرفر', 'value': 'غير متصل', 'icon': '🔴', 'color': self.colors['error']},
            {'title': 'حالة GFX', 'value': 'متوقف', 'icon': '⏹️', 'color': self.colors['warning']},
            {'title': 'الأجهزة المتصلة', 'value': '0', 'icon': '📱', 'color': self.colors['info']},
            {'title': 'وقت التشغيل', 'value': '00:00:00', 'icon': '⏱️', 'color': self.colors['success']}
        ]
        
        # إنشاء البطاقات
        for i, card in enumerate(cards_data):
            self.create_info_card(cards_frame, card, i)
            
    def create_info_card(self, parent, card_data, index):
        """إنشاء بطاقة معلومات واحدة"""
        # إطار البطاقة
        card_frame = tk.Frame(parent, 
                             bg=self.colors['secondary'],
                             relief='raised',
                             bd=1)
        card_frame.grid(row=0, column=index, padx=10, pady=5, sticky='ew')
        
        # تكوين الأعمدة
        parent.grid_columnconfigure(index, weight=1)
        
        # أيقونة البطاقة
        icon_label = tk.Label(card_frame,
                             text=card_data['icon'],
                             font=('Segoe UI', 24),
                             bg=self.colors['secondary'],
                             fg=card_data['color'])
        icon_label.pack(pady=10)
        
        # عنوان البطاقة
        title_label = tk.Label(card_frame,
                              text=card_data['title'],
                              font=self.fonts['normal'],
                              bg=self.colors['secondary'],
                              fg=self.colors['text'])
        title_label.pack()
        
        # قيمة البطاقة
        value_label = tk.Label(card_frame,
                              text=card_data['value'],
                              font=self.fonts['heading'],
                              bg=self.colors['secondary'],
                              fg=card_data['color'])
        value_label.pack(pady=5)
        
    def create_charts_section(self):
        """إنشاء قسم الرسوم البيانية"""
        charts_frame = tk.Frame(self.inner_content, bg=self.colors['primary'])
        charts_frame.pack(fill=tk.X, pady=20)
        
        # عنوان القسم
        charts_title = tk.Label(charts_frame,
                               text="📈 الرسوم البيانية والإحصائيات",
                               font=self.fonts['heading'],
                               bg=self.colors['primary'],
                               fg=self.colors['text'])
        charts_title.pack(anchor='w', pady=10)
        
        # منطقة الرسوم البيانية
        chart_area = tk.Frame(charts_frame, 
                             bg=self.colors['secondary'],
                             height=200)
        chart_area.pack(fill=tk.X, pady=10)
        chart_area.pack_propagate(False)
        
        # رسالة مؤقتة
        chart_placeholder = tk.Label(chart_area,
                                    text="📊 سيتم إضافة الرسوم البيانية قريباً",
                                    font=self.fonts['normal'],
                                    bg=self.colors['secondary'],
                                    fg=self.colors['text'])
        chart_placeholder.pack(expand=True)
        
    def create_recent_activities(self):
        """إنشاء قائمة الأنشطة الأخيرة"""
        activities_frame = tk.Frame(self.inner_content, bg=self.colors['primary'])
        activities_frame.pack(fill=tk.X, pady=20)
        
        # عنوان القسم
        activities_title = tk.Label(activities_frame,
                                   text="📋 الأنشطة الأخيرة",
                                   font=self.fonts['heading'],
                                   bg=self.colors['primary'],
                                   fg=self.colors['text'])
        activities_title.pack(anchor='w', pady=10)
        
        # قائمة الأنشطة
        activities_list = tk.Frame(activities_frame, bg=self.colors['secondary'])
        activities_list.pack(fill=tk.X, pady=10)
        
        # أنشطة تجريبية
        sample_activities = [
            "🚀 تم تشغيل أداة التحكم الاحترافية",
            "📊 تم تحميل لوحة المعلومات",
            "⚙️ تم إعداد النظام الأساسي",
            "🎨 تم تطبيق السمة الاحترافية"
        ]
        
        for activity in sample_activities:
            activity_label = tk.Label(activities_list,
                                     text=f"• {activity}",
                                     font=self.fonts['small'],
                                     bg=self.colors['secondary'],
                                     fg=self.colors['text'],
                                     anchor='w')
            activity_label.pack(fill=tk.X, padx=10, pady=2)
            
    # ===== وظائف التحكم في النافذة =====
    
    def minimize_window(self):
        """تصغير النافذة"""
        self.root.iconify()
        
    def toggle_maximize(self):
        """تبديل تكبير النافذة"""
        if self.root.state() == 'zoomed':
            self.root.state('normal')
            self.maximize_btn.config(text="🗖")
        else:
            self.root.state('zoomed')
            self.maximize_btn.config(text="🗗")
            
    def toggle_sidebar(self):
        """تبديل عرض الشريط الجانبي"""
        if self.sidebar_expanded:
            self.sidebar_frame.pack_forget()
            self.sidebar_expanded = False
        else:
            self.sidebar_frame.pack(side=tk.LEFT, fill=tk.Y)
            self.sidebar_expanded = True
            
    def switch_section(self, section_name):
        """التبديل بين الأقسام"""
        # تحديث الزر النشط
        for name, btn in self.section_buttons.items():
            if name == section_name:
                btn.config(bg=self.colors['selected'])
            else:
                btn.config(bg=self.colors['secondary'])
                
        # تحديث القسم الحالي
        self.current_section = section_name
        
        # تحميل محتوى القسم
        self.load_section_content(section_name)
        
    def load_section_content(self, section_name):
        """تحميل محتوى القسم المحدد"""
        # تحديث عنوان القسم
        section_titles = {
            'dashboard': '📊 لوحة المعلومات',
            'connection': '🔗 إدارة الاتصال',
            'server_control': '🛠️ التحكم في السيرفر',
            'gfx_management': '🎮 إدارة GFX',
            'file_management': '📁 إدارة الملفات',
            'monitoring': '📈 المراقبة والأداء',
            'security': '🔒 الأمان والحماية',
            'backup': '💾 النسخ الاحتياطية',
            'reports': '📋 التقارير والإحصائيات',
            'settings': '⚙️ الإعدادات'
        }
        
        self.section_title.config(text=section_titles.get(section_name, section_name))
        
        # تحميل محتوى القسم المناسب
        if section_name == 'dashboard':
            self.load_dashboard_section()
        else:
            self.load_placeholder_section(section_name)
            
    def load_placeholder_section(self, section_name):
        """تحميل قسم مؤقت"""
        # مسح المحتوى السابق
        for widget in self.inner_content.winfo_children():
            widget.destroy()
            
        # رسالة مؤقتة
        placeholder = tk.Label(self.inner_content,
                              text=f"🚧 قسم {section_name} قيد التطوير\nسيتم إضافة المحتوى قريباً",
                              font=self.fonts['heading'],
                              bg=self.colors['primary'],
                              fg=self.colors['text'],
                              justify=tk.CENTER)
        placeholder.pack(expand=True, pady=50)
        
    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_info.config(text=f"🕐 {current_time}")
        
        # جدولة التحديث التالي
        self.root.after(1000, self.update_time)
        
    def update_connection_status(self, connected, server_url=""):
        """تحديث حالة الاتصال"""
        if connected:
            self.connection_status.config(text=f"✅ متصل - {server_url}",
                                         fg=self.colors['success'])
        else:
            self.connection_status.config(text="❌ غير متصل",
                                         fg=self.colors['error'])
                                         
    def show_notification(self, title, message, type="info"):
        """عرض إشعار"""
        if type == "error":
            messagebox.showerror(title, message)
        elif type == "warning":
            messagebox.showwarning(title, message)
        else:
            messagebox.showinfo(title, message)
