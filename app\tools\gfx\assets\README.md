# 🎨 مجلد Assets - ملفات GFX

## 📁 محتويات المجلد:
هذا المجلد يحتوي على جميع الملفات المطلوبة لأداة GFX:

### 🖼️ الصور والأيقونات:
- `app_icon.png` - أيقونة التطبيق
- `app_logo.png` - شعار التطبيق
- `pubg_icon.png` - أيقونة PUBG Mobile
- `tray_icon.ico` - أيقونة شريط المهام

### 🎨 صور أنماط الرسومات:
- `classic.png` - نمط Classic
- `colorful.png` - نمط Colorful
- `realistic.png` - نمط Realistic
- `soft.png` - نمط Soft
- `movie.png` - نمط Movie

## 📋 كيفية إضافة الملفات:
1. انسخ جميع الملفات من `GFX/assets/`
2. ضعها في هذا المجلد: `app/tools/gfx/assets/`
3. تأكد من أن أسماء الملفات صحيحة

## 🔧 الاستخدام:
هذه الملفات تستخدم بواسطة:
- برنامج GFX الرئيسي
- واجهة المستخدم PyQt5
- عرض الأنماط والخيارات

## ⚠️ ملاحظة:
جميع الملفات في هذا المجلد ضرورية لعمل البرنامج بشكل صحيح.
