#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار أداة التحكم في السيرفر
يختبر جميع وظائف الأداة بدون واجهة المستخدم
"""

import requests
import json
import time
from datetime import datetime

class ServerControllerTester:
    def __init__(self):
        """تهيئة اختبار الأداة"""
        self.server_url = "http://localhost:3000"
        self.auth_token = None
        self.test_results = []
        
    def log_test(self, test_name, success, message=""):
        """تسجيل نتيجة الاختبار"""
        result = {
            'test': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        self.test_results.append(result)
        
        status = "✅ نجح" if success else "❌ فشل"
        print(f"{status} - {test_name}: {message}")
        
    def get_headers(self):
        """الحصول على headers للطلبات"""
        if not self.auth_token:
            raise Exception("غير متصل بالسيرفر")
            
        return {
            'Authorization': f'Bearer {self.auth_token}',
            'Content-Type': 'application/json'
        }
        
    def test_server_connection(self):
        """اختبار الاتصال بالسيرفر"""
        try:
            response = requests.get(f"{self.server_url}/", timeout=5)
            if response.status_code == 200:
                self.log_test("اتصال السيرفر", True, "السيرفر متاح ويعمل")
                return True
            else:
                self.log_test("اتصال السيرفر", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("اتصال السيرفر", False, str(e))
            return False
            
    def test_login(self):
        """اختبار تسجيل الدخول"""
        try:
            login_data = {
                "email": "<EMAIL>",
                "password": "admin123"
            }
            
            response = requests.post(
                f"{self.server_url}/api/auth/login",
                json=login_data,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.auth_token = data.get('token')
                    self.log_test("تسجيل الدخول", True, "تم تسجيل الدخول بنجاح")
                    return True
                else:
                    self.log_test("تسجيل الدخول", False, data.get('message', 'فشل تسجيل الدخول'))
                    return False
            else:
                self.log_test("تسجيل الدخول", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("تسجيل الدخول", False, str(e))
            return False
            
    def test_tool_status(self):
        """اختبار حالة الأداة"""
        try:
            response = requests.get(
                f"{self.server_url}/api/tool/status",
                headers=self.get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    tool = data.get('tool', {})
                    self.log_test("حالة الأداة", True, f"الحالة: {tool.get('status')}")
                    return True
                else:
                    self.log_test("حالة الأداة", False, data.get('message'))
                    return False
            else:
                self.log_test("حالة الأداة", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("حالة الأداة", False, str(e))
            return False
            
    def test_gfx_status(self):
        """اختبار حالة GFX"""
        try:
            response = requests.get(
                f"{self.server_url}/api/tool/gfx/status",
                headers=self.get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    status = data.get('status', 'غير محدد')
                    process_id = data.get('processId', 'غير محدد')
                    self.log_test("حالة GFX", True, f"الحالة: {status}, العملية: {process_id}")
                    return True
                else:
                    self.log_test("حالة GFX", False, data.get('message'))
                    return False
            else:
                self.log_test("حالة GFX", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("حالة GFX", False, str(e))
            return False
            
    def test_devices_list(self):
        """اختبار قائمة الأجهزة"""
        try:
            response = requests.get(
                f"{self.server_url}/api/tool/adb/devices",
                headers=self.get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    devices = data.get('devices', [])
                    self.log_test("قائمة الأجهزة", True, f"عدد الأجهزة: {len(devices)}")
                    return True
                else:
                    self.log_test("قائمة الأجهزة", False, data.get('message'))
                    return False
            else:
                self.log_test("قائمة الأجهزة", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("قائمة الأجهزة", False, str(e))
            return False
            
    def test_gfx_start_stop(self):
        """اختبار تشغيل وإيقاف GFX"""
        try:
            # محاولة تشغيل GFX
            response = requests.post(
                f"{self.server_url}/api/tool/gfx/start",
                headers=self.get_headers(),
                timeout=15
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_test("تشغيل GFX", True, f"معرف العملية: {data.get('processId')}")
                    
                    # انتظار قليل
                    time.sleep(2)
                    
                    # محاولة إيقاف GFX
                    response = requests.post(
                        f"{self.server_url}/api/tool/gfx/stop",
                        headers=self.get_headers(),
                        timeout=15
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        if data.get('success'):
                            self.log_test("إيقاف GFX", True, "تم إيقاف GFX بنجاح")
                            return True
                        else:
                            self.log_test("إيقاف GFX", False, data.get('message'))
                            return False
                    else:
                        self.log_test("إيقاف GFX", False, f"HTTP {response.status_code}")
                        return False
                else:
                    self.log_test("تشغيل GFX", False, data.get('message'))
                    return False
            else:
                self.log_test("تشغيل GFX", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("تشغيل/إيقاف GFX", False, str(e))
            return False
            
    def test_file_update(self):
        """اختبار تحديث الملفات"""
        try:
            # محتوى تجريبي للتحديث
            test_content = f"""# ملف تجريبي محدث
# تاريخ التحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

print("هذا ملف تجريبي تم تحديثه من أداة التحكم")
"""
            
            response = requests.put(
                f"{self.server_url}/api/tool/gfx/files",
                headers=self.get_headers(),
                json={
                    "fileId": 6,  # README.md
                    "fileContent": test_content
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    file_info = data.get('file', {})
                    self.log_test("تحديث الملف", True, f"الملف: {file_info.get('name')}, الحجم: {file_info.get('size')} بايت")
                    return True
                else:
                    self.log_test("تحديث الملف", False, data.get('message'))
                    return False
            else:
                self.log_test("تحديث الملف", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("تحديث الملف", False, str(e))
            return False
            
    def test_monitoring_data(self):
        """اختبار جمع بيانات المراقبة"""
        try:
            # جمع بيانات من عدة endpoints
            monitoring_data = {}
            
            # حالة الأداة
            response = requests.get(
                f"{self.server_url}/api/tool/status",
                headers=self.get_headers(),
                timeout=5
            )
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    monitoring_data['tool_status'] = data.get('tool', {}).get('status')
                    
            # حالة GFX
            response = requests.get(
                f"{self.server_url}/api/tool/gfx/status",
                headers=self.get_headers(),
                timeout=5
            )
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    monitoring_data['gfx_status'] = data.get('status')
                    
            # الأجهزة
            response = requests.get(
                f"{self.server_url}/api/tool/adb/devices",
                headers=self.get_headers(),
                timeout=5
            )
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    monitoring_data['devices_count'] = len(data.get('devices', []))
                    
            self.log_test("بيانات المراقبة", True, f"البيانات: {monitoring_data}")
            return True
            
        except Exception as e:
            self.log_test("بيانات المراقبة", False, str(e))
            return False
            
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🧪 بدء اختبار أداة التحكم في السيرفر...\n")
        
        tests = [
            ("اختبار الاتصال بالسيرفر", self.test_server_connection),
            ("اختبار تسجيل الدخول", self.test_login),
            ("اختبار حالة الأداة", self.test_tool_status),
            ("اختبار حالة GFX", self.test_gfx_status),
            ("اختبار قائمة الأجهزة", self.test_devices_list),
            ("اختبار تشغيل/إيقاف GFX", self.test_gfx_start_stop),
            ("اختبار تحديث الملفات", self.test_file_update),
            ("اختبار بيانات المراقبة", self.test_monitoring_data)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            print(f"\n🔄 تشغيل: {test_name}")
            try:
                if test_func():
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                self.log_test(test_name, False, f"خطأ في التنفيذ: {str(e)}")
                failed += 1
                
            time.sleep(1)  # انتظار قصير بين الاختبارات
            
        # عرض النتائج النهائية
        print(f"\n{'='*50}")
        print(f"📊 نتائج الاختبار:")
        print(f"✅ نجح: {passed}")
        print(f"❌ فشل: {failed}")
        print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
        print(f"{'='*50}")
        
        # حفظ النتائج في ملف
        self.save_test_results()
        
        return passed, failed
        
    def save_test_results(self):
        """حفظ نتائج الاختبار في ملف"""
        try:
            results_file = f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            test_summary = {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'server_url': self.server_url,
                'total_tests': len(self.test_results),
                'passed': len([r for r in self.test_results if r['success']]),
                'failed': len([r for r in self.test_results if not r['success']]),
                'results': self.test_results
            }
            
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(test_summary, f, ensure_ascii=False, indent=2)
                
            print(f"\n💾 تم حفظ النتائج في: {results_file}")
            
        except Exception as e:
            print(f"❌ فشل حفظ النتائج: {str(e)}")

def main():
    """الدالة الرئيسية"""
    print("🎮 اختبار أداة التحكم في السيرفر")
    print("=" * 50)
    
    tester = ServerControllerTester()
    
    try:
        passed, failed = tester.run_all_tests()
        
        if failed == 0:
            print("\n🎉 جميع الاختبارات نجحت! الأداة جاهزة للاستخدام.")
        else:
            print(f"\n⚠️ فشل {failed} اختبار. يرجى مراجعة السجلات.")
            
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الاختبارات: {str(e)}")

if __name__ == "__main__":
    main()
