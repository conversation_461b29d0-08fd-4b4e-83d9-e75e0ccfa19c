# 🎮 أداة التحكم الاحترافية في السيرفر v3.0
## Server Controller Professional Edition

[![Version](https://img.shields.io/badge/version-3.0.0-blue.svg)](https://github.com/jogametool/server-controller)
[![Python](https://img.shields.io/badge/python-3.8+-green.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-orange.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/platform-Windows%20%7C%20Linux%20%7C%20macOS-lightgrey.svg)](https://github.com/jogametool/server-controller)

---

## 📋 نظرة عامة

أداة التحكم الاحترافية في السيرفر هي حل شامل ومتقدم للتحكم في السيرفر وإدارة برنامج GFX عن بُعد. تم تطويرها بتقنيات احترافية لتوفير تجربة مستخدم متميزة مع أعلى مستويات الأمان والأداء.

### 🌟 **المميزات الاحترافية الجديدة:**

#### 🎨 **واجهة مستخدم احترافية:**
- تصميم حديث ومتطور مع سمة احترافية داكنة
- واجهة مقسمة لأقسام متخصصة (10 أقسام رئيسية)
- شريط جانبي قابل للطي والتوسيع
- شريط عنوان مخصص مع أزرار تحكم
- تأثيرات بصرية متقدمة وانتقالات سلسة

#### 💾 **نظام قاعدة بيانات متطور:**
- قاعدة بيانات SQLite محلية مع 10 جداول متخصصة
- إدارة المستخدمين والجلسات
- تسجيل مفصل لجميع الأنشطة
- نظام إعدادات متقدم
- فهرسة محسنة للأداء العالي

#### 🔒 **نظام أمان متكامل:**
- تشفير البيانات باستخدام Fernet (AES 128)
- نظام مصادقة متعدد المستويات
- حماية من هجمات Brute Force
- إدارة الجلسات الآمنة
- مراقبة الأنشطة المشبوهة
- نظام حظر عناوين IP التلقائي

#### 📈 **مراقبة الأداء المتقدمة:**
- مراقبة استخدام المعالج والذاكرة في الوقت الفعلي
- قياس زمن استجابة الشبكة
- مراقبة حالة السيرفر وGFX
- تنبيهات ذكية للمشاكل
- رسوم بيانية للأداء (قريباً)

#### 💾 **نظام نسخ احتياطية ذكي:**
- نسخ احتياطية تلقائية مجدولة
- ضغط الملفات وحساب Checksum
- تنظيف النسخ القديمة تلقائياً
- استعادة سريعة وآمنة
- تشفير النسخ الاحتياطية (اختياري)

#### 🔔 **نظام إشعارات متطور:**
- إشعارات فورية للأحداث المهمة
- تصنيف الإشعارات حسب الأولوية
- حفظ سجل الإشعارات
- إشعارات سطح المكتب (قريباً)

---

## 🚀 التثبيت والتشغيل

### 📋 **المتطلبات:**

#### **متطلبات النظام:**
- نظام التشغيل: Windows 10/11, Linux, macOS
- Python 3.8 أو أحدث
- ذاكرة: 512 MB RAM كحد أدنى
- مساحة القرص: 100 MB مساحة فارغة

#### **المكتبات المطلوبة:**
```bash
# المكتبات الأساسية
requests>=2.28.0
psutil>=5.9.0
cryptography>=3.4.8

# مكتبات مدمجة (لا تحتاج تثبيت)
tkinter, sqlite3, threading, json, hashlib, etc.
```

### 🔧 **خطوات التثبيت:**

#### **1. تحميل الأداة:**
```bash
# من السيرفر مباشرة
http://localhost:3000/download

# أو استنساخ المستودع
git clone https://github.com/jogametool/server-controller-pro.git
cd server-controller-pro
```

#### **2. تثبيت المتطلبات:**
```bash
# تثبيت تلقائي
pip install -r requirements_pro.txt

# أو تثبيت يدوي
pip install requests psutil cryptography
```

#### **3. تشغيل الأداة:**

**على Windows:**
```cmd
# تشغيل مباشر
python server_controller_pro.py

# أو استخدام ملف التشغيل
run_pro.bat
```

**على Linux/macOS:**
```bash
# تشغيل مباشر
python3 server_controller_pro.py

# أو إعطاء صلاحيات وتشغيل
chmod +x run_pro.sh
./run_pro.sh
```

---

## 🎛️ دليل الاستخدام

### 📊 **الأقسام الرئيسية:**

#### **1. 📊 لوحة المعلومات:**
- نظرة عامة على حالة النظام
- بطاقات معلومات تفاعلية
- رسوم بيانية للأداء
- قائمة الأنشطة الأخيرة

#### **2. 🔗 إدارة الاتصال:**
- إعدادات الاتصال بالسيرفر
- اختبار الاتصال
- إدارة بيانات تسجيل الدخول
- سجل الاتصالات

#### **3. 🛠️ التحكم في السيرفر:**
- تشغيل/إيقاف الخدمات
- مراقبة حالة السيرفر
- إدارة العمليات
- سجل العمليات

#### **4. 🎮 إدارة GFX:**
- تشغيل/إيقاف برنامج GFX
- إدارة الأجهزة المتصلة
- مراقبة الأداء
- إعدادات GFX المتقدمة

#### **5. 📁 إدارة الملفات:**
- رفع وتحديث الملفات
- إدارة النسخ
- مراقبة أحجام الملفات
- سجل التحديثات

#### **6. 📈 المراقبة والأداء:**
- مراقبة الأداء المباشر
- إعدادات المراقبة
- تنبيهات الأداء
- تقارير الأداء

#### **7. 🔒 الأمان والحماية:**
- إدارة المستخدمين
- إعدادات الأمان
- سجل الأنشطة الأمنية
- إدارة الجلسات

#### **8. 💾 النسخ الاحتياطية:**
- إنشاء نسخ احتياطية
- جدولة النسخ التلقائية
- استعادة النسخ
- إدارة النسخ القديمة

#### **9. 📋 التقارير والإحصائيات:**
- تقارير الأداء
- إحصائيات الاستخدام
- تقارير الأمان
- تصدير التقارير

#### **10. ⚙️ الإعدادات:**
- إعدادات الواجهة
- إعدادات النظام
- إعدادات الأمان
- إعدادات المراقبة

### 🔧 **العمليات الأساسية:**

#### **الاتصال بالسيرفر:**
1. افتح قسم "🔗 إدارة الاتصال"
2. أدخل عنوان السيرفر: `http://localhost:3000`
3. أدخل بيانات تسجيل الدخول
4. اضغط "اتصال"

#### **تشغيل GFX:**
1. انتقل لقسم "🎮 إدارة GFX"
2. اضغط "▶️ تشغيل GFX"
3. راقب حالة التشغيل
4. تحقق من الأجهزة المتصلة

#### **إنشاء نسخة احتياطية:**
1. افتح قسم "💾 النسخ الاحتياطية"
2. اضغط "إنشاء نسخة احتياطية"
3. اختر نوع النسخة
4. انتظر اكتمال العملية

---

## 🔧 الإعدادات المتقدمة

### 🔒 **إعدادات الأمان:**
```json
{
  "session_timeout": 3600,
  "max_login_attempts": 3,
  "password_encryption": true,
  "auto_logout": true,
  "secure_storage": true
}
```

### 📊 **إعدادات المراقبة:**
```json
{
  "monitoring_interval": 5,
  "auto_monitoring": true,
  "performance_alerts": true,
  "alert_thresholds": {
    "cpu_usage": 90,
    "memory_usage": 90,
    "disk_usage": 95
  }
}
```

### 💾 **إعدادات النسخ الاحتياطية:**
```json
{
  "auto_backup": true,
  "backup_interval": 24,
  "keep_backups": 30,
  "compress_backups": true,
  "encrypt_backups": false
}
```

---

## 📊 هيكل المشروع

```
server-controller-pro/
├── 📄 server_controller_pro.py      # الملف الرئيسي
├── 📁 modules/                      # الوحدات المتخصصة
│   ├── 🎨 ui_manager.py            # مدير الواجهة
│   ├── 💾 database_manager.py      # مدير قاعدة البيانات
│   ├── 🔒 security_manager.py      # مدير الأمان
│   ├── 📈 monitoring_manager.py    # مدير المراقبة
│   ├── 💾 backup_manager.py        # مدير النسخ الاحتياطية
│   ├── 🔔 notification_manager.py  # مدير الإشعارات
│   └── 📄 __init__.py              # ملف الحزمة
├── 📁 data/                        # بيانات الأداة
│   ├── 💾 server_controller_pro.db # قاعدة البيانات
│   ├── 🔑 encryption.key          # مفتاح التشفير
│   └── ⚙️ settings.json           # الإعدادات
├── 📁 logs/                        # ملفات السجلات
├── 📁 backups/                     # النسخ الاحتياطية
├── 📁 themes/                      # السمات
├── 📁 languages/                   # ملفات اللغات
├── 📄 requirements_pro.txt         # المتطلبات
├── 🚀 run_pro.bat                  # ملف التشغيل
└── 📖 README_PRO.md               # هذا الملف
```

---

## 🔍 استكشاف الأخطاء

### ❌ **مشاكل شائعة وحلولها:**

#### **خطأ في الاتصال:**
```
❌ المشكلة: فشل الاتصال بالسيرفر
✅ الحل: 
   1. تأكد من تشغيل السيرفر على http://localhost:3000
   2. تحقق من إعدادات الشبكة
   3. تأكد من صحة بيانات تسجيل الدخول
```

#### **خطأ في قاعدة البيانات:**
```
❌ المشكلة: فشل الوصول لقاعدة البيانات
✅ الحل:
   1. تأكد من وجود مجلد data/
   2. تحقق من صلاحيات الكتابة
   3. أعد تشغيل الأداة
```

#### **خطأ في المكتبات:**
```
❌ المشكلة: مكتبة غير موجودة
✅ الحل:
   1. pip install -r requirements_pro.txt
   2. تحديث pip: pip install --upgrade pip
   3. استخدام بيئة افتراضية
```

### 🔧 **أدوات التشخيص:**

#### **فحص النظام:**
```bash
# فحص إصدار Python
python --version

# فحص المكتبات المثبتة
pip list

# فحص المتطلبات
pip check
```

#### **فحص الملفات:**
```bash
# فحص هيكل المجلدات
ls -la

# فحص صلاحيات الملفات
ls -l data/

# فحص حجم قاعدة البيانات
du -h data/server_controller_pro.db
```

---

## 🔄 التحديثات والصيانة

### 📥 **تحديث الأداة:**
1. تحميل الإصدار الجديد
2. نسخ احتياطية للبيانات الحالية
3. استبدال الملفات القديمة
4. تشغيل الأداة والتحقق من التوافق

### 🧹 **صيانة دورية:**
- تنظيف السجلات القديمة
- تحسين قاعدة البيانات
- فحص النسخ الاحتياطية
- تحديث المكتبات

---

## 📞 الدعم والمساعدة

### 🆘 **طرق الحصول على المساعدة:**

#### **الدعم الفني:**
- 📧 البريد الإلكتروني: <EMAIL>
- 💬 الدردشة المباشرة: متوفرة في الأداة
- 📱 تليجرام: @JOGameToolSupport

#### **الموارد:**
- 📖 الوثائق الكاملة: docs.jogametool.com
- 🎥 فيديوهات تعليمية: youtube.com/jogametool
- 💬 منتدى المجتمع: forum.jogametool.com

#### **الإبلاغ عن الأخطاء:**
- 🐛 GitHub Issues: github.com/jogametool/server-controller/issues
- 📧 البريد المباشر: <EMAIL>

---

## 📄 الترخيص والحقوق

### 📜 **معلومات الترخيص:**
- **الترخيص:** MIT License
- **حقوق الطبع:** © 2024 JO GAME TOOL Team
- **الاستخدام:** مجاني للاستخدام الشخصي والتجاري

### ⚖️ **شروط الاستخدام:**
- يُسمح بالتعديل والتوزيع
- يجب الاحتفاظ بحقوق الطبع
- لا توجد ضمانات صريحة أو ضمنية
- المطور غير مسؤول عن أي أضرار

---

## 🎉 شكر وتقدير

### 👥 **فريق التطوير:**
- **المطور الرئيسي:** JO GAME TOOL Team
- **مطوري الواجهة:** UI/UX Specialists
- **مطوري الأمان:** Security Engineers
- **مختبري الجودة:** QA Testers

### 🙏 **شكر خاص:**
- مجتمع Python للمكتبات الرائعة
- مستخدمي الإصدارات السابقة للملاحظات القيمة
- المساهمين في تطوير الأداة

---

## 🔮 خطط المستقبل

### 🚀 **الميزات القادمة:**
- [ ] واجهة ويب تفاعلية
- [ ] دعم قواعد بيانات خارجية
- [ ] API متقدم للتكامل
- [ ] تطبيق موبايل مصاحب
- [ ] ذكاء اصطناعي للتحليل
- [ ] دعم السحابة الإلكترونية

### 📈 **تحسينات مخططة:**
- [ ] أداء أسرع وذاكرة أقل
- [ ] واجهة أكثر تفاعلية
- [ ] دعم لغات إضافية
- [ ] ميزات أمان متقدمة
- [ ] تقارير أكثر تفصيلاً
- [ ] تكامل مع أدوات أخرى

---

**🎮 أداة التحكم الاحترافية في السيرفر v3.0 - حيث الاحترافية تلتقي بالبساطة! 🚀**
