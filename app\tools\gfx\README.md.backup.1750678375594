# 🎮 GFX Tool - PUBG Mobile Graphics Optimizer

## 📋 نظرة عامة
أداة تحسين رسومات PUBG Mobile المدمجة في السيرفر.

## 🕒 آخر تحديث: ٢٣‏/٦‏/٢٠٢٥، ٢:٢١:٥٩ م

## 📁 محتويات المجلد:
- `GFX.py` - الكود الأساسي للأداة
- `GUI.py` - واجهة المستخدم PyQt5
- `JO_GAME_TOOL.exe` - الملف التنفيذي
- `assets/` - الصور والأيقونات
- `platform-tools/` - أدوات ADB
- `build/` - ملفات البناء

## 🔧 إدارة الأداة:
يمكن إدارة هذه الأداة من خلال:
- تشغيل/إيقاف البرنامج
- تحديث الملفات
- مراقبة الحالة
- إدارة الإعدادات

## 🌐 API Endpoints:
- `POST /api/tool/gfx/start` - تشغيل الأداة
- `POST /api/tool/gfx/stop` - إيقاف الأداة
- `POST /api/tool/gfx/restart` - إعادة تشغيل الأداة
- `GET /api/tool/gfx/status` - حالة الأداة
- `PUT /api/tool/gfx/files` - تحديث الملفات

## ✅ تم اختبار النظام بنجاح!
