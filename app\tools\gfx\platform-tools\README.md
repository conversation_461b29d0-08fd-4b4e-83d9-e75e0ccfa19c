# 🔧 مجلد Platform Tools - أدوات ADB

## 📁 محتويات المجلد:
هذا المجلد يحتوي على أدوات Android Debug Bridge (ADB) المطلوبة:

### 🛠️ الملفات الأساسية:
- `adb.exe` - أداة ADB الرئيسية
- `AdbWinApi.dll` - مكتبة Windows API
- `AdbWinUsbApi.dll` - مكتبة USB API
- `fastboot.exe` - أداة Fastboot

### 📦 أدوات إضافية:
- `etc1tool.exe` - أداة ضغط الصور
- `hprof-conv.exe` - محول ملفات HPROF
- `make_f2fs.exe` - أداة إنشاء نظام ملفات F2FS
- `mke2fs.exe` - أداة إنشاء نظام ملفات EXT
- `sqlite3.exe` - أداة قاعدة بيانات SQLite

### 🔗 مكتبات النظام:
- `libwinpthread-1.dll` - مكتبة pthread
- `mke2fs.conf` - ملف تكوين mke2fs

## 📋 كيفية إضافة الملفات:
1. حمل Android Platform Tools من:
   https://developer.android.com/studio/releases/platform-tools
2. استخرج الملفات
3. انسخ جميع الملفات إلى هذا المجلد: `app/tools/gfx/platform-tools/`

## 🔧 الاستخدام:
هذه الأدوات تستخدم لـ:
- الاتصال بالأجهزة والمحاكيات
- نقل الملفات من وإلى الجهاز
- تنفيذ أوامر النظام
- إدارة تطبيقات Android

## ⚠️ متطلبات النظام:
- Windows 7 أو أحدث
- USB drivers للجهاز المتصل
- تفعيل USB Debugging على الجهاز

## 🚀 اختبار التثبيت:
```bash
# اختبار ADB
./adb.exe version

# عرض الأجهزة المتصلة
./adb.exe devices
```
