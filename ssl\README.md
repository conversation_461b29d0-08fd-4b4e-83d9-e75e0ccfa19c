# مجلد شهادات SSL

ضع ملفات شهادات SSL الخاصة بك هنا للتشغيل في وضع الإنتاج.

المطلوب:
- `private.key`: مفتاح SSL الخاص
- `certificate.crt`: شهادة SSL

## كيفية إنشاء شهادات ذاتية التوقيع للاختبار

> ملاحظة: الشهادات ذاتية التوقيع مناسبة للاختبار فقط. للإنتاج، استخدم شهادات من هيئة إصدار شهادات موثوقة مثل Let's Encrypt.

```bash
# إنشاء مفتاح خاص
openssl genrsa -out private.key 2048

# إنشاء طلب توقيع شهادة (CSR)
openssl req -new -key private.key -out certificate.csr

# إنشاء شهادة ذاتية التوقيع صالحة لمدة 365 يومًا
openssl x509 -req -days 365 -in certificate.csr -signkey private.key -out certificate.crt
```

## للإنتاج

للإنتاج، يوصى باستخدام Let's Encrypt للحصول على شهادات SSL مجانية وموثوقة.
يمكنك استخدام Certbot للحصول على شهادات Let's Encrypt وتجديدها تلقائيًا.

مراجع:
- [Let's Encrypt](https://letsencrypt.org/)
- [Certbot](https://certbot.eff.org/)
