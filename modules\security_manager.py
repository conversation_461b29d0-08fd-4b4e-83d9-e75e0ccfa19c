#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔒 مدير الأمان الاحترافي - Professional Security Manager
مسؤول عن جميع جوانب الأمان والحماية في الأداة
"""

import hashlib
import secrets
import base64
import json
import time
import threading
from datetime import datetime, timedelta
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

class SecurityManager:
    """مدير الأمان الاحترافي"""
    
    def __init__(self, parent):
        """تهيئة مدير الأمان"""
        self.parent = parent
        self.encryption_key = None
        self.failed_attempts = {}
        self.blocked_ips = {}
        self.active_sessions = {}
        self.security_lock = threading.Lock()
        
        # إعداد نظام الأمان
        self.setup_security_system()
        
        self.parent.logger.info("🔒 تم تهيئة مدير الأمان الاحترافي")
        
    def setup_security_system(self):
        """إعداد نظام الأمان"""
        # إنشاء مفتاح التشفير
        self.generate_encryption_key()
        
        # تحميل قائمة الحظر
        self.load_blocked_ips()
        
        # بدء خدمات الأمان في الخلفية
        self.start_security_services()
        
    def generate_encryption_key(self):
        """توليد مفتاح التشفير"""
        try:
            # محاولة تحميل المفتاح الموجود
            key_file = "data/encryption.key"
            
            try:
                with open(key_file, 'rb') as f:
                    self.encryption_key = f.read()
                self.parent.logger.info("🔑 تم تحميل مفتاح التشفير الموجود")
            except FileNotFoundError:
                # إنشاء مفتاح جديد
                self.encryption_key = Fernet.generate_key()
                
                with open(key_file, 'wb') as f:
                    f.write(self.encryption_key)
                    
                self.parent.logger.info("🔑 تم إنشاء مفتاح تشفير جديد")
                
        except Exception as e:
            self.parent.logger.error(f"❌ فشل إعداد مفتاح التشفير: {str(e)}")
            # استخدام مفتاح افتراضي (غير آمن)
            self.encryption_key = Fernet.generate_key()
            
    def load_blocked_ips(self):
        """تحميل قائمة عناوين IP المحظورة"""
        try:
            blocked_file = "data/blocked_ips.json"
            
            try:
                with open(blocked_file, 'r', encoding='utf-8') as f:
                    self.blocked_ips = json.load(f)
                self.parent.logger.info(f"🚫 تم تحميل {len(self.blocked_ips)} عنوان IP محظور")
            except FileNotFoundError:
                self.blocked_ips = {}
                
        except Exception as e:
            self.parent.logger.error(f"❌ فشل تحميل قائمة الحظر: {str(e)}")
            self.blocked_ips = {}
            
    def save_blocked_ips(self):
        """حفظ قائمة عناوين IP المحظورة"""
        try:
            blocked_file = "data/blocked_ips.json"
            
            with open(blocked_file, 'w', encoding='utf-8') as f:
                json.dump(self.blocked_ips, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.parent.logger.error(f"❌ فشل حفظ قائمة الحظر: {str(e)}")
            
    def start_security_services(self):
        """بدء خدمات الأمان في الخلفية"""
        # خدمة تنظيف الجلسات المنتهية الصلاحية
        threading.Thread(target=self.session_cleanup_service, daemon=True).start()
        
        # خدمة مراقبة الأمان
        threading.Thread(target=self.security_monitoring_service, daemon=True).start()
        
        # خدمة إلغاء حظر عناوين IP
        threading.Thread(target=self.ip_unblock_service, daemon=True).start()
        
    def session_cleanup_service(self):
        """خدمة تنظيف الجلسات المنتهية الصلاحية"""
        while True:
            try:
                current_time = time.time()
                expired_sessions = []
                
                with self.security_lock:
                    for session_id, session_data in self.active_sessions.items():
                        if current_time > session_data['expires_at']:
                            expired_sessions.append(session_id)
                            
                    # إزالة الجلسات المنتهية الصلاحية
                    for session_id in expired_sessions:
                        del self.active_sessions[session_id]
                        self.parent.logger.info(f"🗑️ تم حذف جلسة منتهية الصلاحية: {session_id}")
                        
                # تنظيف كل 5 دقائق
                time.sleep(300)
                
            except Exception as e:
                self.parent.logger.error(f"❌ خطأ في خدمة تنظيف الجلسات: {str(e)}")
                time.sleep(60)
                
    def security_monitoring_service(self):
        """خدمة مراقبة الأمان"""
        while True:
            try:
                # مراقبة المحاولات المشبوهة
                self.monitor_suspicious_activities()
                
                # فحص سلامة النظام
                self.check_system_integrity()
                
                # مراقبة كل دقيقة
                time.sleep(60)
                
            except Exception as e:
                self.parent.logger.error(f"❌ خطأ في خدمة مراقبة الأمان: {str(e)}")
                time.sleep(60)
                
    def ip_unblock_service(self):
        """خدمة إلغاء حظر عناوين IP"""
        while True:
            try:
                current_time = time.time()
                unblock_ips = []
                
                with self.security_lock:
                    for ip, block_data in self.blocked_ips.items():
                        if current_time > block_data.get('unblock_at', 0):
                            unblock_ips.append(ip)
                            
                    # إلغاء حظر عناوين IP
                    for ip in unblock_ips:
                        del self.blocked_ips[ip]
                        self.parent.logger.info(f"🔓 تم إلغاء حظر عنوان IP: {ip}")
                        
                if unblock_ips:
                    self.save_blocked_ips()
                    
                # فحص كل 10 دقائق
                time.sleep(600)
                
            except Exception as e:
                self.parent.logger.error(f"❌ خطأ في خدمة إلغاء الحظر: {str(e)}")
                time.sleep(60)
                
    # ===== وظائف التشفير =====
    
    def encrypt_data(self, data):
        """تشفير البيانات"""
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
                
            fernet = Fernet(self.encryption_key)
            encrypted_data = fernet.encrypt(data)
            
            return base64.b64encode(encrypted_data).decode('utf-8')
            
        except Exception as e:
            self.parent.logger.error(f"❌ فشل تشفير البيانات: {str(e)}")
            return None
            
    def decrypt_data(self, encrypted_data):
        """فك تشفير البيانات"""
        try:
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
            
            fernet = Fernet(self.encryption_key)
            decrypted_data = fernet.decrypt(encrypted_bytes)
            
            return decrypted_data.decode('utf-8')
            
        except Exception as e:
            self.parent.logger.error(f"❌ فشل فك تشفير البيانات: {str(e)}")
            return None
            
    def hash_password(self, password, salt=None):
        """تشفير كلمة المرور"""
        if salt is None:
            salt = secrets.token_hex(32)
            
        # استخدام PBKDF2 مع SHA-256
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt.encode('utf-8'),
            iterations=100000,
        )
        
        password_hash = base64.b64encode(kdf.derive(password.encode('utf-8'))).decode('utf-8')
        
        return password_hash, salt
        
    def verify_password(self, password, stored_hash, salt):
        """التحقق من كلمة المرور"""
        try:
            computed_hash, _ = self.hash_password(password, salt)
            return computed_hash == stored_hash
        except Exception as e:
            self.parent.logger.error(f"❌ فشل التحقق من كلمة المرور: {str(e)}")
            return False
            
    # ===== وظائف إدارة الجلسات =====
    
    def create_session(self, user_id, user_data, ip_address=None):
        """إنشاء جلسة آمنة"""
        try:
            # توليد معرف جلسة آمن
            session_id = secrets.token_urlsafe(32)
            
            # تحديد وقت انتهاء الجلسة
            expires_at = time.time() + self.parent.security_config['session_timeout']
            
            # بيانات الجلسة
            session_data = {
                'user_id': user_id,
                'user_data': user_data,
                'created_at': time.time(),
                'expires_at': expires_at,
                'ip_address': ip_address,
                'last_activity': time.time(),
                'is_active': True
            }
            
            with self.security_lock:
                self.active_sessions[session_id] = session_data
                
            # تسجيل إنشاء الجلسة
            self.parent.db_manager.log_activity(
                user_id, "session_created", "security",
                f"تم إنشاء جلسة جديدة من IP: {ip_address}", "success", ip_address
            )
            
            return session_id
            
        except Exception as e:
            self.parent.logger.error(f"❌ فشل إنشاء الجلسة: {str(e)}")
            return None
            
    def validate_session(self, session_id, ip_address=None):
        """التحقق من صحة الجلسة"""
        try:
            with self.security_lock:
                session_data = self.active_sessions.get(session_id)
                
                if not session_data:
                    return None
                    
                # التحقق من انتهاء الصلاحية
                if time.time() > session_data['expires_at']:
                    del self.active_sessions[session_id]
                    return None
                    
                # التحقق من عنوان IP (اختياري)
                if ip_address and session_data.get('ip_address') != ip_address:
                    self.parent.logger.warning(f"⚠️ محاولة استخدام جلسة من IP مختلف: {ip_address}")
                    return None
                    
                # تحديث آخر نشاط
                session_data['last_activity'] = time.time()
                
                return session_data['user_data']
                
        except Exception as e:
            self.parent.logger.error(f"❌ فشل التحقق من الجلسة: {str(e)}")
            return None
            
    def invalidate_session(self, session_id):
        """إلغاء الجلسة"""
        try:
            with self.security_lock:
                if session_id in self.active_sessions:
                    user_id = self.active_sessions[session_id]['user_id']
                    del self.active_sessions[session_id]
                    
                    # تسجيل إلغاء الجلسة
                    self.parent.db_manager.log_activity(
                        user_id, "session_invalidated", "security",
                        "تم إلغاء الجلسة", "success"
                    )
                    
                    return True
                    
            return False
            
        except Exception as e:
            self.parent.logger.error(f"❌ فشل إلغاء الجلسة: {str(e)}")
            return False
            
    def extend_session(self, session_id):
        """تمديد الجلسة"""
        try:
            with self.security_lock:
                session_data = self.active_sessions.get(session_id)
                
                if session_data:
                    # تمديد وقت انتهاء الصلاحية
                    session_data['expires_at'] = time.time() + self.parent.security_config['session_timeout']
                    session_data['last_activity'] = time.time()
                    
                    return True
                    
            return False
            
        except Exception as e:
            self.parent.logger.error(f"❌ فشل تمديد الجلسة: {str(e)}")
            return False
            
    # ===== وظائف الحماية من الهجمات =====
    
    def check_login_attempts(self, ip_address, email):
        """فحص محاولات تسجيل الدخول"""
        try:
            current_time = time.time()
            max_attempts = self.parent.security_config['max_login_attempts']
            
            # التحقق من حظر IP
            if self.is_ip_blocked(ip_address):
                return False, "عنوان IP محظور"
                
            # فحص محاولات IP
            ip_key = f"ip_{ip_address}"
            if ip_key not in self.failed_attempts:
                self.failed_attempts[ip_key] = []
                
            # تنظيف المحاولات القديمة (آخر ساعة)
            self.failed_attempts[ip_key] = [
                attempt for attempt in self.failed_attempts[ip_key]
                if current_time - attempt < 3600
            ]
            
            # فحص عدد المحاولات
            if len(self.failed_attempts[ip_key]) >= max_attempts:
                self.block_ip(ip_address, "تجاوز الحد الأقصى لمحاولات تسجيل الدخول")
                return False, "تم تجاوز الحد الأقصى للمحاولات"
                
            return True, "مسموح"
            
        except Exception as e:
            self.parent.logger.error(f"❌ فشل فحص محاولات تسجيل الدخول: {str(e)}")
            return False, "خطأ في النظام"
            
    def record_failed_attempt(self, ip_address, email):
        """تسجيل محاولة فاشلة"""
        try:
            current_time = time.time()
            
            # تسجيل محاولة IP
            ip_key = f"ip_{ip_address}"
            if ip_key not in self.failed_attempts:
                self.failed_attempts[ip_key] = []
                
            self.failed_attempts[ip_key].append(current_time)
            
            # تسجيل محاولة البريد الإلكتروني
            email_key = f"email_{email}"
            if email_key not in self.failed_attempts:
                self.failed_attempts[email_key] = []
                
            self.failed_attempts[email_key].append(current_time)
            
            # تسجيل في قاعدة البيانات
            self.parent.db_manager.log_activity(
                None, "login_failed", "security",
                f"محاولة تسجيل دخول فاشلة - IP: {ip_address}, Email: {email}",
                "error", ip_address
            )
            
        except Exception as e:
            self.parent.logger.error(f"❌ فشل تسجيل المحاولة الفاشلة: {str(e)}")
            
    def is_ip_blocked(self, ip_address):
        """فحص ما إذا كان عنوان IP محظور"""
        with self.security_lock:
            return ip_address in self.blocked_ips
            
    def block_ip(self, ip_address, reason, duration_hours=24):
        """حظر عنوان IP"""
        try:
            current_time = time.time()
            unblock_at = current_time + (duration_hours * 3600)
            
            with self.security_lock:
                self.blocked_ips[ip_address] = {
                    'blocked_at': current_time,
                    'unblock_at': unblock_at,
                    'reason': reason,
                    'duration_hours': duration_hours
                }
                
            self.save_blocked_ips()
            
            # تسجيل الحظر
            self.parent.db_manager.log_activity(
                None, "ip_blocked", "security",
                f"تم حظر عنوان IP: {ip_address} - السبب: {reason}",
                "warning", ip_address
            )
            
            self.parent.logger.warning(f"🚫 تم حظر عنوان IP: {ip_address}")
            
        except Exception as e:
            self.parent.logger.error(f"❌ فشل حظر عنوان IP: {str(e)}")
            
    def unblock_ip(self, ip_address):
        """إلغاء حظر عنوان IP"""
        try:
            with self.security_lock:
                if ip_address in self.blocked_ips:
                    del self.blocked_ips[ip_address]
                    
            self.save_blocked_ips()
            
            # تسجيل إلغاء الحظر
            self.parent.db_manager.log_activity(
                None, "ip_unblocked", "security",
                f"تم إلغاء حظر عنوان IP: {ip_address}",
                "info", ip_address
            )
            
            self.parent.logger.info(f"🔓 تم إلغاء حظر عنوان IP: {ip_address}")
            
        except Exception as e:
            self.parent.logger.error(f"❌ فشل إلغاء حظر عنوان IP: {str(e)}")
            
    # ===== وظائف مراقبة الأمان =====
    
    def monitor_suspicious_activities(self):
        """مراقبة الأنشطة المشبوهة"""
        try:
            # فحص محاولات تسجيل الدخول المتكررة
            self.check_repeated_login_attempts()
            
            # فحص الجلسات المشبوهة
            self.check_suspicious_sessions()
            
            # فحص الأنشطة غير العادية
            self.check_unusual_activities()
            
        except Exception as e:
            self.parent.logger.error(f"❌ فشل مراقبة الأنشطة المشبوهة: {str(e)}")
            
    def check_repeated_login_attempts(self):
        """فحص محاولات تسجيل الدخول المتكررة"""
        # سيتم تنفيذها لاحقاً
        pass
        
    def check_suspicious_sessions(self):
        """فحص الجلسات المشبوهة"""
        # سيتم تنفيذها لاحقاً
        pass
        
    def check_unusual_activities(self):
        """فحص الأنشطة غير العادية"""
        # سيتم تنفيذها لاحقاً
        pass
        
    def check_system_integrity(self):
        """فحص سلامة النظام"""
        try:
            # فحص ملفات النظام المهمة
            critical_files = [
                "data/server_controller_pro.db",
                "data/encryption.key",
                "data/settings.json"
            ]
            
            for file_path in critical_files:
                if not self.verify_file_integrity(file_path):
                    self.parent.logger.error(f"⚠️ تم اكتشاف تغيير في ملف حساس: {file_path}")
                    
        except Exception as e:
            self.parent.logger.error(f"❌ فشل فحص سلامة النظام: {str(e)}")
            
    def verify_file_integrity(self, file_path):
        """التحقق من سلامة الملف"""
        try:
            # حساب hash للملف
            with open(file_path, 'rb') as f:
                file_hash = hashlib.sha256(f.read()).hexdigest()
                
            # مقارنة مع hash المحفوظ (سيتم تنفيذها لاحقاً)
            return True
            
        except FileNotFoundError:
            return False
        except Exception as e:
            self.parent.logger.error(f"❌ فشل التحقق من سلامة الملف {file_path}: {str(e)}")
            return False
            
    # ===== وظائف التدقيق =====
    
    def generate_security_report(self):
        """إنشاء تقرير أمان"""
        try:
            report = {
                'timestamp': datetime.now().isoformat(),
                'active_sessions': len(self.active_sessions),
                'blocked_ips': len(self.blocked_ips),
                'failed_attempts_summary': self.get_failed_attempts_summary(),
                'security_events': self.get_recent_security_events(),
                'system_status': 'secure'
            }
            
            return report
            
        except Exception as e:
            self.parent.logger.error(f"❌ فشل إنشاء تقرير الأمان: {str(e)}")
            return None
            
    def get_failed_attempts_summary(self):
        """الحصول على ملخص المحاولات الفاشلة"""
        current_time = time.time()
        summary = {
            'last_hour': 0,
            'last_24_hours': 0,
            'unique_ips': set(),
            'unique_emails': set()
        }
        
        for key, attempts in self.failed_attempts.items():
            recent_attempts = [a for a in attempts if current_time - a < 3600]  # آخر ساعة
            daily_attempts = [a for a in attempts if current_time - a < 86400]  # آخر 24 ساعة
            
            summary['last_hour'] += len(recent_attempts)
            summary['last_24_hours'] += len(daily_attempts)
            
            if key.startswith('ip_'):
                summary['unique_ips'].add(key[3:])
            elif key.startswith('email_'):
                summary['unique_emails'].add(key[6:])
                
        summary['unique_ips'] = len(summary['unique_ips'])
        summary['unique_emails'] = len(summary['unique_emails'])
        
        return summary
        
    def get_recent_security_events(self, hours=24):
        """الحصول على الأحداث الأمنية الأخيرة"""
        try:
            return self.parent.db_manager.get_recent_activities(
                limit=100, category='security'
            )
        except Exception as e:
            self.parent.logger.error(f"❌ فشل الحصول على الأحداث الأمنية: {str(e)}")
            return []
            
    # ===== وظائف الإغلاق =====
    
    def cleanup(self):
        """تنظيف موارد الأمان"""
        try:
            # حفظ قائمة الحظر
            self.save_blocked_ips()
            
            # إلغاء جميع الجلسات النشطة
            with self.security_lock:
                self.active_sessions.clear()
                
            self.parent.logger.info("🔒 تم تنظيف موارد الأمان")
            
        except Exception as e:
            self.parent.logger.error(f"❌ فشل تنظيف موارد الأمان: {str(e)}")
