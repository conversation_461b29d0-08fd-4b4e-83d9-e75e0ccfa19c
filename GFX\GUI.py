# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'login.ui'
#
# Created by: PyQt5 UI code generator 5.15.11
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_ALFGAMETOOL(object):
    def setupUi(self, ALFGAMETOOL):
        ALFGAMETOOL.setObjectName("ALFGAMETOOL")
        ALFGAMETOOL.resize(1262, 846)
        ALFGAMETOOL.setIconSize(QtCore.QSize(80, 80))
        self.centralwidget = QtWidgets.QWidget(ALFGAMETOOL)
        self.centralwidget.setObjectName("centralwidget")
        self.ADB = QtWidgets.QPushButton(self.centralwidget)
        self.ADB.setGeometry(QtCore.QRect(10, 800, 75, 31))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setWeight(75)
        self.ADB.setFont(font)
        self.ADB.setObjectName("ADB")
        self.Get_Graphics_file = QtWidgets.QPushButton(self.centralwidget)
        self.Get_Graphics_file.setGeometry(QtCore.QRect(870, 780, 181, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.Get_Graphics_file.setFont(font)
        self.Get_Graphics_file.setObjectName("Get_Graphics_file")
        self.Apply = QtWidgets.QPushButton(self.centralwidget)
        self.Apply.setGeometry(QtCore.QRect(1060, 780, 181, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.Apply.setFont(font)
        self.Apply.setObjectName("Apply")
        self.Graphics = QtWidgets.QLabel(self.centralwidget)
        self.Graphics.setGeometry(QtCore.QRect(20, 20, 91, 31))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setWeight(75)
        self.Graphics.setFont(font)
        self.Graphics.setObjectName("Graphics")
        self.super_smooth = QtWidgets.QPushButton(self.centralwidget)
        self.super_smooth.setGeometry(QtCore.QRect(20, 70, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.super_smooth.setFont(font)
        self.super_smooth.setObjectName("super_smooth")
        self.smooth = QtWidgets.QPushButton(self.centralwidget)
        self.smooth.setGeometry(QtCore.QRect(190, 70, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.smooth.setFont(font)
        self.smooth.setObjectName("smooth")
        self.Balansed = QtWidgets.QPushButton(self.centralwidget)
        self.Balansed.setGeometry(QtCore.QRect(360, 70, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.Balansed.setFont(font)
        self.Balansed.setObjectName("Balansed")
        self.Ultra_HD = QtWidgets.QPushButton(self.centralwidget)
        self.Ultra_HD.setGeometry(QtCore.QRect(870, 70, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.Ultra_HD.setFont(font)
        self.Ultra_HD.setObjectName("Ultra_HD")
        self.HDR = QtWidgets.QPushButton(self.centralwidget)
        self.HDR.setGeometry(QtCore.QRect(700, 70, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.HDR.setFont(font)
        self.HDR.setObjectName("HDR")
        self.HD = QtWidgets.QPushButton(self.centralwidget)
        self.HD.setGeometry(QtCore.QRect(530, 70, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.HD.setFont(font)
        self.HD.setObjectName("HD")
        self.Frame_Rate = QtWidgets.QLabel(self.centralwidget)
        self.Frame_Rate.setGeometry(QtCore.QRect(20, 150, 131, 21))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setWeight(75)
        self.Frame_Rate.setFont(font)
        self.Frame_Rate.setObjectName("Frame_Rate")
        self.Medium = QtWidgets.QPushButton(self.centralwidget)
        self.Medium.setGeometry(QtCore.QRect(190, 180, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.Medium.setFont(font)
        self.Medium.setObjectName("Medium")
        self.Extreme = QtWidgets.QPushButton(self.centralwidget)
        self.Extreme.setGeometry(QtCore.QRect(700, 180, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.Extreme.setFont(font)
        self.Extreme.setObjectName("Extreme")
        self.High = QtWidgets.QPushButton(self.centralwidget)
        self.High.setGeometry(QtCore.QRect(360, 180, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.High.setFont(font)
        self.High.setObjectName("High")
        self.Extreme_2 = QtWidgets.QPushButton(self.centralwidget)
        self.Extreme_2.setGeometry(QtCore.QRect(870, 180, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.Extreme_2.setFont(font)
        self.Extreme_2.setObjectName("Extreme_2")
        self.Ultra = QtWidgets.QPushButton(self.centralwidget)
        self.Ultra.setGeometry(QtCore.QRect(530, 180, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.Ultra.setFont(font)
        self.Ultra.setObjectName("Ultra")
        self.Low = QtWidgets.QPushButton(self.centralwidget)
        self.Low.setGeometry(QtCore.QRect(20, 180, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.Low.setFont(font)
        self.Low.setObjectName("Low")
        self.Ultra_Extreme = QtWidgets.QPushButton(self.centralwidget)
        self.Ultra_Extreme.setGeometry(QtCore.QRect(1040, 180, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.Ultra_Extreme.setFont(font)
        self.Ultra_Extreme.setObjectName("Ultra_Extreme")
        self.style = QtWidgets.QLabel(self.centralwidget)
        self.style.setGeometry(QtCore.QRect(20, 230, 81, 71))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setWeight(75)
        self.style.setFont(font)
        self.style.setObjectName("style")
        self.Launch_PUBG_Mobile = QtWidgets.QPushButton(self.centralwidget)
        self.Launch_PUBG_Mobile.setGeometry(QtCore.QRect(130, 790, 51, 51))
        self.Launch_PUBG_Mobile.setText("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap("C:/Users/<USER>/Pictures/pubg_icon.ico"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.Launch_PUBG_Mobile.setIcon(icon)
        self.Launch_PUBG_Mobile.setIconSize(QtCore.QSize(51, 51))
        self.Launch_PUBG_Mobile.setObjectName("Launch_PUBG_Mobile")
        self.Classic = QtWidgets.QPushButton(self.centralwidget)
        self.Classic.setGeometry(QtCore.QRect(20, 310, 164, 164))
        self.Classic.setText("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap("C:/Users/<USER>/Desktop/New folder/classic.ico"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.Classic.setIcon(icon1)
        self.Classic.setIconSize(QtCore.QSize(164, 164))
        self.Classic.setObjectName("Classic")
        self.Colorful = QtWidgets.QPushButton(self.centralwidget)
        self.Colorful.setGeometry(QtCore.QRect(200, 310, 164, 164))
        self.Colorful.setText("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap("C:/Users/<USER>/Desktop/New folder/colorful.ico"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.Colorful.setIcon(icon2)
        self.Colorful.setIconSize(QtCore.QSize(164, 164))
        self.Colorful.setObjectName("Colorful")
        self.Soft = QtWidgets.QPushButton(self.centralwidget)
        self.Soft.setGeometry(QtCore.QRect(560, 310, 164, 164))
        self.Soft.setText("")
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap("C:/Users/<USER>/Desktop/New folder/soft.ico"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.Soft.setIcon(icon3)
        self.Soft.setIconSize(QtCore.QSize(164, 164))
        self.Soft.setObjectName("Soft")
        self.Realistic = QtWidgets.QPushButton(self.centralwidget)
        self.Realistic.setGeometry(QtCore.QRect(380, 310, 164, 164))
        self.Realistic.setText("")
        icon4 = QtGui.QIcon()
        icon4.addPixmap(QtGui.QPixmap("C:/Users/<USER>/Desktop/New folder/realistic.ico"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.Realistic.setIcon(icon4)
        self.Realistic.setIconSize(QtCore.QSize(164, 164))
        self.Realistic.setObjectName("Realistic")
        self.Movie = QtWidgets.QPushButton(self.centralwidget)
        self.Movie.setGeometry(QtCore.QRect(740, 310, 164, 164))
        self.Movie.setText("")
        icon5 = QtGui.QIcon()
        icon5.addPixmap(QtGui.QPixmap("C:/Users/<USER>/Desktop/New folder/movie.ico"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.Movie.setIcon(icon5)
        self.Movie.setIconSize(QtCore.QSize(164, 164))
        self.Movie.setObjectName("Movie")
        self.label = QtWidgets.QLabel(self.centralwidget)
        self.label.setGeometry(QtCore.QRect(20, 500, 121, 41))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setWeight(75)
        self.label.setFont(font)
        self.label.setObjectName("label")
        self.Low_2 = QtWidgets.QPushButton(self.centralwidget)
        self.Low_2.setGeometry(QtCore.QRect(20, 570, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.Low_2.setFont(font)
        self.Low_2.setObjectName("Low_2")
        self.High_2 = QtWidgets.QPushButton(self.centralwidget)
        self.High_2.setGeometry(QtCore.QRect(190, 570, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.High_2.setFont(font)
        self.High_2.setObjectName("High_2")
        self.Ultra_2 = QtWidgets.QPushButton(self.centralwidget)
        self.Ultra_2.setGeometry(QtCore.QRect(360, 570, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.Ultra_2.setFont(font)
        self.Ultra_2.setObjectName("Ultra_2")
        self.label_2 = QtWidgets.QLabel(self.centralwidget)
        self.label_2.setGeometry(QtCore.QRect(30, 640, 91, 41))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setWeight(75)
        self.label_2.setFont(font)
        self.label_2.setObjectName("label_2")
        self.Enable = QtWidgets.QPushButton(self.centralwidget)
        self.Enable.setGeometry(QtCore.QRect(20, 690, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.Enable.setFont(font)
        self.Enable.setObjectName("Enable")
        self.Disable = QtWidgets.QPushButton(self.centralwidget)
        self.Disable.setGeometry(QtCore.QRect(190, 690, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.Disable.setFont(font)
        self.Disable.setObjectName("Disable")
        ALFGAMETOOL.setCentralWidget(self.centralwidget)

        self.retranslateUi(ALFGAMETOOL)
        QtCore.QMetaObject.connectSlotsByName(ALFGAMETOOL)
        ALFGAMETOOL.setTabOrder(self.Launch_PUBG_Mobile, self.ADB)
        ALFGAMETOOL.setTabOrder(self.ADB, self.Get_Graphics_file)
        ALFGAMETOOL.setTabOrder(self.Get_Graphics_file, self.Apply)
        ALFGAMETOOL.setTabOrder(self.Apply, self.super_smooth)
        ALFGAMETOOL.setTabOrder(self.super_smooth, self.smooth)
        ALFGAMETOOL.setTabOrder(self.smooth, self.Balansed)
        ALFGAMETOOL.setTabOrder(self.Balansed, self.HD)
        ALFGAMETOOL.setTabOrder(self.HD, self.HDR)
        ALFGAMETOOL.setTabOrder(self.HDR, self.Ultra_HD)
        ALFGAMETOOL.setTabOrder(self.Ultra_HD, self.Low)
        ALFGAMETOOL.setTabOrder(self.Low, self.Medium)
        ALFGAMETOOL.setTabOrder(self.Medium, self.High)
        ALFGAMETOOL.setTabOrder(self.High, self.Ultra)
        ALFGAMETOOL.setTabOrder(self.Ultra, self.Extreme)
        ALFGAMETOOL.setTabOrder(self.Extreme, self.Extreme_2)
        ALFGAMETOOL.setTabOrder(self.Extreme_2, self.Ultra_Extreme)
        ALFGAMETOOL.setTabOrder(self.Ultra_Extreme, self.Classic)
        ALFGAMETOOL.setTabOrder(self.Classic, self.Colorful)
        ALFGAMETOOL.setTabOrder(self.Colorful, self.Realistic)
        ALFGAMETOOL.setTabOrder(self.Realistic, self.Soft)
        ALFGAMETOOL.setTabOrder(self.Soft, self.Movie)
        ALFGAMETOOL.setTabOrder(self.Movie, self.Low_2)
        ALFGAMETOOL.setTabOrder(self.Low_2, self.High_2)
        ALFGAMETOOL.setTabOrder(self.High_2, self.Ultra_2)
        ALFGAMETOOL.setTabOrder(self.Ultra_2, self.Enable)
        ALFGAMETOOL.setTabOrder(self.Enable, self.Disable)

    def retranslateUi(self, ALFGAMETOOL):
        _translate = QtCore.QCoreApplication.translate
        ALFGAMETOOL.setWindowTitle(_translate("ALFGAMETOOL", "ALF GAME TOOL "))
        self.ADB.setText(_translate("ALFGAMETOOL", "ADB"))
        self.Get_Graphics_file.setText(_translate("ALFGAMETOOL", "Get Graphics file"))
        self.Apply.setText(_translate("ALFGAMETOOL", "Apply"))
        self.Graphics.setText(_translate("ALFGAMETOOL", "Graphics "))
        self.super_smooth.setText(_translate("ALFGAMETOOL", "super smooth"))
        self.smooth.setText(_translate("ALFGAMETOOL", " smooth"))
        self.Balansed.setText(_translate("ALFGAMETOOL", "Balanced"))
        self.Ultra_HD.setText(_translate("ALFGAMETOOL", "Ultra HD"))
        self.HDR.setText(_translate("ALFGAMETOOL", "HDR"))
        self.HD.setText(_translate("ALFGAMETOOL", "HD"))
        self.Frame_Rate.setText(_translate("ALFGAMETOOL", "Frame Rate"))
        self.Medium.setText(_translate("ALFGAMETOOL", "Medium"))
        self.Extreme.setText(_translate("ALFGAMETOOL", "Extreme"))
        self.High.setText(_translate("ALFGAMETOOL", "High"))
        self.Extreme_2.setText(_translate("ALFGAMETOOL", "Extreme+"))
        self.Ultra.setText(_translate("ALFGAMETOOL", "Ultra"))
        self.Low.setText(_translate("ALFGAMETOOL", "Low"))
        self.Ultra_Extreme.setText(_translate("ALFGAMETOOL", "Ultra Extreme"))
        self.style.setText(_translate("ALFGAMETOOL", "Style"))
        self.label.setText(_translate("ALFGAMETOOL", "SFX Quality"))
        self.Low_2.setText(_translate("ALFGAMETOOL", "Low"))
        self.High_2.setText(_translate("ALFGAMETOOL", "High"))
        self.Ultra_2.setText(_translate("ALFGAMETOOL", "Ultra"))
        self.label_2.setText(_translate("ALFGAMETOOL", "Shadow"))
        self.Enable.setText(_translate("ALFGAMETOOL", "Enable"))
        self.Disable.setText(_translate("ALFGAMETOOL", "Disable"))


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ALFGAMETOOL = QtWidgets.QMainWindow()
    ui = Ui_ALFGAMETOOL()
    ui.setupUi(ALFGAMETOOL)
    ALFGAMETOOL.show()
    sys.exit(app.exec_())
