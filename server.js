// تحميل متغيرات البيئة
require('dotenv').config();

// وحدات أساسية
const express = require('express');
const fs = require('fs');
const path = require('path');
const http = require('http');
const https = require('https');
// const spdy = require('spdy'); // دعم HTTP/2 - تم تعطيله مؤقتًا
const cluster = require('cluster');
const os = require('os');
const cors = require('cors'); // للسماح بالوصول من أي مكان
const moment = require('moment-timezone'); // للتعامل مع المناطق الزمنية
const i18n = require('i18n'); // لدعم اللغات المتعددة

// وحدات الأمان والأداء - تم تعطيلها مؤقتًا للتجربة
// const helmet = require('helmet');
// const compression = require('compression');
// const shrinkRay = require('shrink-ray-current'); // ضغط Brotli المتقدم
// const hpp = require('hpp'); // حماية من تلوث المعلمات
// const enforceSSL = require('express-enforces-ssl');
// const ddos = require('ddos'); // حماية من هجمات DDoS
// const statusMonitor = require('express-status-monitor')(); // مراقبة حالة السيرفر
// const expressMinify = require('express-minify');
// const NodeCache = require('node-cache'); // تخزين مؤقت في الذاكرة

// وحدات التكوين والتسجيل
const config = require('./config/config');
const logger = require('./utils/logger');

// إنشاء تخزين مؤقت للاستجابات - تم تعطيله مؤقتًا
// const responseCache = new NodeCache({
//   stdTTL: 300, // مدة التخزين المؤقت الافتراضية (5 دقائق)
//   checkperiod: 60, // فترة التحقق من انتهاء الصلاحية (1 دقيقة)
//   useClones: false, // تعطيل النسخ لتحسين الأداء
//   deleteOnExpire: true // حذف العناصر منتهية الصلاحية
// });

// تكوين حماية DDoS - تم تعطيله مؤقتًا
// const ddosInstance = new ddos({
//   burst: 10, // عدد الطلبات المسموح بها في الانفجار
//   limit: 15, // حد الطلبات لكل فترة
//   maxexpiry: 120, // فترة الحظر القصوى (بالثانية)
//   trustProxy: true, // ثقة بوكيل العكسي
//   includeUserAgent: true, // تضمين وكيل المستخدم في الحساب
//   whitelist: ['127.0.0.1'], // القائمة البيضاء
//   errormessage: 'تم اكتشاف هجوم DDoS محتمل. تم حظر الوصول مؤقتًا.'
// });

// إعداد دعم اللغات المتعددة
i18n.configure({
  locales: ['ar', 'en', 'fr', 'es', 'zh', 'ru'], // اللغات المدعومة
  directory: path.join(__dirname, 'locales'),
  defaultLocale: 'ar',
  objectNotation: true,
  updateFiles: false,
  syncFiles: false,
  cookie: 'lang'
});

// إنشاء تطبيق Express
const app = express();

// إعداد CORS للسماح بالوصول من أي مكان
app.use(cors({
  origin: '*', // في الإنتاج، حدد المواقع المسموح لها بالوصول
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept-Language'],
  credentials: true,
  maxAge: 86400 // 24 ساعة
}));

// إعداد دعم اللغات المتعددة
app.use(i18n.init);

// إعداد المنطقة الزمنية
app.use((req, res, next) => {
  // استخدام UTC كمنطقة زمنية افتراضية
  moment.tz.setDefault('UTC');

  // يمكن تغيير المنطقة الزمنية بناءً على طلب المستخدم
  const timezone = req.headers['x-timezone'] || 'UTC';
  res.locals.timezone = timezone;

  // إضافة دالة مساعدة لتحويل التواريخ
  res.locals.formatDate = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
    return moment(date).tz(timezone).format(format);
  };

  next();
});

// تم تعطيل وسائط الأمان والأداء مؤقتًا للتجربة
// app.use(statusMonitor);
// app.use(helmet({ ... }));
// app.use(ddosInstance.express);
// if (config.server.env === 'production') {
//   app.enable('trust proxy');
//   app.use(enforceSSL());
// }
// app.use(hpp());
// app.use(shrinkRay());
// app.use(expressMinify());

// وسائط تحليل الطلبات
app.use(express.json({ limit: '5kb' })); // تقييد حجم طلبات JSON
app.use(express.urlencoded({ extended: true, limit: '5kb' }));

// تم تعطيل وسيط التخزين المؤقت مؤقتًا للتجربة
// app.use((req, res, next) => {
//   // تخزين مؤقت للطلبات GET فقط
//   if (req.method !== 'GET') {
//     return next();
//   }
//
//   // تجاهل التخزين المؤقت للمسارات المصادقة
//   if (req.path.startsWith('/api/auth')) {
//     return next();
//   }
//
//   const cacheKey = `${req.originalUrl}`;
//   const cachedResponse = responseCache.get(cacheKey);
//
//   if (cachedResponse) {
//     return res.status(200).send(cachedResponse);
//   }
//
//   // تخزين الاستجابة الأصلية
//   const originalSend = res.send;
//   res.send = function(body) {
//     if (res.statusCode === 200) {
//       responseCache.set(cacheKey, body);
//     }
//     originalSend.call(this, body);
//   };
//
//   next();
// });

// تكوين وسائط الأمان الإضافية
require('./middleware/security')(app);

// تسجيل الطلبات مع معلومات إضافية
app.use((req, res, next) => {
  const startTime = Date.now();

  // تسجيل عند اكتمال الطلب
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    logger.info(`${req.method} ${req.originalUrl} ${res.statusCode}`, {
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      duration: `${duration}ms`,
      contentLength: res.get('Content-Length') || 0,
      referrer: req.headers.referer || req.headers.referrer || '-'
    });
  });

  next();
});

// تسجيل المسارات للأقسام الثلاثة

// 1. قسم الأداة وملفاتها
app.use('/api/tool', require('./app/routes'));

// 2. قسم بيانات الترخيص
app.use('/api/license', require('./license/routes'));

// 3. قسم التحديثات والتحكم
app.use('/api/admin', require('./admin/routes'));

// مسارات المصادقة المشتركة
app.use('/api/auth', require('./routes/auth'));

// مسارات API العامة
app.use('/api', require('./routes/api'));

// مسار الصفحة الرئيسية
app.get('/', (req, res) => {
  // استخدام i18n للترجمة حسب لغة المستخدم
  const welcomeMessage = res.__('welcome');

  res.json({
    success: true,
    message: welcomeMessage,
    version: '1.0.0',
    status: 'متصل',
    timestamp: new Date(),
    timezone: res.locals.timezone,
    formattedTime: res.locals.formatDate(new Date()),
    supportedLanguages: i18n.getLocales(),
    currentLanguage: req.getLocale()
  });
});

// تكوين معالج الأخطاء
require('./middleware/errorHandler')(app);

// تحديد المنفذ
const PORT = config.server.port;

// إعداد إشارة الجاهزية لـ PM2
process.on('message', (message) => {
  if (message === 'ready') {
    logger.info('تم استلام إشارة الجاهزية من PM2');
  }
});

// وظيفة بدء تشغيل السيرفر
const startServer = () => {
  // إعدادات SSL
  let sslOptions = {};
  let server;

  if (config.server.env === 'production') {
    try {
      // محاولة تحميل شهادات SSL
      const privateKey = fs.readFileSync(path.join(__dirname, 'ssl', 'private.key'), 'utf8');
      const certificate = fs.readFileSync(path.join(__dirname, 'ssl', 'certificate.crt'), 'utf8');
      const ca = fs.existsSync(path.join(__dirname, 'ssl', 'ca.crt')) ?
        fs.readFileSync(path.join(__dirname, 'ssl', 'ca.crt'), 'utf8') : undefined;

      sslOptions = {
        key: privateKey,
        cert: certificate,
        ca: ca,
        honorCipherOrder: true,
        ciphers: [
          'ECDHE-ECDSA-AES256-GCM-SHA384',
          'ECDHE-RSA-AES256-GCM-SHA384',
          'ECDHE-ECDSA-CHACHA20-POLY1305',
          'ECDHE-RSA-CHACHA20-POLY1305',
          'ECDHE-ECDSA-AES128-GCM-SHA256',
          'ECDHE-RSA-AES128-GCM-SHA256'
        ].join(':'),
        ecdhCurve: 'prime256v1:secp384r1:secp521r1',
        secureOptions: require('constants').SSL_OP_NO_TLSv1 | require('constants').SSL_OP_NO_TLSv1_1
      };

      // إنشاء سيرفر HTTPS بدلاً من HTTP/2 مؤقتًا
      server = https.createServer(sslOptions, app);

      logger.info('تم تكوين السيرفر باستخدام HTTP/2 و HTTPS');
    } catch (error) {
      logger.error(`فشل تحميل شهادات SSL: ${error.message}`);
      logger.warn('استخدام HTTP بدلاً من ذلك (غير آمن)');

      // استخدام HTTP إذا فشل تحميل شهادات SSL
      server = http.createServer(app);
    }
  } else {
    // استخدام HTTP في بيئة التطوير
    server = http.createServer(app);
    logger.info('تم تكوين السيرفر باستخدام HTTP (بيئة التطوير)');
  }

  // إعدادات السيرفر المتقدمة
  server.headersTimeout = 60000; // 60 ثانية
  server.keepAliveTimeout = 65000; // 65 ثانية
  server.maxHeadersCount = 100; // الحد الأقصى لعدد الرؤوس
  server.timeout = 300000; // 5 دقائق
  server.maxConnections = 10000; // الحد الأقصى للاتصالات المتزامنة

  // بدء الاستماع على جميع الواجهات (0.0.0.0) للوصول العالمي
  server.listen(PORT, '0.0.0.0', () => {
    logger.info(`السيرفر يعمل على المنفذ ${PORT} في وضع ${config.server.env}`);

    // إرسال إشارة الجاهزية إلى PM2
    if (process.send) {
      process.send('ready');
    }

    // طباعة معلومات النظام
    logger.info(`معلومات النظام: ${os.platform()} ${os.release()}, ${os.cpus().length} وحدات معالجة، ${Math.round(os.totalmem() / (1024 * 1024 * 1024))}GB ذاكرة`);
  });

  return server;
};

// استخدام وضع المجموعة في الإنتاج إذا لم يكن يعمل بالفعل تحت PM2
if (config.server.env === 'production' && !process.env.PM2_HOME && cluster.isMaster) {
  const numCPUs = os.cpus().length;

  logger.info(`بدء تشغيل السيرفر في وضع المجموعة مع ${numCPUs} عمليات`);

  // إنشاء عامل لكل وحدة معالجة
  for (let i = 0; i < numCPUs; i++) {
    cluster.fork();
  }

  // التعامل مع خروج العامل
  cluster.on('exit', (worker, code, signal) => {
    logger.warn(`العامل ${worker.process.pid} توقف بالرمز ${code}. إعادة التشغيل...`);
    cluster.fork();
  });
} else {
  // بدء تشغيل السيرفر
  const server = startServer();

  // التعامل مع الإغلاق الآمن
  const gracefulShutdown = (signal) => {
    logger.info(`تم استلام إشارة ${signal}، بدء الإغلاق الآمن...`);

    server.close(() => {
      logger.info('تم إغلاق جميع الاتصالات بنجاح');
      process.exit(0);
    });

    // إغلاق قسري بعد 30 ثانية
    setTimeout(() => {
      logger.error('تم تجاوز مهلة الإغلاق الآمن، إجبار الخروج');
      process.exit(1);
    }, 30000);
  };

  // التقاط إشارات الإنهاء
  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));
}

// التعامل مع الأخطاء غير المعالجة
process.on('uncaughtException', (error) => {
  logger.error(`خطأ غير معالج: ${error.message}`, { stack: error.stack });

  // محاولة الإغلاق الآمن
  if (config.server.env === 'production') {
    logger.error('حدث خطأ حرج. إعادة تشغيل العملية...');
    process.exit(1);
  }
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error(`رفض غير معالج: ${reason}`, {
    stack: reason.stack || 'لا يوجد تتبع للمكدس',
    promise: promise.toString()
  });

  // في بيئة التطوير، نطبع التتبع الكامل ولكن لا نخرج
  if (config.server.env !== 'production') {
    console.error('رفض غير معالج:', reason);
  }
});

// تنظيف الذاكرة دوريًا
setInterval(() => {
  if (global.gc) {
    global.gc();
    logger.debug('تم تشغيل جامع القمامة يدويًا');
  }
}, 30 * 60 * 1000); // كل 30 دقيقة

// مراقبة استخدام الذاكرة
setInterval(() => {
  const memoryUsage = process.memoryUsage();
  logger.info('استخدام الذاكرة:', {
    rss: `${Math.round(memoryUsage.rss / (1024 * 1024))} MB`,
    heapTotal: `${Math.round(memoryUsage.heapTotal / (1024 * 1024))} MB`,
    heapUsed: `${Math.round(memoryUsage.heapUsed / (1024 * 1024))} MB`,
    external: `${Math.round(memoryUsage.external / (1024 * 1024))} MB`,
    pid: process.pid
  });
}, 15 * 60 * 1000); // كل 15 دقيقة

module.exports = app;
