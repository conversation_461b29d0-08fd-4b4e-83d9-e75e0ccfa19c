const express = require('express');
const { body, validationResult } = require('express-validator');
const { hashPassword, comparePassword, generateToken } = require('../utils/encryption');
const logger = require('../utils/logger');
const router = express.Router();

// مصفوفة تخزين المستخدمين (في الإنتاج، استخدم قاعدة بيانات)
const users = [];

// إضافة مستخدم افتراضي للاختبار
const { hashPassword: hashPasswordSync } = require('../utils/encryption');

// إنشاء مستخدم افتراضي
(async () => {
  try {
    const defaultUser = {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      password: await hashPasswordSync('admin123'),
      role: 'admin',
      createdAt: new Date()
    };
    users.push(defaultUser);
    console.log('✅ تم إنشاء مستخدم افتراضي: <EMAIL> / admin123');
  } catch (error) {
    console.error('❌ فشل في إنشاء المستخدم الافتراضي:', error.message);
  }
})();

/**
 * مسار تسجيل مستخدم جديد
 * POST /api/auth/register
 */
router.post('/register', [
  // التحقق من صحة البيانات
  body('username')
    .isLength({ min: 3, max: 30 })
    .withMessage('يجب أن يكون اسم المستخدم بين 3 و 30 حرفًا')
    .isAlphanumeric()
    .withMessage('يجب أن يحتوي اسم المستخدم على أحرف وأرقام فقط'),
  body('email')
    .isEmail()
    .withMessage('يرجى إدخال بريد إلكتروني صالح')
    .normalizeEmail(),
  body('password')
    .isLength({ min: 8 })
    .withMessage('يجب أن تكون كلمة المرور 8 أحرف على الأقل')
    .matches(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*])/)
    .withMessage('يجب أن تحتوي كلمة المرور على حرف كبير وحرف صغير ورقم ورمز خاص')
], async (req, res) => {
  try {
    // التحقق من نتائج التحقق
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { username, email, password } = req.body;

    // التحقق من عدم وجود المستخدم مسبقًا
    const userExists = users.find(user =>
      user.email === email || user.username === username
    );

    if (userExists) {
      return res.status(400).json({
        success: false,
        message: 'البريد الإلكتروني أو اسم المستخدم مسجل بالفعل'
      });
    }

    // تشفير كلمة المرور
    const hashedPassword = await hashPassword(password);

    // إنشاء مستخدم جديد
    const newUser = {
      id: users.length + 1,
      username,
      email,
      password: hashedPassword,
      role: 'user',
      createdAt: new Date()
    };

    // إضافة المستخدم إلى المصفوفة
    users.push(newUser);

    // إنشاء توكن JWT
    const token = generateToken({
      id: newUser.id,
      username: newUser.username,
      email: newUser.email,
      role: newUser.role
    });

    // تسجيل الحدث
    logger.info(`تم تسجيل مستخدم جديد: ${username}`);

    // إرسال الاستجابة
    res.status(201).json({
      success: true,
      message: 'تم تسجيل المستخدم بنجاح',
      token,
      user: {
        id: newUser.id,
        username: newUser.username,
        email: newUser.email,
        role: newUser.role
      }
    });
  } catch (error) {
    logger.error(`خطأ في تسجيل المستخدم: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء تسجيل المستخدم'
    });
  }
});

/**
 * مسار تسجيل الدخول
 * POST /api/auth/login
 */
router.post('/login', [
  // التحقق من صحة البيانات
  body('email').isEmail().withMessage('يرجى إدخال بريد إلكتروني صالح'),
  body('password').notEmpty().withMessage('كلمة المرور مطلوبة')
], async (req, res) => {
  try {
    // التحقق من نتائج التحقق
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { email, password } = req.body;

    // البحث عن المستخدم
    const user = users.find(user => user.email === email);

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'بيانات الاعتماد غير صالحة'
      });
    }

    // التحقق من كلمة المرور
    const isPasswordValid = await comparePassword(password, user.password);

    if (!isPasswordValid) {
      // تسجيل محاولة تسجيل دخول فاشلة
      logger.warn(`محاولة تسجيل دخول فاشلة للبريد الإلكتروني: ${email}`);

      return res.status(401).json({
        success: false,
        message: 'بيانات الاعتماد غير صالحة'
      });
    }

    // إنشاء توكن JWT
    const token = generateToken({
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role
    });

    // تسجيل تسجيل الدخول الناجح
    logger.info(`تسجيل دخول ناجح: ${user.username}`);

    // إرسال الاستجابة
    res.status(200).json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      }
    });
  } catch (error) {
    logger.error(`خطأ في تسجيل الدخول: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء تسجيل الدخول'
    });
  }
});

module.exports = router;
