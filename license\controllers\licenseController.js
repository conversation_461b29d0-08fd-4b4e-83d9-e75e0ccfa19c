/**
 * وحدة التحكم بالتراخيص
 */
const License = require('../models/License');
const logger = require('../../utils/logger');

// إنشاء مجموعة تجريبية من التراخيص
const licenses = [
  new License('LIC-12345-ABCDE', 'abcde-12345-fghij-67890', 1, 'premium', ['feature1', 'feature2', 'feature3', 'feature4', 'feature5']),
  new License('LIC-67890-FGHIJ', 'fghij-67890-klmno-12345', 2, 'standard', ['feature1', 'feature2', 'feature3']),
  new License('LIC-13579-KLMNO', 'klmno-13579-pqrst-24680', 3, 'basic', ['feature1'])
];

// تعطيل الترخيص الثالث للاختبار
licenses[2].deactivate();

/**
 * التحقق من حالة الترخيص
 */
exports.checkStatus = (req, res) => {
  const userId = req.user.id;
  
  logger.info(`تم طلب حالة الترخيص للمستخدم: ${req.user.username}`);
  
  // البحث عن ترخيص المستخدم
  const license = licenses.find(lic => lic.userId === userId);
  
  if (!license) {
    return res.status(404).json({
      success: false,
      message: 'لم يتم العثور على ترخيص لهذا المستخدم'
    });
  }
  
  res.json({
    success: true,
    message: 'تم التحقق من حالة الترخيص بنجاح',
    license: license.getInfo()
  });
};

/**
 * التحقق من صحة مفتاح الترخيص
 */
exports.validateLicense = (req, res) => {
  const { licenseKey } = req.body;
  
  if (!licenseKey) {
    return res.status(400).json({
      success: false,
      message: 'مفتاح الترخيص مطلوب'
    });
  }
  
  logger.info(`تم طلب التحقق من صحة مفتاح الترخيص: ${licenseKey.substring(0, 5)}...`);
  
  // البحث عن الترخيص بواسطة المفتاح
  const license = licenses.find(lic => lic.key === licenseKey);
  
  if (!license) {
    return res.status(400).json({
      success: false,
      message: 'مفتاح الترخيص غير صالح'
    });
  }
  
  // التحقق من صلاحية الترخيص
  const isValid = license.isValid();
  
  if (!isValid) {
    return res.status(400).json({
      success: false,
      message: 'الترخيص غير صالح أو منتهي الصلاحية',
      license: license.getInfo()
    });
  }
  
  res.json({
    success: true,
    message: 'مفتاح الترخيص صالح',
    license: license.getInfo()
  });
};

/**
 * تفعيل الترخيص
 */
exports.activateLicense = (req, res) => {
  const { licenseKey } = req.body;
  const userId = req.user.id;
  
  if (!licenseKey) {
    return res.status(400).json({
      success: false,
      message: 'مفتاح الترخيص مطلوب'
    });
  }
  
  logger.info(`تم طلب تفعيل الترخيص للمستخدم: ${req.user.username}`, {
    licenseKey: licenseKey.substring(0, 5) + '...'
  });
  
  // البحث عن الترخيص بواسطة المفتاح
  let license = licenses.find(lic => lic.key === licenseKey);
  
  // إذا لم يتم العثور على الترخيص، نقوم بإنشاء ترخيص جديد
  if (!license) {
    // تحديد نوع الترخيص بناءً على طول المفتاح (للتجربة فقط)
    let type = 'basic';
    let features = ['feature1'];
    
    if (licenseKey.length > 20) {
      type = 'standard';
      features = ['feature1', 'feature2', 'feature3'];
    }
    
    if (licenseKey.length > 25) {
      type = 'premium';
      features = ['feature1', 'feature2', 'feature3', 'feature4', 'feature5'];
    }
    
    // إنشاء ترخيص جديد
    license = new License(
      'LIC-' + Math.floor(10000 + Math.random() * 90000) + '-' + licenseKey.substring(0, 5).toUpperCase(),
      licenseKey,
      userId,
      type,
      features
    );
    
    // إضافة الترخيص إلى المجموعة
    licenses.push(license);
  } else {
    // إذا كان الترخيص موجودًا بالفعل، نقوم بتحديث معرف المستخدم
    license.userId = userId;
  }
  
  // تفعيل الترخيص
  const result = license.activate();
  
  res.json(result);
};

/**
 * تعطيل الترخيص
 */
exports.deactivateLicense = (req, res) => {
  const userId = req.user.id;
  
  logger.info(`تم طلب تعطيل الترخيص للمستخدم: ${req.user.username}`);
  
  // البحث عن ترخيص المستخدم
  const license = licenses.find(lic => lic.userId === userId);
  
  if (!license) {
    return res.status(404).json({
      success: false,
      message: 'لم يتم العثور على ترخيص لهذا المستخدم'
    });
  }
  
  // تعطيل الترخيص
  const result = license.deactivate();
  
  res.json(result);
};

/**
 * تمديد فترة الترخيص
 */
exports.extendLicense = (req, res) => {
  const { licenseId, days } = req.body;
  
  if (!licenseId || !days) {
    return res.status(400).json({
      success: false,
      message: 'معرف الترخيص وعدد الأيام مطلوبان'
    });
  }
  
  logger.info(`تم طلب تمديد فترة الترخيص بواسطة المسؤول: ${req.user.username}`, {
    licenseId,
    days
  });
  
  // البحث عن الترخيص بواسطة المعرف
  const license = licenses.find(lic => lic.id === licenseId);
  
  if (!license) {
    return res.status(404).json({
      success: false,
      message: 'لم يتم العثور على الترخيص'
    });
  }
  
  // تمديد فترة الترخيص
  const result = license.extend(parseInt(days));
  
  res.json(result);
};

/**
 * ترقية نوع الترخيص
 */
exports.upgradeLicense = (req, res) => {
  const { licenseId, newType } = req.body;
  
  if (!licenseId || !newType) {
    return res.status(400).json({
      success: false,
      message: 'معرف الترخيص ونوع الترخيص الجديد مطلوبان'
    });
  }
  
  logger.info(`تم طلب ترقية نوع الترخيص بواسطة المسؤول: ${req.user.username}`, {
    licenseId,
    newType
  });
  
  // البحث عن الترخيص بواسطة المعرف
  const license = licenses.find(lic => lic.id === licenseId);
  
  if (!license) {
    return res.status(404).json({
      success: false,
      message: 'لم يتم العثور على الترخيص'
    });
  }
  
  // تحديد الميزات بناءً على نوع الترخيص الجديد
  let newFeatures;
  
  switch (newType) {
    case 'basic':
      newFeatures = ['feature1'];
      break;
    case 'standard':
      newFeatures = ['feature1', 'feature2', 'feature3'];
      break;
    case 'premium':
      newFeatures = ['feature1', 'feature2', 'feature3', 'feature4', 'feature5'];
      break;
    default:
      return res.status(400).json({
        success: false,
        message: 'نوع الترخيص غير صالح. الأنواع المتاحة: basic, standard, premium'
      });
  }
  
  // ترقية نوع الترخيص
  const result = license.upgrade(newType, newFeatures);
  
  res.json(result);
};

/**
 * الحصول على قائمة التراخيص
 */
exports.getLicenses = (req, res) => {
  const { limit = 10, page = 1 } = req.query;
  
  logger.info(`تم طلب قائمة التراخيص بواسطة المسؤول: ${req.user.username}`, {
    limit,
    page
  });
  
  // حساب التراخيص المطلوبة بناءً على الصفحة والحد
  const startIndex = (parseInt(page) - 1) * parseInt(limit);
  const endIndex = startIndex + parseInt(limit);
  
  // الحصول على التراخيص المطلوبة
  const paginatedLicenses = licenses.slice(startIndex, endIndex);
  
  // تحويل التراخيص إلى معلومات
  const licensesInfo = paginatedLicenses.map(license => license.getInfo());
  
  res.json({
    success: true,
    message: 'تم استرجاع قائمة التراخيص بنجاح',
    licenses: licensesInfo,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(licenses.length / parseInt(limit)),
      totalItems: licenses.length
    }
  });
};
