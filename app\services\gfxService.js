/**
 * خدمة GFX - تحسين رسومات PUBG Mobile
 * تحويل وظائف GFX.py إلى خدمة Node.js
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const logger = require('../../utils/logger');
const gfxConfig = require('../config/gfx');

class GFXService {
  constructor() {
    // استخدام إعدادات من ملف التكوين
    this.adbPath = gfxConfig.adb.path;
    this.adbTimeout = gfxConfig.adb.timeout;
    this.retryAttempts = gfxConfig.adb.retryAttempts;
    this.retryDelay = gfxConfig.adb.retryDelay;

    this.connectedDevices = new Map();
    this.settingsCache = new Map();
    this.currentSettings = {
      graphics: gfxConfig.graphics.default,
      framerate: gfxConfig.framerate.default,
      style: gfxConfig.style.default,
      sfx: gfxConfig.sfx.default,
      shadow: gfxConfig.shadow.default
    };

    // مسارات ملفات PUBG Mobile من التكوين
    this.pubgPaths = {
      activeSav: gfxConfig.pubg.activeSavPath,
      userSettings: gfxConfig.pubg.userSettingsPath,
      packageName: gfxConfig.pubg.packageName
    };

    // خرائط الإعدادات من التكوين
    this.graphicsMapping = gfxConfig.graphics.mapping;
    this.framerateMapping = gfxConfig.framerate.mapping;
    this.styleMapping = gfxConfig.style.mapping;
    this.sfxMapping = gfxConfig.sfx.mapping;
    this.shadowMapping = gfxConfig.shadow.mapping;

    // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
    if (gfxConfig.security.createBackup) {
      this.ensureBackupDirectory();
    }
  }

  /**
   * إنشاء مجلد النسخ الاحتياطية
   */
  ensureBackupDirectory() {
    try {
      const backupDir = gfxConfig.security.backupDirectory;
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
        logger.info(`تم إنشاء مجلد النسخ الاحتياطية: ${backupDir}`);
      }
    } catch (error) {
      logger.error(`فشل في إنشاء مجلد النسخ الاحتياطية: ${error.message}`);
    }
  }

  /**
   * إنشاء نسخة احتياطية من ملف
   */
  async createBackup(deviceId, fileName) {
    if (!gfxConfig.security.createBackup) return null;

    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFileName = `${fileName}_${deviceId}_${timestamp}.bak`;
      const backupPath = path.join(gfxConfig.security.backupDirectory, backupFileName);

      // سحب الملف الأصلي
      const tempDir = require('os').tmpdir();
      const tempPath = path.join(tempDir, fileName);

      await this.executeADBCommand(`-s ${deviceId} pull ${this.pubgPaths.activeSav} ${tempPath}`);

      // نسخ إلى مجلد النسخ الاحتياطية
      if (fs.existsSync(tempPath)) {
        fs.copyFileSync(tempPath, backupPath);
        fs.unlinkSync(tempPath);

        logger.info(`تم إنشاء نسخة احتياطية: ${backupFileName}`);

        // تنظيف النسخ القديمة
        this.cleanupOldBackups();

        return backupPath;
      }

      return null;
    } catch (error) {
      logger.error(`فشل في إنشاء نسخة احتياطية: ${error.message}`);
      return null;
    }
  }

  /**
   * تنظيف النسخ الاحتياطية القديمة
   */
  cleanupOldBackups() {
    try {
      const backupDir = gfxConfig.security.backupDirectory;
      const files = fs.readdirSync(backupDir)
        .filter(file => file.endsWith('.bak'))
        .map(file => ({
          name: file,
          path: path.join(backupDir, file),
          stats: fs.statSync(path.join(backupDir, file))
        }))
        .sort((a, b) => b.stats.mtime - a.stats.mtime);

      // حذف النسخ الزائدة
      if (files.length > gfxConfig.security.maxBackups) {
        const filesToDelete = files.slice(gfxConfig.security.maxBackups);
        filesToDelete.forEach(file => {
          fs.unlinkSync(file.path);
          logger.info(`تم حذف نسخة احتياطية قديمة: ${file.name}`);
        });
      }
    } catch (error) {
      logger.error(`فشل في تنظيف النسخ الاحتياطية: ${error.message}`);
    }
  }

  /**
   * تشغيل أمر ADB مع إعادة المحاولة
   */
  async executeADBCommand(command) {
    return new Promise((resolve, reject) => {
      const fullCommand = `${this.adbPath} ${command}`;

      exec(fullCommand, (error, stdout, stderr) => {
        if (error) {
          logger.error(`خطأ في تنفيذ أمر ADB: ${error.message}`);
          reject(error);
          return;
        }

        if (stderr) {
          logger.warn(`تحذير ADB: ${stderr}`);
        }

        resolve(stdout.trim());
      });
    });
  }

  /**
   * البحث عن الأجهزة المتصلة
   */
  async getConnectedDevices() {
    try {
      const output = await this.executeADBCommand('devices');
      const lines = output.split('\n').slice(1); // تجاهل السطر الأول
      const devices = [];

      for (const line of lines) {
        const parts = line.trim().split('\t');
        if (parts.length === 2 && parts[1] === 'device') {
          devices.push({
            id: parts[0],
            status: 'connected',
            type: parts[0].includes('emulator') ? 'emulator' : 'device'
          });
        }
      }

      return devices;
    } catch (error) {
      logger.error(`فشل في الحصول على الأجهزة المتصلة: ${error.message}`);
      return [];
    }
  }

  /**
   * الاتصال بجهاز محدد
   */
  async connectToDevice(deviceId = null) {
    try {
      const devices = await this.getConnectedDevices();

      if (devices.length === 0) {
        throw new Error('لا توجد أجهزة متصلة');
      }

      // إذا لم يتم تحديد جهاز، استخدم الأول
      const targetDevice = deviceId ?
        devices.find(d => d.id === deviceId) :
        devices[0];

      if (!targetDevice) {
        throw new Error(`الجهاز ${deviceId} غير موجود`);
      }

      // اختبار الاتصال
      await this.executeADBCommand(`-s ${targetDevice.id} shell echo "test"`);

      this.connectedDevices.set(targetDevice.id, {
        ...targetDevice,
        connectedAt: new Date()
      });

      logger.info(`تم الاتصال بالجهاز: ${targetDevice.id}`);

      return {
        success: true,
        device: targetDevice,
        message: `تم الاتصال بالجهاز ${targetDevice.id} بنجاح`
      };

    } catch (error) {
      logger.error(`فشل الاتصال بالجهاز: ${error.message}`);
      throw error;
    }
  }

  /**
   * قطع الاتصال بجهاز
   */
  disconnectFromDevice(deviceId) {
    if (this.connectedDevices.has(deviceId)) {
      this.connectedDevices.delete(deviceId);
      logger.info(`تم قطع الاتصال بالجهاز: ${deviceId}`);
      return true;
    }
    return false;
  }

  /**
   * قراءة ملف Active.sav من الجهاز
   */
  async readActiveSavFile(deviceId) {
    try {
      const tempDir = require('os').tmpdir();
      const localPath = path.join(tempDir, 'Active.sav');

      // سحب الملف من الجهاز
      await this.executeADBCommand(`-s ${deviceId} pull ${this.pubgPaths.activeSav} ${localPath}`);

      // قراءة الملف
      if (fs.existsSync(localPath)) {
        const fileContent = fs.readFileSync(localPath);

        // حذف الملف المؤقت
        fs.unlinkSync(localPath);

        return fileContent;
      } else {
        throw new Error('فشل في سحب ملف Active.sav');
      }

    } catch (error) {
      logger.error(`فشل في قراءة ملف Active.sav: ${error.message}`);
      throw error;
    }
  }

  /**
   * كتابة ملف Active.sav إلى الجهاز
   */
  async writeActiveSavFile(deviceId, fileContent) {
    try {
      const tempDir = require('os').tmpdir();
      const localPath = path.join(tempDir, 'Active_modified.sav');

      // كتابة الملف محلياً
      fs.writeFileSync(localPath, fileContent);

      // رفع الملف إلى الجهاز
      await this.executeADBCommand(`-s ${deviceId} push ${localPath} ${this.pubgPaths.activeSav}`);

      // حذف الملف المؤقت
      fs.unlinkSync(localPath);

      logger.info('تم رفع ملف Active.sav المحدث إلى الجهاز');
      return true;

    } catch (error) {
      logger.error(`فشل في كتابة ملف Active.sav: ${error.message}`);
      throw error;
    }
  }

  /**
   * البحث عن موقع خاصية في ملف Active.sav
   */
  findPropertyInSav(fileContent, propertyName) {
    const searchBytes = Buffer.from(propertyName + '\0\f\0\0\0IntProperty\0\u0004\0\0\0\0\0\0\0\0', 'utf-8');
    const index = fileContent.indexOf(searchBytes);

    if (index !== -1) {
      return index + searchBytes.length;
    }

    return -1;
  }

  /**
   * تحديث خاصية في ملف Active.sav
   */
  updatePropertyInSav(fileContent, propertyName, newValue) {
    const position = this.findPropertyInSav(fileContent, propertyName);

    if (position !== -1) {
      fileContent[position] = newValue;
      return true;
    }

    logger.warn(`لم يتم العثور على الخاصية: ${propertyName}`);
    return false;
  }

  /**
   * قراءة الإعدادات الحالية من الجهاز
   */
  async readCurrentSettings(deviceId) {
    try {
      const fileContent = await this.readActiveSavFile(deviceId);

      // تحليل الإعدادات من الملف
      // هذا مبسط - في التطبيق الحقيقي نحتاج تحليل أكثر تعقيداً

      this.currentSettings = {
        graphics: 'Balanced',  // قيمة افتراضية
        framerate: 'Medium',
        style: 'Classic',
        sfx: 'Medium',
        shadow: 'Enable'
      };

      logger.info('تم قراءة الإعدادات الحالية من الجهاز');
      return this.currentSettings;

    } catch (error) {
      logger.error(`فشل في قراءة الإعدادات: ${error.message}`);
      throw error;
    }
  }

  /**
   * تطبيق إعدادات جديدة على الجهاز
   */
  async applySettings(deviceId, newSettings) {
    try {
      // إيقاف PUBG Mobile أولاً
      await this.executeADBCommand(`-s ${deviceId} shell am force-stop ${this.pubgPaths.packageName}`);

      // قراءة ملف Active.sav
      const fileContent = await this.readActiveSavFile(deviceId);

      // تحديث الإعدادات
      let modified = false;

      if (newSettings.graphics) {
        const value = this.graphicsMapping[newSettings.graphics];
        if (value) {
          this.updatePropertyInSav(fileContent, 'LobbyRenderQuality', value);
          this.updatePropertyInSav(fileContent, 'BattleRenderQuality', value);
          modified = true;
        }
      }

      if (newSettings.framerate) {
        const value = this.framerateMapping[newSettings.framerate];
        if (value) {
          this.updatePropertyInSav(fileContent, 'LobbyFrameRate', value);
          this.updatePropertyInSav(fileContent, 'BattleFrameRate', value);
          modified = true;
        }
      }

      if (newSettings.style) {
        const value = this.styleMapping[newSettings.style];
        if (value) {
          this.updatePropertyInSav(fileContent, 'LobbyRenderStyle', value);
          this.updatePropertyInSav(fileContent, 'BattleRenderStyle', value);
          modified = true;
        }
      }

      // كتابة الملف المحدث
      if (modified) {
        await this.writeActiveSavFile(deviceId, fileContent);

        // تحديث الإعدادات المحلية
        this.currentSettings = { ...this.currentSettings, ...newSettings };

        logger.info('تم تطبيق الإعدادات الجديدة بنجاح');
        return {
          success: true,
          message: 'تم تطبيق الإعدادات بنجاح',
          appliedSettings: newSettings
        };
      } else {
        return {
          success: false,
          message: 'لم يتم تطبيق أي إعدادات'
        };
      }

    } catch (error) {
      logger.error(`فشل في تطبيق الإعدادات: ${error.message}`);
      throw error;
    }
  }

  /**
   * تشغيل PUBG Mobile
   */
  async launchPUBGMobile(deviceId) {
    try {
      await this.executeADBCommand(`-s ${deviceId} shell monkey -p ${this.pubgPaths.packageName} -c android.intent.category.LAUNCHER 1`);

      logger.info('تم تشغيل PUBG Mobile');
      return {
        success: true,
        message: 'تم تشغيل PUBG Mobile بنجاح'
      };

    } catch (error) {
      logger.error(`فشل في تشغيل PUBG Mobile: ${error.message}`);
      throw error;
    }
  }
}

module.exports = new GFXService();
