<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 تحميل أداة التحكم في السيرفر</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2d2d2d 0%, #4a4a4a 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .server-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 5px solid #ecb440;
        }

        .server-info h2 {
            color: #2d2d2d;
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .info-item {
            background: white;
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #e0e0e0;
        }

        .info-item strong {
            color: #ecb440;
        }

        .downloads-section {
            margin-top: 30px;
        }

        .downloads-section h2 {
            color: #2d2d2d;
            margin-bottom: 20px;
            font-size: 1.8em;
            text-align: center;
        }

        .downloads-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .download-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 2px solid transparent;
        }

        .download-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
            border-color: #ecb440;
        }

        .download-card h3 {
            color: #2d2d2d;
            margin-bottom: 10px;
            font-size: 1.3em;
        }

        .download-card p {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .file-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-size: 0.9em;
        }

        .file-info div {
            margin-bottom: 5px;
        }

        .download-btn {
            display: inline-block;
            background: linear-gradient(135deg, #ecb440 0%, #ffd966 100%);
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
            text-align: center;
            box-shadow: 0 5px 15px rgba(236, 180, 64, 0.3);
        }

        .download-btn:hover {
            background: linear-gradient(135deg, #d4a73a 0%, #ecb440 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(236, 180, 64, 0.4);
        }

        .download-btn.auth-required {
            background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
        }

        .download-btn.auth-required:hover {
            background: linear-gradient(135deg, #ff5252 0%, #ff6b6b 100%);
            box-shadow: 0 8px 20px rgba(255, 107, 107, 0.4);
        }

        .auth-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 10px;
            border-radius: 8px;
            margin-top: 10px;
            font-size: 0.9em;
        }

        .instructions {
            background: #e8f5e8;
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
            border-left: 5px solid #4CAF50;
        }

        .instructions h3 {
            color: #2d2d2d;
            margin-bottom: 15px;
        }

        .instructions ol {
            padding-right: 20px;
        }

        .instructions li {
            margin-bottom: 10px;
            line-height: 1.6;
        }

        .footer {
            background: #2d2d2d;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 30px;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .error {
            background: #ffebee;
            border: 1px solid #ffcdd2;
            color: #c62828;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
            
            .downloads-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 تحميل أداة التحكم في السيرفر</h1>
            <p>أداة Python شاملة للتحكم في السيرفر وإدارة GFX عن بُعد</p>
        </div>

        <div class="content">
            <div class="server-info">
                <h2>📊 معلومات السيرفر</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <strong>اسم السيرفر:</strong> <span id="serverName">جاري التحميل...</span>
                    </div>
                    <div class="info-item">
                        <strong>الإصدار:</strong> <span id="serverVersion">جاري التحميل...</span>
                    </div>
                    <div class="info-item">
                        <strong>الحالة:</strong> <span id="serverStatus">🟢 متاح</span>
                    </div>
                    <div class="info-item">
                        <strong>عدد الملفات:</strong> <span id="totalFiles">جاري التحميل...</span>
                    </div>
                </div>
            </div>

            <div class="downloads-section">
                <h2>📥 الملفات المتاحة للتحميل</h2>
                <div id="downloadsContainer" class="downloads-grid">
                    <div class="loading">
                        <h3>🔄 جاري تحميل معلومات الملفات...</h3>
                        <p>يرجى الانتظار...</p>
                    </div>
                </div>
            </div>

            <div class="instructions">
                <h3>📋 تعليمات التثبيت والاستخدام</h3>
                <ol>
                    <li><strong>تحميل أداة التحكم:</strong> اضغط على زر "تحميل" بجانب "أداة التحكم في السيرفر"</li>
                    <li><strong>تثبيت Python:</strong> تأكد من تثبيت Python 3.7 أو أحدث على جهازك</li>
                    <li><strong>تثبيت المتطلبات:</strong> قم بتشغيل الأمر <code>pip install requests</code></li>
                    <li><strong>تشغيل الأداة:</strong> قم بتشغيل الملف <code>python server_controller.py</code></li>
                    <li><strong>الاتصال:</strong> أدخل عنوان السيرفر وبيانات تسجيل الدخول</li>
                    <li><strong>التحكم:</strong> استخدم التبويبات المختلفة للتحكم في السيرفر</li>
                </ol>
            </div>
        </div>

        <div class="footer">
            <p>🎮 JO GAME TOOL Server - أداة التحكم في السيرفر</p>
            <p>تم التطوير بواسطة فريق JO GAME TOOL</p>
        </div>
    </div>

    <script>
        // تحميل معلومات الملفات
        async function loadDownloadInfo() {
            try {
                const response = await fetch('/api/tool/download/info');
                const data = await response.json();

                if (data.success) {
                    // تحديث معلومات السيرفر
                    document.getElementById('serverName').textContent = data.serverInfo.name;
                    document.getElementById('serverVersion').textContent = data.serverInfo.version;
                    document.getElementById('totalFiles').textContent = data.totalFiles;

                    // عرض الملفات
                    displayDownloads(data.downloads);
                } else {
                    showError('فشل في تحميل معلومات الملفات: ' + data.message);
                }
            } catch (error) {
                showError('خطأ في الاتصال بالسيرفر: ' + error.message);
            }
        }

        function displayDownloads(downloads) {
            const container = document.getElementById('downloadsContainer');
            container.innerHTML = '';

            downloads.forEach(download => {
                const card = createDownloadCard(download);
                container.appendChild(card);
            });
        }

        function createDownloadCard(download) {
            const card = document.createElement('div');
            card.className = 'download-card';

            const size = formatFileSize(download.size);
            const date = new Date(download.lastModified).toLocaleDateString('ar-EG');
            const authRequired = download.requiresAuth;

            card.innerHTML = `
                <h3>${download.name}</h3>
                <p>${download.description}</p>
                <div class="file-info">
                    <div><strong>اسم الملف:</strong> ${download.filename}</div>
                    <div><strong>الحجم:</strong> ${size}</div>
                    <div><strong>آخر تحديث:</strong> ${date}</div>
                    <div><strong>النوع:</strong> ${getTypeLabel(download.type)}</div>
                </div>
                <a href="${download.downloadUrl}" 
                   class="download-btn ${authRequired ? 'auth-required' : ''}" 
                   ${authRequired ? 'onclick="return handleAuthDownload(event, this)"' : ''}>
                    📥 تحميل ${download.filename}
                </a>
                ${authRequired ? '<div class="auth-note">⚠️ يتطلب تسجيل الدخول</div>' : ''}
            `;

            return card;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 بايت';
            const k = 1024;
            const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function getTypeLabel(type) {
            const types = {
                'python': 'ملف Python',
                'executable': 'ملف تنفيذي',
                'text': 'ملف نصي',
                'markdown': 'ملف توثيق',
                'batch': 'ملف تشغيل'
            };
            return types[type] || type;
        }

        function handleAuthDownload(event, element) {
            event.preventDefault();
            alert('هذا الملف يتطلب تسجيل الدخول. يرجى استخدام أداة التحكم للتحميل.');
            return false;
        }

        function showError(message) {
            const container = document.getElementById('downloadsContainer');
            container.innerHTML = `
                <div class="error">
                    <h3>❌ خطأ</h3>
                    <p>${message}</p>
                </div>
            `;
        }

        // تحميل المعلومات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', loadDownloadInfo);
    </script>
</body>
</html>
