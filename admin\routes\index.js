/**
 * مسارات قسم التحديثات والتحكم
 */
const express = require('express');
const router = express.Router();
const { authenticate, authorize } = require('../../middleware/auth');
const logger = require('../../utils/logger');

// استيراد وحدات التحكم الإدارية
const adminController = require('../controllers/adminController');

/**
 * @route   GET /api/admin/status
 * @desc    الحصول على حالة النظام
 * @access  خاص (يتطلب مصادقة ودور المسؤول)
 */
router.get('/status', authenticate, authorize('admin'), adminController.getSystemStatus);

/**
 * @route   POST /api/admin/tool/control
 * @desc    التحكم في تشغيل/إيقاف الأداة
 * @access  خاص (يتطلب مصادقة ودور المسؤول)
 */
router.post('/tool/control', authenticate, authorize('admin'), adminController.controlTool);

/**
 * @route   POST /api/admin/update
 * @desc    تحديث النظام
 * @access  خاص (يتطلب مصادقة ودور المسؤول)
 */
router.post('/update', authenticate, authorize('admin'), adminController.installUpdate);

/**
 * @route   GET /api/admin/updates/check
 * @desc    التحقق من وجود تحديثات جديدة
 * @access  خاص (يتطلب مصادقة ودور المسؤول)
 */
router.get('/updates/check', authenticate, authorize('admin'), adminController.checkUpdates);

/**
 * @route   POST /api/admin/updates/cancel
 * @desc    إلغاء التحديث
 * @access  خاص (يتطلب مصادقة ودور المسؤول)
 */
router.post('/updates/cancel', authenticate, authorize('admin'), adminController.cancelUpdate);

/**
 * @route   GET /api/admin/logs
 * @desc    الحصول على سجلات النظام
 * @access  خاص (يتطلب مصادقة ودور المسؤول)
 */
router.get('/logs', authenticate, authorize('admin'), adminController.getSystemLogs);

module.exports = router;
