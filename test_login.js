const axios = require('axios');

async function testLogin() {
  try {
    console.log('🔐 اختبار تسجيل الدخول...');
    
    const response = await axios.post('http://localhost:3000/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    console.log('✅ نجح تسجيل الدخول!');
    console.log('📋 البيانات:', response.data);
    
  } catch (error) {
    console.error('❌ فشل تسجيل الدخول:');
    console.error('Status:', error.response?.status);
    console.error('Message:', error.response?.data?.message || error.message);
    console.error('Full response:', error.response?.data);
  }
}

testLogin();
