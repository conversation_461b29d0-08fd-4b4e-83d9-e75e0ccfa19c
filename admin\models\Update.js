/**
 * نموذج بيانات التحديث
 */
class Update {
  constructor(id, version, releaseNotes, files, isRequired) {
    this.id = id;
    this.version = version;
    this.releaseNotes = releaseNotes || '';
    this.files = files || [];
    this.isRequired = isRequired || false;
    this.size = this.calculateSize();
    this.status = 'available'; // 'available', 'downloading', 'installing', 'completed', 'failed'
    this.progress = 0;
    this.createdAt = new Date();
    this.releasedAt = new Date();
    this.installedAt = null;
    this.error = null;
  }
  
  /**
   * حساب حجم التحديث
   */
  calculateSize() {
    return this.files.reduce((total, file) => total + file.size, 0);
  }
  
  /**
   * بدء تنزيل التحديث
   */
  startDownload() {
    this.status = 'downloading';
    this.progress = 0;
    this.error = null;
    
    return {
      success: true,
      message: `بدء تنزيل التحديث ${this.version}`,
      update: this.getInfo(),
      timestamp: new Date()
    };
  }
  
  /**
   * تحديث تقدم التنزيل
   * @param {number} progress - نسبة التقدم (0-100)
   */
  updateDownloadProgress(progress) {
    this.progress = Math.min(100, Math.max(0, progress));
    
    if (this.progress === 100 && this.status === 'downloading') {
      this.status = 'installing';
      this.progress = 0;
    }
    
    return {
      success: true,
      message: `تم تحديث تقدم التنزيل إلى ${this.progress}%`,
      update: this.getInfo(),
      timestamp: new Date()
    };
  }
  
  /**
   * تحديث تقدم التثبيت
   * @param {number} progress - نسبة التقدم (0-100)
   */
  updateInstallProgress(progress) {
    this.progress = Math.min(100, Math.max(0, progress));
    
    if (this.progress === 100 && this.status === 'installing') {
      this.status = 'completed';
      this.installedAt = new Date();
    }
    
    return {
      success: true,
      message: `تم تحديث تقدم التثبيت إلى ${this.progress}%`,
      update: this.getInfo(),
      timestamp: new Date()
    };
  }
  
  /**
   * تسجيل فشل التحديث
   * @param {string} errorMessage - رسالة الخطأ
   */
  fail(errorMessage) {
    this.status = 'failed';
    this.error = errorMessage;
    
    return {
      success: false,
      message: `فشل التحديث: ${errorMessage}`,
      update: this.getInfo(),
      timestamp: new Date()
    };
  }
  
  /**
   * إلغاء التحديث
   */
  cancel() {
    if (this.status === 'downloading' || this.status === 'installing') {
      this.status = 'available';
      this.progress = 0;
      
      return {
        success: true,
        message: 'تم إلغاء التحديث',
        update: this.getInfo(),
        timestamp: new Date()
      };
    }
    
    return {
      success: false,
      message: `لا يمكن إلغاء التحديث في الحالة الحالية: ${this.status}`,
      update: this.getInfo(),
      timestamp: new Date()
    };
  }
  
  /**
   * الحصول على معلومات التحديث
   */
  getInfo() {
    return {
      id: this.id,
      version: this.version,
      releaseNotes: this.releaseNotes,
      filesCount: this.files.length,
      size: this.formatSize(this.size),
      isRequired: this.isRequired,
      status: this.status,
      progress: this.progress,
      createdAt: this.createdAt,
      releasedAt: this.releasedAt,
      installedAt: this.installedAt,
      error: this.error
    };
  }
  
  /**
   * تنسيق حجم الملف
   * @param {number} bytes - الحجم بالبايت
   */
  formatSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

module.exports = Update;
