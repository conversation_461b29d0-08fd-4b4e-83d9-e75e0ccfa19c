/**
 * وحدة التحكم بالأداة
 */
const Tool = require('../models/Tool');
const logger = require('../../utils/logger');
const gfxService = require('../services/gfxService');
const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// إنشاء مثيل تجريبي للأداة مع دعم GFX
const defaultTool = new Tool(
  1,
  'JO GAME TOOL - PUBG Mobile Graphics Optimizer',
  '2.0.0',
  'stopped',
  {
    autoStart: false,
    logLevel: 'info',
    maxMemory: 512,
    timeout: 30000,
    gfxEnabled: true,
    adbPath: './platform-tools/adb.exe',
    executablePath: './app/tools/gfx/JO_GAME_TOOL.exe',
    pythonPath: './app/tools/gfx/GFX.py',
    workingDirectory: './app/tools/gfx'
  },
  [
    { id: 1, name: 'GFX.py', size: 45821, lastUpdated: new Date(), path: './app/tools/gfx/GFX.py' },
    { id: 2, name: 'GUI.py', size: 12458, lastUpdated: new Date(), path: './app/tools/gfx/GUI.py' },
    { id: 3, name: 'JO_GAME_TOOL.exe', size: 15728640, lastUpdated: new Date(), path: './app/tools/gfx/JO_GAME_TOOL.exe' },
    { id: 4, name: 'platform-tools/adb.exe', size: 1258291, lastUpdated: new Date(), path: './app/tools/gfx/platform-tools/adb.exe' },
    { id: 5, name: 'assets/', size: 2048000, lastUpdated: new Date(), path: './app/tools/gfx/assets/' },
    { id: 6, name: 'README.md', size: 4096, lastUpdated: new Date(), path: './app/tools/gfx/README.md' }
  ]
);

// متغيرات إدارة العملية
let gfxProcess = null;
let gfxProcessId = null;

/**
 * الحصول على حالة الأداة
 */
exports.getStatus = (req, res) => {
  logger.info('تم طلب حالة الأداة');

  const toolInfo = defaultTool.getInfo();

  res.json({
    success: true,
    message: 'تم استرجاع حالة الأداة بنجاح',
    tool: toolInfo
  });
};

/**
 * تشغيل الأداة
 */
exports.startTool = (req, res) => {
  logger.info(`تم طلب تشغيل الأداة بواسطة المستخدم: ${req.user.username}`);

  const result = defaultTool.start();

  res.json(result);
};

/**
 * إيقاف الأداة
 */
exports.stopTool = (req, res) => {
  logger.info(`تم طلب إيقاف الأداة بواسطة المستخدم: ${req.user.username}`);

  const result = defaultTool.stop();

  res.json(result);
};

/**
 * تنفيذ أمر في الأداة
 */
exports.executeCommand = (req, res) => {
  const { command } = req.body;

  if (!command) {
    return res.status(400).json({
      success: false,
      message: 'الأمر مطلوب'
    });
  }

  logger.info(`تم طلب تنفيذ أمر في الأداة بواسطة المستخدم: ${req.user.username}`, {
    command
  });

  const result = defaultTool.executeCommand(command);

  res.json(result);
};

/**
 * تحديث إعدادات الأداة
 */
exports.updateConfig = (req, res) => {
  const { config } = req.body;

  if (!config || typeof config !== 'object') {
    return res.status(400).json({
      success: false,
      message: 'الإعدادات مطلوبة وتجب أن تكون كائن'
    });
  }

  logger.info(`تم طلب تحديث إعدادات الأداة بواسطة المستخدم: ${req.user.username}`, {
    config
  });

  const result = defaultTool.updateConfig(config);

  res.json(result);
};

/**
 * الحصول على قائمة ملفات الأداة
 */
exports.getFiles = (req, res) => {
  logger.info(`تم طلب قائمة ملفات الأداة بواسطة المستخدم: ${req.user.username}`);

  res.json({
    success: true,
    message: 'تم استرجاع قائمة ملفات الأداة بنجاح',
    files: defaultTool.files
  });
};

/**
 * إضافة ملف للأداة
 */
exports.addFile = (req, res) => {
  const { name, size, content } = req.body;

  if (!name || !size) {
    return res.status(400).json({
      success: false,
      message: 'اسم الملف وحجمه مطلوبان'
    });
  }

  logger.info(`تم طلب إضافة ملف للأداة بواسطة المستخدم: ${req.user.username}`, {
    name,
    size
  });

  const result = defaultTool.addFile({
    name,
    size,
    content,
    lastUpdated: new Date()
  });

  res.json(result);
};

/**
 * حذف ملف من الأداة
 */
exports.removeFile = (req, res) => {
  const { fileId } = req.params;

  if (!fileId) {
    return res.status(400).json({
      success: false,
      message: 'معرف الملف مطلوب'
    });
  }

  logger.info(`تم طلب حذف ملف من الأداة بواسطة المستخدم: ${req.user.username}`, {
    fileId
  });

  const result = defaultTool.removeFile(parseInt(fileId));

  res.json(result);
};

// ===== وظائف GFX الجديدة =====

/**
 * الاتصال بـ ADB
 */
exports.connectADB = async (req, res) => {
  try {
    logger.info(`تم طلب الاتصال بـ ADB بواسطة المستخدم: ${req.user.username}`);

    const { deviceId } = req.body;

    // استخدام خدمة GFX للاتصال
    const result = await gfxService.connectToDevice(deviceId);

    // تحديث حالة الأداة
    if (result.success) {
      defaultTool.adbConnection.isConnected = true;
      defaultTool.adbConnection.deviceId = result.device.id;
      defaultTool.adbConnection.lastConnectionCheck = new Date();
      defaultTool.gfxSettings.isConnected = true;
      defaultTool.gfxSettings.adbDevice = result.device.id;
      defaultTool.updatedAt = new Date();
    }

    res.json(result);
  } catch (error) {
    logger.error(`فشل الاتصال بـ ADB: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `فشل الاتصال بـ ADB: ${error.message}`,
      timestamp: new Date()
    });
  }
};

/**
 * قطع الاتصال بـ ADB
 */
exports.disconnectADB = (req, res) => {
  logger.info(`تم طلب قطع الاتصال بـ ADB بواسطة المستخدم: ${req.user.username}`);

  const result = defaultTool.disconnectADB();

  res.json(result);
};

/**
 * قراءة إعدادات الرسومات من الجهاز
 */
exports.getGraphicsSettings = async (req, res) => {
  try {
    logger.info(`تم طلب قراءة إعدادات الرسومات بواسطة المستخدم: ${req.user.username}`);

    if (!defaultTool.adbConnection.isConnected) {
      return res.status(400).json({
        success: false,
        message: 'لا يوجد اتصال بالمحاكي. يرجى الاتصال بـ ADB أولاً',
        timestamp: new Date()
      });
    }

    // قراءة الإعدادات من الجهاز باستخدام خدمة GFX
    const settings = await gfxService.readCurrentSettings(defaultTool.adbConnection.deviceId);

    // تحديث إعدادات الأداة
    defaultTool.gfxSettings = { ...defaultTool.gfxSettings, ...settings };
    defaultTool.updatedAt = new Date();

    res.json({
      success: true,
      message: 'تم قراءة إعدادات الرسومات بنجاح',
      settings: defaultTool.gfxSettings,
      timestamp: defaultTool.updatedAt
    });

  } catch (error) {
    logger.error(`فشل في قراءة إعدادات الرسومات: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `فشل في قراءة إعدادات الرسومات: ${error.message}`,
      timestamp: new Date()
    });
  }
};

/**
 * تحديث إعدادات الرسومات
 */
exports.updateGraphicsSettings = (req, res) => {
  const { graphics, framerate, style, sfx, shadow } = req.body;

  const newSettings = {};
  if (graphics) newSettings.graphics = graphics;
  if (framerate) newSettings.framerate = framerate;
  if (style) newSettings.style = style;
  if (sfx) newSettings.sfx = sfx;
  if (shadow) newSettings.shadow = shadow;

  if (Object.keys(newSettings).length === 0) {
    return res.status(400).json({
      success: false,
      message: 'يجب تحديد إعداد واحد على الأقل للتحديث'
    });
  }

  logger.info(`تم طلب تحديث إعدادات الرسومات بواسطة المستخدم: ${req.user.username}`, {
    newSettings
  });

  const result = defaultTool.updateGraphicsSettings(newSettings);

  res.json(result);
};

/**
 * تطبيق إعدادات الرسومات على الجهاز
 */
exports.applyGraphicsSettings = async (req, res) => {
  try {
    logger.info(`تم طلب تطبيق إعدادات الرسومات بواسطة المستخدم: ${req.user.username}`);

    if (!defaultTool.adbConnection.isConnected) {
      return res.status(400).json({
        success: false,
        message: 'لا يوجد اتصال بالمحاكي. يرجى الاتصال بـ ADB أولاً',
        timestamp: new Date()
      });
    }

    // تطبيق الإعدادات باستخدام خدمة GFX
    const result = await gfxService.applySettings(
      defaultTool.adbConnection.deviceId,
      defaultTool.gfxSettings
    );

    if (result.success) {
      defaultTool.updatedAt = new Date();
    }

    res.json({
      ...result,
      timestamp: new Date()
    });

  } catch (error) {
    logger.error(`فشل في تطبيق إعدادات الرسومات: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `فشل في تطبيق إعدادات الرسومات: ${error.message}`,
      timestamp: new Date()
    });
  }
};

/**
 * تشغيل PUBG Mobile
 */
exports.launchPUBG = async (req, res) => {
  try {
    logger.info(`تم طلب تشغيل PUBG Mobile بواسطة المستخدم: ${req.user.username}`);

    if (!defaultTool.adbConnection.isConnected) {
      return res.status(400).json({
        success: false,
        message: 'لا يوجد اتصال بالمحاكي. يرجى الاتصال بـ ADB أولاً',
        timestamp: new Date()
      });
    }

    // تشغيل PUBG Mobile باستخدام خدمة GFX
    const result = await gfxService.launchPUBGMobile(defaultTool.adbConnection.deviceId);

    res.json({
      ...result,
      timestamp: new Date()
    });

  } catch (error) {
    logger.error(`فشل في تشغيل PUBG Mobile: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `فشل في تشغيل PUBG Mobile: ${error.message}`,
      timestamp: new Date()
    });
  }
};

/**
 * الحصول على حالة الاتصال بـ ADB
 */
exports.getADBStatus = (req, res) => {
  logger.info(`تم طلب حالة الاتصال بـ ADB بواسطة المستخدم: ${req.user.username}`);

  res.json({
    success: true,
    message: 'تم استرجاع حالة الاتصال بـ ADB بنجاح',
    adbConnection: defaultTool.adbConnection,
    timestamp: new Date()
  });
};

/**
 * الحصول على الخيارات المتاحة لإعدادات الرسومات
 */
exports.getGraphicsOptions = (req, res) => {
  logger.info(`تم طلب الخيارات المتاحة لإعدادات الرسومات بواسطة المستخدم: ${req.user.username}`);

  const options = {
    graphics: ['Super Smooth', 'Smooth', 'Balanced', 'HD', 'HDR', 'Ultra HD'],
    framerate: ['Low', 'Medium', 'High', 'Ultra'],
    style: ['Classic', 'Colorful', 'Realistic', 'Soft', 'Movie'],
    shadow: ['Enable', 'Disable'],
    sfx: ['Low', 'Medium', 'High', 'Ultra']
  };

  res.json({
    success: true,
    message: 'تم استرجاع الخيارات المتاحة بنجاح',
    options,
    timestamp: new Date()
  });
};

/**
 * الحصول على قائمة الأجهزة المتصلة
 */
exports.getConnectedDevices = async (req, res) => {
  try {
    logger.info(`تم طلب قائمة الأجهزة المتصلة بواسطة المستخدم: ${req.user.username}`);

    const devices = await gfxService.getConnectedDevices();

    res.json({
      success: true,
      message: 'تم استرجاع قائمة الأجهزة المتصلة بنجاح',
      devices,
      count: devices.length,
      timestamp: new Date()
    });

  } catch (error) {
    logger.error(`فشل في الحصول على قائمة الأجهزة: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `فشل في الحصول على قائمة الأجهزة: ${error.message}`,
      timestamp: new Date()
    });
  }
};

// ===== وظائف إدارة GFX كبرنامج =====

/**
 * تشغيل برنامج GFX
 */
exports.startGFXProgram = async (req, res) => {
  try {
    logger.info(`تم طلب تشغيل برنامج GFX بواسطة المستخدم: ${req.user.username}`);

    // التحقق من وجود الملف التنفيذي
    const executablePath = defaultTool.config.executablePath;
    if (!fs.existsSync(executablePath)) {
      return res.status(404).json({
        success: false,
        message: 'ملف GFX التنفيذي غير موجود',
        path: executablePath,
        timestamp: new Date()
      });
    }

    // التحقق من أن البرنامج غير شغال بالفعل
    if (gfxProcess && !gfxProcess.killed) {
      return res.status(400).json({
        success: false,
        message: 'برنامج GFX شغال بالفعل',
        processId: gfxProcessId,
        timestamp: new Date()
      });
    }

    // تشغيل البرنامج
    gfxProcess = spawn(executablePath, [], {
      cwd: defaultTool.config.workingDirectory,
      detached: false,
      stdio: ['ignore', 'pipe', 'pipe']
    });

    gfxProcessId = gfxProcess.pid;
    defaultTool.status = 'running';
    defaultTool.updatedAt = new Date();

    // معالجة أحداث العملية
    gfxProcess.on('error', (error) => {
      logger.error(`خطأ في تشغيل GFX: ${error.message}`);
      defaultTool.status = 'error';
    });

    gfxProcess.on('exit', (code, signal) => {
      logger.info(`تم إغلاق GFX بالكود: ${code}, الإشارة: ${signal}`);
      defaultTool.status = 'stopped';
      gfxProcess = null;
      gfxProcessId = null;
    });

    logger.info(`تم تشغيل GFX بنجاح، معرف العملية: ${gfxProcessId}`);

    res.json({
      success: true,
      message: 'تم تشغيل برنامج GFX بنجاح',
      processId: gfxProcessId,
      status: defaultTool.status,
      timestamp: new Date()
    });

  } catch (error) {
    logger.error(`فشل في تشغيل GFX: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `فشل في تشغيل GFX: ${error.message}`,
      timestamp: new Date()
    });
  }
};

/**
 * إيقاف برنامج GFX
 */
exports.stopGFXProgram = async (req, res) => {
  try {
    logger.info(`تم طلب إيقاف برنامج GFX بواسطة المستخدم: ${req.user.username}`);

    if (!gfxProcess || gfxProcess.killed) {
      return res.status(400).json({
        success: false,
        message: 'برنامج GFX غير شغال',
        timestamp: new Date()
      });
    }

    // إيقاف العملية
    gfxProcess.kill('SIGTERM');

    // انتظار لمدة 5 ثواني، إذا لم يتم الإغلاق، استخدم SIGKILL
    setTimeout(() => {
      if (gfxProcess && !gfxProcess.killed) {
        gfxProcess.kill('SIGKILL');
        logger.warn('تم إجبار إغلاق GFX باستخدام SIGKILL');
      }
    }, 5000);

    defaultTool.status = 'stopped';
    defaultTool.updatedAt = new Date();
    gfxProcess = null;
    gfxProcessId = null;

    logger.info('تم إيقاف GFX بنجاح');

    res.json({
      success: true,
      message: 'تم إيقاف برنامج GFX بنجاح',
      status: defaultTool.status,
      timestamp: new Date()
    });

  } catch (error) {
    logger.error(`فشل في إيقاف GFX: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `فشل في إيقاف GFX: ${error.message}`,
      timestamp: new Date()
    });
  }
};

/**
 * إعادة تشغيل برنامج GFX
 */
exports.restartGFXProgram = async (req, res) => {
  try {
    logger.info(`تم طلب إعادة تشغيل برنامج GFX بواسطة المستخدم: ${req.user.username}`);

    // إيقاف البرنامج أولاً
    if (gfxProcess && !gfxProcess.killed) {
      gfxProcess.kill('SIGTERM');

      // انتظار الإغلاق
      await new Promise((resolve) => {
        const checkInterval = setInterval(() => {
          if (!gfxProcess || gfxProcess.killed) {
            clearInterval(checkInterval);
            resolve();
          }
        }, 100);

        // مهلة قصوى 10 ثواني
        setTimeout(() => {
          clearInterval(checkInterval);
          if (gfxProcess && !gfxProcess.killed) {
            gfxProcess.kill('SIGKILL');
          }
          resolve();
        }, 10000);
      });
    }

    // انتظار ثانية واحدة قبل إعادة التشغيل
    await new Promise(resolve => setTimeout(resolve, 1000));

    // تشغيل البرنامج مرة أخرى
    const executablePath = defaultTool.config.executablePath;
    gfxProcess = spawn(executablePath, [], {
      cwd: defaultTool.config.workingDirectory,
      detached: false,
      stdio: ['ignore', 'pipe', 'pipe']
    });

    gfxProcessId = gfxProcess.pid;
    defaultTool.status = 'running';
    defaultTool.updatedAt = new Date();

    // معالجة أحداث العملية
    gfxProcess.on('error', (error) => {
      logger.error(`خطأ في إعادة تشغيل GFX: ${error.message}`);
      defaultTool.status = 'error';
    });

    gfxProcess.on('exit', (code, signal) => {
      logger.info(`تم إغلاق GFX بعد إعادة التشغيل بالكود: ${code}, الإشارة: ${signal}`);
      defaultTool.status = 'stopped';
      gfxProcess = null;
      gfxProcessId = null;
    });

    logger.info(`تم إعادة تشغيل GFX بنجاح، معرف العملية: ${gfxProcessId}`);

    res.json({
      success: true,
      message: 'تم إعادة تشغيل برنامج GFX بنجاح',
      processId: gfxProcessId,
      status: defaultTool.status,
      timestamp: new Date()
    });

  } catch (error) {
    logger.error(`فشل في إعادة تشغيل GFX: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `فشل في إعادة تشغيل GFX: ${error.message}`,
      timestamp: new Date()
    });
  }
};

/**
 * الحصول على حالة برنامج GFX
 */
exports.getGFXProgramStatus = async (req, res) => {
  try {
    logger.info(`تم طلب حالة برنامج GFX بواسطة المستخدم: ${req.user.username}`);

    const isRunning = gfxProcess && !gfxProcess.killed;
    const status = isRunning ? 'running' : 'stopped';

    // تحديث حالة الأداة
    defaultTool.status = status;

    res.json({
      success: true,
      message: 'تم استرجاع حالة برنامج GFX بنجاح',
      status: status,
      processId: gfxProcessId,
      isRunning: isRunning,
      tool: defaultTool.getInfo(),
      timestamp: new Date()
    });

  } catch (error) {
    logger.error(`فشل في الحصول على حالة GFX: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `فشل في الحصول على حالة GFX: ${error.message}`,
      timestamp: new Date()
    });
  }
};

/**
 * تحديث ملفات GFX
 */
exports.updateGFXFiles = async (req, res) => {
  try {
    logger.info(`تم طلب تحديث ملفات GFX بواسطة المستخدم: ${req.user.username}`);

    const { fileId, fileContent } = req.body;

    if (!fileId || !fileContent) {
      return res.status(400).json({
        success: false,
        message: 'معرف الملف والمحتوى مطلوبان',
        timestamp: new Date()
      });
    }

    // البحث عن الملف
    const file = defaultTool.files.find(f => f.id === parseInt(fileId));
    if (!file) {
      return res.status(404).json({
        success: false,
        message: 'الملف غير موجود',
        timestamp: new Date()
      });
    }

    // إنشاء نسخة احتياطية
    const backupPath = `${file.path}.backup.${Date.now()}`;
    if (fs.existsSync(file.path)) {
      fs.copyFileSync(file.path, backupPath);
      logger.info(`تم إنشاء نسخة احتياطية: ${backupPath}`);
    }

    // كتابة المحتوى الجديد
    fs.writeFileSync(file.path, fileContent, 'utf8');

    // تحديث معلومات الملف
    file.lastUpdated = new Date();
    file.size = Buffer.byteLength(fileContent, 'utf8');
    defaultTool.updatedAt = new Date();

    logger.info(`تم تحديث الملف: ${file.name}`);

    res.json({
      success: true,
      message: `تم تحديث الملف ${file.name} بنجاح`,
      file: file,
      backupPath: backupPath,
      timestamp: new Date()
    });

  } catch (error) {
    logger.error(`فشل في تحديث ملف GFX: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `فشل في تحديث ملف GFX: ${error.message}`,
      timestamp: new Date()
    });
  }
};

/**
 * الحصول على سجلات الأداة
 */
exports.getLogs = (req, res) => {
  const { limit = 10, page = 1 } = req.query;

  logger.info(`تم طلب سجلات الأداة بواسطة المستخدم: ${req.user.username}`, {
    limit,
    page
  });

  // هنا سيتم استرجاع سجلات الأداة من قاعدة البيانات أو ملفات السجل
  const logs = Array(parseInt(limit)).fill().map((_, i) => ({
    id: i + 1 + (parseInt(page) - 1) * parseInt(limit),
    level: i % 3 === 0 ? 'error' : i % 2 === 0 ? 'warning' : 'info',
    message: `رسالة سجل تجريبية رقم ${i + 1 + (parseInt(page) - 1) * parseInt(limit)}`,
    timestamp: new Date(Date.now() - i * 60000)
  }));

  res.json({
    success: true,
    message: 'تم استرجاع سجلات الأداة بنجاح',
    logs,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: 10, // قيمة تجريبية
      totalItems: 100 // قيمة تجريبية
    }
  });
};
