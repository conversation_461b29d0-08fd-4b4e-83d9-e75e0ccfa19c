/**
 * وحدة التحكم بالأداة
 */
const Tool = require('../models/Tool');
const logger = require('../../utils/logger');
const gfxService = require('../services/gfxService');

// إنشاء مثيل تجريبي للأداة مع دعم GFX
const defaultTool = new Tool(
  1,
  'JO GAME TOOL - PUBG Mobile Graphics Optimizer',
  '2.0.0',
  'stopped',
  {
    autoStart: false,
    logLevel: 'info',
    maxMemory: 512,
    timeout: 30000,
    gfxEnabled: true,
    adbPath: './platform-tools/adb.exe'
  },
  [
    { id: 1, name: 'GFX.py', size: 45821, lastUpdated: new Date() },
    { id: 2, name: 'GUI.py', size: 12458, lastUpdated: new Date() },
    { id: 3, name: 'platform-tools/adb.exe', size: 1258291, lastUpdated: new Date() },
    { id: 4, name: 'assets/icons.png', size: 256000, lastUpdated: new Date() },
    { id: 5, name: 'config.json', size: 4096, lastUpdated: new Date() }
  ]
);

/**
 * الحصول على حالة الأداة
 */
exports.getStatus = (req, res) => {
  logger.info('تم طلب حالة الأداة');

  const toolInfo = defaultTool.getInfo();

  res.json({
    success: true,
    message: 'تم استرجاع حالة الأداة بنجاح',
    tool: toolInfo
  });
};

/**
 * تشغيل الأداة
 */
exports.startTool = (req, res) => {
  logger.info(`تم طلب تشغيل الأداة بواسطة المستخدم: ${req.user.username}`);

  const result = defaultTool.start();

  res.json(result);
};

/**
 * إيقاف الأداة
 */
exports.stopTool = (req, res) => {
  logger.info(`تم طلب إيقاف الأداة بواسطة المستخدم: ${req.user.username}`);

  const result = defaultTool.stop();

  res.json(result);
};

/**
 * تنفيذ أمر في الأداة
 */
exports.executeCommand = (req, res) => {
  const { command } = req.body;

  if (!command) {
    return res.status(400).json({
      success: false,
      message: 'الأمر مطلوب'
    });
  }

  logger.info(`تم طلب تنفيذ أمر في الأداة بواسطة المستخدم: ${req.user.username}`, {
    command
  });

  const result = defaultTool.executeCommand(command);

  res.json(result);
};

/**
 * تحديث إعدادات الأداة
 */
exports.updateConfig = (req, res) => {
  const { config } = req.body;

  if (!config || typeof config !== 'object') {
    return res.status(400).json({
      success: false,
      message: 'الإعدادات مطلوبة وتجب أن تكون كائن'
    });
  }

  logger.info(`تم طلب تحديث إعدادات الأداة بواسطة المستخدم: ${req.user.username}`, {
    config
  });

  const result = defaultTool.updateConfig(config);

  res.json(result);
};

/**
 * الحصول على قائمة ملفات الأداة
 */
exports.getFiles = (req, res) => {
  logger.info(`تم طلب قائمة ملفات الأداة بواسطة المستخدم: ${req.user.username}`);

  res.json({
    success: true,
    message: 'تم استرجاع قائمة ملفات الأداة بنجاح',
    files: defaultTool.files
  });
};

/**
 * إضافة ملف للأداة
 */
exports.addFile = (req, res) => {
  const { name, size, content } = req.body;

  if (!name || !size) {
    return res.status(400).json({
      success: false,
      message: 'اسم الملف وحجمه مطلوبان'
    });
  }

  logger.info(`تم طلب إضافة ملف للأداة بواسطة المستخدم: ${req.user.username}`, {
    name,
    size
  });

  const result = defaultTool.addFile({
    name,
    size,
    content,
    lastUpdated: new Date()
  });

  res.json(result);
};

/**
 * حذف ملف من الأداة
 */
exports.removeFile = (req, res) => {
  const { fileId } = req.params;

  if (!fileId) {
    return res.status(400).json({
      success: false,
      message: 'معرف الملف مطلوب'
    });
  }

  logger.info(`تم طلب حذف ملف من الأداة بواسطة المستخدم: ${req.user.username}`, {
    fileId
  });

  const result = defaultTool.removeFile(parseInt(fileId));

  res.json(result);
};

// ===== وظائف GFX الجديدة =====

/**
 * الاتصال بـ ADB
 */
exports.connectADB = async (req, res) => {
  try {
    logger.info(`تم طلب الاتصال بـ ADB بواسطة المستخدم: ${req.user.username}`);

    const { deviceId } = req.body;

    // استخدام خدمة GFX للاتصال
    const result = await gfxService.connectToDevice(deviceId);

    // تحديث حالة الأداة
    if (result.success) {
      defaultTool.adbConnection.isConnected = true;
      defaultTool.adbConnection.deviceId = result.device.id;
      defaultTool.adbConnection.lastConnectionCheck = new Date();
      defaultTool.gfxSettings.isConnected = true;
      defaultTool.gfxSettings.adbDevice = result.device.id;
      defaultTool.updatedAt = new Date();
    }

    res.json(result);
  } catch (error) {
    logger.error(`فشل الاتصال بـ ADB: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `فشل الاتصال بـ ADB: ${error.message}`,
      timestamp: new Date()
    });
  }
};

/**
 * قطع الاتصال بـ ADB
 */
exports.disconnectADB = (req, res) => {
  logger.info(`تم طلب قطع الاتصال بـ ADB بواسطة المستخدم: ${req.user.username}`);

  const result = defaultTool.disconnectADB();

  res.json(result);
};

/**
 * قراءة إعدادات الرسومات من الجهاز
 */
exports.getGraphicsSettings = async (req, res) => {
  try {
    logger.info(`تم طلب قراءة إعدادات الرسومات بواسطة المستخدم: ${req.user.username}`);

    if (!defaultTool.adbConnection.isConnected) {
      return res.status(400).json({
        success: false,
        message: 'لا يوجد اتصال بالمحاكي. يرجى الاتصال بـ ADB أولاً',
        timestamp: new Date()
      });
    }

    // قراءة الإعدادات من الجهاز باستخدام خدمة GFX
    const settings = await gfxService.readCurrentSettings(defaultTool.adbConnection.deviceId);

    // تحديث إعدادات الأداة
    defaultTool.gfxSettings = { ...defaultTool.gfxSettings, ...settings };
    defaultTool.updatedAt = new Date();

    res.json({
      success: true,
      message: 'تم قراءة إعدادات الرسومات بنجاح',
      settings: defaultTool.gfxSettings,
      timestamp: defaultTool.updatedAt
    });

  } catch (error) {
    logger.error(`فشل في قراءة إعدادات الرسومات: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `فشل في قراءة إعدادات الرسومات: ${error.message}`,
      timestamp: new Date()
    });
  }
};

/**
 * تحديث إعدادات الرسومات
 */
exports.updateGraphicsSettings = (req, res) => {
  const { graphics, framerate, style, sfx, shadow } = req.body;

  const newSettings = {};
  if (graphics) newSettings.graphics = graphics;
  if (framerate) newSettings.framerate = framerate;
  if (style) newSettings.style = style;
  if (sfx) newSettings.sfx = sfx;
  if (shadow) newSettings.shadow = shadow;

  if (Object.keys(newSettings).length === 0) {
    return res.status(400).json({
      success: false,
      message: 'يجب تحديد إعداد واحد على الأقل للتحديث'
    });
  }

  logger.info(`تم طلب تحديث إعدادات الرسومات بواسطة المستخدم: ${req.user.username}`, {
    newSettings
  });

  const result = defaultTool.updateGraphicsSettings(newSettings);

  res.json(result);
};

/**
 * تطبيق إعدادات الرسومات على الجهاز
 */
exports.applyGraphicsSettings = async (req, res) => {
  try {
    logger.info(`تم طلب تطبيق إعدادات الرسومات بواسطة المستخدم: ${req.user.username}`);

    if (!defaultTool.adbConnection.isConnected) {
      return res.status(400).json({
        success: false,
        message: 'لا يوجد اتصال بالمحاكي. يرجى الاتصال بـ ADB أولاً',
        timestamp: new Date()
      });
    }

    // تطبيق الإعدادات باستخدام خدمة GFX
    const result = await gfxService.applySettings(
      defaultTool.adbConnection.deviceId,
      defaultTool.gfxSettings
    );

    if (result.success) {
      defaultTool.updatedAt = new Date();
    }

    res.json({
      ...result,
      timestamp: new Date()
    });

  } catch (error) {
    logger.error(`فشل في تطبيق إعدادات الرسومات: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `فشل في تطبيق إعدادات الرسومات: ${error.message}`,
      timestamp: new Date()
    });
  }
};

/**
 * تشغيل PUBG Mobile
 */
exports.launchPUBG = async (req, res) => {
  try {
    logger.info(`تم طلب تشغيل PUBG Mobile بواسطة المستخدم: ${req.user.username}`);

    if (!defaultTool.adbConnection.isConnected) {
      return res.status(400).json({
        success: false,
        message: 'لا يوجد اتصال بالمحاكي. يرجى الاتصال بـ ADB أولاً',
        timestamp: new Date()
      });
    }

    // تشغيل PUBG Mobile باستخدام خدمة GFX
    const result = await gfxService.launchPUBGMobile(defaultTool.adbConnection.deviceId);

    res.json({
      ...result,
      timestamp: new Date()
    });

  } catch (error) {
    logger.error(`فشل في تشغيل PUBG Mobile: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `فشل في تشغيل PUBG Mobile: ${error.message}`,
      timestamp: new Date()
    });
  }
};

/**
 * الحصول على حالة الاتصال بـ ADB
 */
exports.getADBStatus = (req, res) => {
  logger.info(`تم طلب حالة الاتصال بـ ADB بواسطة المستخدم: ${req.user.username}`);

  res.json({
    success: true,
    message: 'تم استرجاع حالة الاتصال بـ ADB بنجاح',
    adbConnection: defaultTool.adbConnection,
    timestamp: new Date()
  });
};

/**
 * الحصول على الخيارات المتاحة لإعدادات الرسومات
 */
exports.getGraphicsOptions = (req, res) => {
  logger.info(`تم طلب الخيارات المتاحة لإعدادات الرسومات بواسطة المستخدم: ${req.user.username}`);

  const options = {
    graphics: ['Super Smooth', 'Smooth', 'Balanced', 'HD', 'HDR', 'Ultra HD'],
    framerate: ['Low', 'Medium', 'High', 'Ultra'],
    style: ['Classic', 'Colorful', 'Realistic', 'Soft', 'Movie'],
    shadow: ['Enable', 'Disable'],
    sfx: ['Low', 'Medium', 'High', 'Ultra']
  };

  res.json({
    success: true,
    message: 'تم استرجاع الخيارات المتاحة بنجاح',
    options,
    timestamp: new Date()
  });
};

/**
 * الحصول على قائمة الأجهزة المتصلة
 */
exports.getConnectedDevices = async (req, res) => {
  try {
    logger.info(`تم طلب قائمة الأجهزة المتصلة بواسطة المستخدم: ${req.user.username}`);

    const devices = await gfxService.getConnectedDevices();

    res.json({
      success: true,
      message: 'تم استرجاع قائمة الأجهزة المتصلة بنجاح',
      devices,
      count: devices.length,
      timestamp: new Date()
    });

  } catch (error) {
    logger.error(`فشل في الحصول على قائمة الأجهزة: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `فشل في الحصول على قائمة الأجهزة: ${error.message}`,
      timestamp: new Date()
    });
  }
};

/**
 * الحصول على سجلات الأداة
 */
exports.getLogs = (req, res) => {
  const { limit = 10, page = 1 } = req.query;

  logger.info(`تم طلب سجلات الأداة بواسطة المستخدم: ${req.user.username}`, {
    limit,
    page
  });

  // هنا سيتم استرجاع سجلات الأداة من قاعدة البيانات أو ملفات السجل
  const logs = Array(parseInt(limit)).fill().map((_, i) => ({
    id: i + 1 + (parseInt(page) - 1) * parseInt(limit),
    level: i % 3 === 0 ? 'error' : i % 2 === 0 ? 'warning' : 'info',
    message: `رسالة سجل تجريبية رقم ${i + 1 + (parseInt(page) - 1) * parseInt(limit)}`,
    timestamp: new Date(Date.now() - i * 60000)
  }));

  res.json({
    success: true,
    message: 'تم استرجاع سجلات الأداة بنجاح',
    logs,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: 10, // قيمة تجريبية
      totalItems: 100 // قيمة تجريبية
    }
  });
};
