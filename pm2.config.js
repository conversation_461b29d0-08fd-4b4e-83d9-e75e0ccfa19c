module.exports = {
  apps: [
    {
      // معلومات أساسية
      name: 'secure-server',
      script: 'server.js',
      instances: 'max', // استخدام جميع وحدات المعالجة المتاحة
      exec_mode: 'cluster', // وضع المجموعة لتوزيع الحمل

      // إعدادات إعادة التشغيل
      autorestart: true, // إعادة التشغيل التلقائي عند الفشل
      watch: false, // عدم مراقبة الملفات للتغييرات في الإنتاج
      max_memory_restart: '2G', // إعادة التشغيل إذا تجاوز استخدام الذاكرة 2GB
      max_restart: 20, // الحد الأقصى لعدد مرات إعادة التشغيل
      restart_delay: 1000, // تأخير إعادة التشغيل (بالمللي ثانية)
      exp_backoff_restart_delay: 50, // تأخير إعادة التشغيل التدريجي

      // إعدادات الأداء
      node_args: [
        '--max-old-space-size=4096', // زيادة حجم الذاكرة المتاحة لـ Node.js
        '--optimize-for-size', // تحسين لحجم الذاكرة
        '--max-http-header-size=16384', // زيادة حجم رأس HTTP
        '--no-warnings', // تعطيل التحذيرات
        '--expose-gc', // السماح بجمع القمامة اليدوي
        '--nouse-idle-notification', // تعطيل إشعارات الخمول
        '--max-semi-space-size=64' // تحسين جامع القمامة
      ],

      // إعدادات المهلة
      listen_timeout: 5000, // مهلة الاستماع (بالمللي ثانية)
      kill_timeout: 3000, // مهلة القتل (بالمللي ثانية)
      wait_ready: true, // انتظار إشارة "ready" من التطبيق
      shutdown_with_message: true, // إرسال رسالة عند الإغلاق

      // إعدادات البيئة
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        UV_THREADPOOL_SIZE: 128, // زيادة حجم مجموعة المواضيع
        NODE_OPTIONS: '--max-http-header-size=16384 --no-warnings',
        NODE_CLUSTER_SCHED_POLICY: 'rr' // سياسة جدولة المجموعة (round-robin)
      },
      env_development: {
        NODE_ENV: 'development',
        PORT: 3000,
        DEBUG: 'express:*'
      },

      // إعدادات السجلات
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      error_file: './logs/pm2-error.log',
      out_file: './logs/pm2-out.log',
      merge_logs: true,
      log_type: 'json', // تنسيق السجلات JSON للتحليل السهل

      // إعدادات المراقبة
      instance_var: 'INSTANCE_ID', // متغير معرف المثيل
      increment_var: 'PORT', // زيادة المنفذ لكل مثيل
      deep_monitoring: true, // تمكين المراقبة العميقة
      trace: true, // تمكين تتبع الأخطاء

      // إعدادات متقدمة
      disable_trace: false, // تمكين تتبع الأخطاء
      source_map_support: true, // دعم خرائط المصدر
      disable_logs: false, // تمكين السجلات
      cron_restart: '0 3 * * *', // إعادة التشغيل المجدولة (3 صباحًا كل يوم)
      vizion: true, // مراقبة التغييرات في Git
      force: true, // فرض إعادة التشغيل
      ignore_watch: ['node_modules', 'logs', '.git'], // تجاهل مراقبة هذه المجلدات
      append_env_to_name: true // إضافة البيئة إلى اسم العملية
    }
  ]
};
