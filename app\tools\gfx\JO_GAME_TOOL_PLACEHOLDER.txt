# 📁 ملف GFX التنفيذي

## 📋 معلومات الملف:
- **الاسم:** JO_GAME_TOOL.exe
- **النوع:** ملف تنفيذي Windows
- **الحجم:** ~15 ميجابايت
- **الوصف:** أداة تحسين رسومات PUBG Mobile

## 📍 المسار المطلوب:
```
app/tools/gfx/JO_GAME_TOOL.exe
```

## 🔧 كيفية الحصول على الملف:
1. انسخ ملف `JO_GAME_TOOL.exe` من مجلد `GFX/dist/`
2. ضعه في هذا المجلد: `app/tools/gfx/`
3. احذف هذا الملف النصي

## 🚀 بعد وضع الملف:
- سيتمكن السيرفر من تشغيل البرنامج
- ستعمل جميع وظائف إدارة GFX
- يمكن التحكم في البرنامج عن طريق API

## 🌐 API Endpoints للتحكم:
- `POST /api/tool/gfx/start` - تشغيل البرنامج
- `POST /api/tool/gfx/stop` - إيقاف البرنامج
- `POST /api/tool/gfx/restart` - إعادة تشغيل البرنامج
- `GET /api/tool/gfx/status` - حالة البرنامج

## ⚠️ ملاحظة مهمة:
هذا الملف مجرد placeholder. يجب استبداله بالملف التنفيذي الحقيقي.
