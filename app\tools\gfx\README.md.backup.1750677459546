# 🎮 GFX Tool - PUBG Mobile Graphics Optimizer

## 📋 نظرة عامة
أداة تحسين رسومات PUBG Mobile المدمجة في السيرفر.

## 📁 محتويات المجلد:
- `GFX.py` - الكود الأساسي للأداة
- `GUI.py` - واجهة المستخدم PyQt5
- `JO_GAME_TOOL.exe` - الملف التنفيذي
- `assets/` - الصور والأيقونات
- `platform-tools/` - أدوات ADB
- `build/` - ملفات البناء

## 🔧 إدارة الأداة:
يمكن إدارة هذه الأداة من خلال:
- تشغيل/إيقاف البرنامج
- تحديث الملفات
- مراقبة الحالة
- إدارة الإعدادات

## 🌐 API Endpoints:
- `POST /api/tool/gfx/start` - تشغيل الأداة
- `POST /api/tool/gfx/stop` - إيقاف الأداة
- `GET /api/tool/gfx/status` - حالة الأداة
- `PUT /api/tool/gfx/update` - تحديث الأداة
- `GET /api/tool/gfx/logs` - سجلات الأداة
