# -*- mode: python ; coding: utf-8 -*-

import os
import sys

# إضافة المسار الحالي
current_dir = os.path.dirname(os.path.abspath('GFX.py'))

a = Analysis(
    ['GFX.py'],
    pathex=[current_dir],
    binaries=[],
    datas=[
        ('assets', 'assets'),
        ('platform-tools', 'platform-tools'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'PIL',
        'PIL.Image',
        'subprocess',
        'threading',
        'os',
        'sys',
        'time'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='JO_GAME_TOOL',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
    version_info=None,
)
