@echo off
chcp 65001 >nul
title 🎮 أداة التحكم في السيرفر - Server Controller

echo.
echo ========================================
echo 🎮 أداة التحكم في السيرفر
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.7 أو أحدث
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM التحقق من وجود المكتبات المطلوبة
echo 🔍 التحقق من المكتبات المطلوبة...
python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ مكتبة requests غير مثبتة
    echo 📦 تثبيت المكتبات المطلوبة...
    pip install requests
    if errorlevel 1 (
        echo ❌ فشل تثبيت المكتبات
        pause
        exit /b 1
    )
)

echo ✅ جميع المكتبات متوفرة

REM التحقق من وجود ملف الأداة
if not exist "server_controller.py" (
    echo ❌ ملف server_controller.py غير موجود
    echo يرجى التأكد من وجود الملف في نفس المجلد
    pause
    exit /b 1
)

echo ✅ ملف الأداة موجود

echo.
echo 🚀 تشغيل أداة التحكم في السيرفر...
echo.

REM تشغيل الأداة
python server_controller.py

echo.
echo 👋 تم إغلاق أداة التحكم في السيرفر
pause
