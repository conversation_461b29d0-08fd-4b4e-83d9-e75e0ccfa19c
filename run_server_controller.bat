@echo off
chcp 65001 >nul
title 🎮 أداة التحكم في السيرفر الاحترافية - Server Controller Professional

echo.
echo ========================================
echo 🎮 أداة التحكم في السيرفر الاحترافية
echo    Server Controller Professional v2.0
echo ========================================
echo.

REM التحقق من وجود Python
echo 🔍 فحص متطلبات النظام...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo 📥 يرجى تثبيت Python 3.7 أو أحدث من:
    echo    https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM عرض إصدار Python
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo 🐍 إصدار Python: %PYTHON_VERSION%

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متوفر
    echo يرجى تثبيت pip أولاً
    pause
    exit /b 1
)

echo ✅ pip متوفر

REM التحقق من المكتبات المطلوبة
echo 🔍 فحص المكتبات المطلوبة...

REM فحص requests
python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ مكتبة requests غير مثبتة
    echo 📦 تثبيت requests...
    pip install requests>=2.28.0
    if errorlevel 1 (
        echo ❌ فشل تثبيت requests
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت requests
)

REM فحص psutil
python -c "import psutil" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ مكتبة psutil غير مثبتة
    echo 📦 تثبيت psutil...
    pip install psutil>=5.8.0
    if errorlevel 1 (
        echo ❌ فشل تثبيت psutil
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت psutil
)

REM فحص tkinter
python -c "import tkinter" >nul 2>&1
if errorlevel 1 (
    echo ❌ مكتبة tkinter غير متوفرة
    echo 📥 يرجى تثبيت python-tk:
    echo    sudo apt-get install python3-tk (على Linux)
    echo    أو إعادة تثبيت Python مع tkinter (على Windows)
    pause
    exit /b 1
)

echo ✅ جميع المكتبات متوفرة

REM التحقق من وجود ملف الأداة
if not exist "server_controller.py" (
    echo ❌ ملف server_controller.py غير موجود
    echo 📁 يرجى التأكد من وجود الملف في نفس المجلد
    echo 📥 يمكنك تحميله من السيرفر عبر:
    echo    http://localhost:3000/download
    pause
    exit /b 1
)

echo ✅ ملف الأداة موجود

REM إنشاء المجلدات المطلوبة
if not exist "logs" mkdir logs
if not exist "backups" mkdir backups

echo ✅ تم إعداد المجلدات

echo.
echo 🚀 تشغيل أداة التحكم في السيرفر الاحترافية...
echo 📊 المميزات المتاحة:
echo    • واجهة احترافية متقدمة
echo    • قاعدة بيانات محلية
echo    • نظام سجلات متطور
echo    • نسخ احتياطية تلقائية
echo    • مراقبة الأداء المباشر
echo    • نظام أمان متكامل
echo.

REM تشغيل الأداة
python server_controller.py

echo.
echo 👋 تم إغلاق أداة التحكم في السيرفر الاحترافية
echo 💾 تم حفظ جميع البيانات والإعدادات
echo.
pause
