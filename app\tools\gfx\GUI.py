# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'login.ui'
#
# Created by: PyQt5 UI code generator 5.15.11
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_ALFGAMETOOL(object):
    def setupUi(self, ALFGAMETOOL):
        ALFGAMETOOL.setObjectName("ALFGAMETOOL")
        ALFGAMETOOL.resize(1262, 846)
        ALFGAMETOOL.setIconSize(QtCore.QSize(80, 80))
        self.centralwidget = QtWidgets.QWidget(ALFGAMETOOL)
        self.centralwidget.setObjectName("centralwidget")
        self.ADB = QtWidgets.QPushButton(self.centralwidget)
        self.ADB.setGeometry(QtCore.QRect(10, 800, 75, 31))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setWeight(75)
        self.ADB.setFont(font)
        self.ADB.setObjectName("ADB")
        self.Get_Graphics_file = QtWidgets.QPushButton(self.centralwidget)
        self.Get_Graphics_file.setGeometry(QtCore.QRect(870, 780, 181, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.Get_Graphics_file.setFont(font)
        self.Get_Graphics_file.setObjectName("Get_Graphics_file")
        self.Apply = QtWidgets.QPushButton(self.centralwidget)
        self.Apply.setGeometry(QtCore.QRect(1060, 780, 181, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.Apply.setFont(font)
        self.Apply.setObjectName("Apply")
        self.Graphics = QtWidgets.QLabel(self.centralwidget)
        self.Graphics.setGeometry(QtCore.QRect(20, 20, 91, 31))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setWeight(75)
        self.Graphics.setFont(font)
        self.Graphics.setObjectName("Graphics")
        self.super_smooth = QtWidgets.QPushButton(self.centralwidget)
        self.super_smooth.setGeometry(QtCore.QRect(20, 70, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.super_smooth.setFont(font)
        self.super_smooth.setObjectName("super_smooth")
        self.smooth = QtWidgets.QPushButton(self.centralwidget)
        self.smooth.setGeometry(QtCore.QRect(190, 70, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.smooth.setFont(font)
        self.smooth.setObjectName("smooth")
        self.Balansed = QtWidgets.QPushButton(self.centralwidget)
        self.Balansed.setGeometry(QtCore.QRect(360, 70, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.Balansed.setFont(font)
        self.Balansed.setObjectName("Balansed")
        self.Ultra_HD = QtWidgets.QPushButton(self.centralwidget)
        self.Ultra_HD.setGeometry(QtCore.QRect(870, 70, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.Ultra_HD.setFont(font)
        self.Ultra_HD.setObjectName("Ultra_HD")
        self.HDR = QtWidgets.QPushButton(self.centralwidget)
        self.HDR.setGeometry(QtCore.QRect(700, 70, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.HDR.setFont(font)
        self.HDR.setObjectName("HDR")
        self.HD = QtWidgets.QPushButton(self.centralwidget)
        self.HD.setGeometry(QtCore.QRect(530, 70, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.HD.setFont(font)
        self.HD.setObjectName("HD")
        
        # Frame Rate section
        self.Frame_Rate = QtWidgets.QLabel(self.centralwidget)
        self.Frame_Rate.setGeometry(QtCore.QRect(20, 150, 131, 21))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setWeight(75)
        self.Frame_Rate.setFont(font)
        self.Frame_Rate.setObjectName("Frame_Rate")
        
        # Frame Rate buttons
        self.Low = QtWidgets.QPushButton(self.centralwidget)
        self.Low.setGeometry(QtCore.QRect(20, 180, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.Low.setFont(font)
        self.Low.setObjectName("Low")
        
        self.Medium = QtWidgets.QPushButton(self.centralwidget)
        self.Medium.setGeometry(QtCore.QRect(190, 180, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.Medium.setFont(font)
        self.Medium.setObjectName("Medium")
        
        self.High = QtWidgets.QPushButton(self.centralwidget)
        self.High.setGeometry(QtCore.QRect(360, 180, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.High.setFont(font)
        self.High.setObjectName("High")
        
        self.Ultra = QtWidgets.QPushButton(self.centralwidget)
        self.Ultra.setGeometry(QtCore.QRect(530, 180, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.Ultra.setFont(font)
        self.Ultra.setObjectName("Ultra")
        
        self.Extreme = QtWidgets.QPushButton(self.centralwidget)
        self.Extreme.setGeometry(QtCore.QRect(700, 180, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.Extreme.setFont(font)
        self.Extreme.setObjectName("Extreme")
        
        self.Extreme_2 = QtWidgets.QPushButton(self.centralwidget)
        self.Extreme_2.setGeometry(QtCore.QRect(870, 180, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.Extreme_2.setFont(font)
        self.Extreme_2.setObjectName("Extreme_2")
        
        self.Ultra_Extreme = QtWidgets.QPushButton(self.centralwidget)
        self.Ultra_Extreme.setGeometry(QtCore.QRect(1040, 180, 161, 51))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.Ultra_Extreme.setFont(font)
        self.Ultra_Extreme.setObjectName("Ultra_Extreme")
        
        # Style section
        self.style = QtWidgets.QLabel(self.centralwidget)
        self.style.setGeometry(QtCore.QRect(20, 230, 81, 71))
        font = QtGui.QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setWeight(75)
        self.style.setFont(font)
        self.style.setObjectName("style")
        
        # PUBG Launch button
        self.Launch_PUBG_Mobile = QtWidgets.QPushButton(self.centralwidget)
        self.Launch_PUBG_Mobile.setGeometry(QtCore.QRect(130, 790, 51, 51))
        self.Launch_PUBG_Mobile.setText("")
        self.Launch_PUBG_Mobile.setObjectName("Launch_PUBG_Mobile")
        
        ALFGAMETOOL.setCentralWidget(self.centralwidget)

        self.retranslateUi(ALFGAMETOOL)
        QtCore.QMetaObject.connectSlotsByName(ALFGAMETOOL)

    def retranslateUi(self, ALFGAMETOOL):
        _translate = QtCore.QCoreApplication.translate
        ALFGAMETOOL.setWindowTitle(_translate("ALFGAMETOOL", "JO GAME TOOL - PUBG Mobile Graphics Optimizer"))
        self.ADB.setText(_translate("ALFGAMETOOL", "ADB"))
        self.Get_Graphics_file.setText(_translate("ALFGAMETOOL", "Get Graphics file"))
        self.Apply.setText(_translate("ALFGAMETOOL", "Apply"))
        self.Graphics.setText(_translate("ALFGAMETOOL", "Graphics"))
        self.super_smooth.setText(_translate("ALFGAMETOOL", "Super Smooth"))
        self.smooth.setText(_translate("ALFGAMETOOL", "Smooth"))
        self.Balansed.setText(_translate("ALFGAMETOOL", "Balanced"))
        self.Ultra_HD.setText(_translate("ALFGAMETOOL", "Ultra HD"))
        self.HDR.setText(_translate("ALFGAMETOOL", "HDR"))
        self.HD.setText(_translate("ALFGAMETOOL", "HD"))
        self.Frame_Rate.setText(_translate("ALFGAMETOOL", "Frame Rate"))
        self.Low.setText(_translate("ALFGAMETOOL", "Low"))
        self.Medium.setText(_translate("ALFGAMETOOL", "Medium"))
        self.High.setText(_translate("ALFGAMETOOL", "High"))
        self.Ultra.setText(_translate("ALFGAMETOOL", "Ultra"))
        self.Extreme.setText(_translate("ALFGAMETOOL", "Extreme"))
        self.Extreme_2.setText(_translate("ALFGAMETOOL", "Extreme+"))
        self.Ultra_Extreme.setText(_translate("ALFGAMETOOL", "Ultra Extreme"))
        self.style.setText(_translate("ALFGAMETOOL", "Style"))


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ALFGAMETOOL = QtWidgets.QMainWindow()
    ui = Ui_ALFGAMETOOL()
    ui.setupUi(ALFGAMETOOL)
    ALFGAMETOOL.show()
    sys.exit(app.exec_())
