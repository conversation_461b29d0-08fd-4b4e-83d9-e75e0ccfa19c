# سيرفر آمن ومشفر

سيرفر Node.js آمن ومشفر يعمل على مدار الساعة مع ميزات أمان متقدمة.

## الميزات

### 🔒 الأمان والحماية
- ✅ تشفير HTTPS
- ✅ مصادقة JWT
- ✅ حماية من هجمات CSRF
- ✅ حماية من هجمات XSS
- ✅ تقييد معدل الطلبات (Rate limiting)
- ✅ تشفير البيانات الحساسة

### 🛠️ الإدارة والمراقبة
- ✅ تسجيل الأحداث (Logging)
- ✅ معالجة الأخطاء
- ✅ تشغيل مستمر باستخدام PM2
- ✅ ضغط HTTP
- ✅ دعم اللغات المتعددة (العربية، الإنجليزية، الفرنسية)

### 🎮 أداة GFX - محسن رسومات PUBG Mobile
- ✅ **تحسين رسومات PUBG Mobile** - تحكم كامل في إعدادات الرسومات
- ✅ **إدارة ADB** - اتصال وإدارة الأجهزة والمحاكيات
- ✅ **إعدادات متقدمة** - رسومات، معدل الإطارات، النمط، الصوت، الظلال
- ✅ **نسخ احتياطية تلقائية** - حماية الإعدادات الأصلية
- ✅ **دعم محاكيات متعددة** - BlueStacks، NoxPlayer، LDPlayer، MEmu
- ✅ **واجهة REST API** - تحكم كامل عبر HTTP requests
- ✅ **تشغيل تلقائي للعبة** - تشغيل PUBG Mobile مباشرة بعد تطبيق الإعدادات

## متطلبات النظام

- Node.js v18 أو أحدث
- npm v9 أو أحدث

## التثبيت

1. استنساخ المستودع:
```bash
git clone https://github.com/yourusername/secure-server.git
cd secure-server
```

2. تثبيت التبعيات:
```bash
npm install
```

3. إنشاء ملف `.env` (أو تعديل الملف الموجود):
```
PORT=3000
NODE_ENV=production
JWT_SECRET=your_super_secure_jwt_secret_key_change_this_in_production
JWT_EXPIRES_IN=1d
```

## التشغيل

### وضع التطوير
```bash
npm run dev
```

### وضع الإنتاج
```bash
npm run prod
```

هذا سيبدأ السيرفر باستخدام PM2 للتشغيل المستمر.

## الأمان

تم تصميم هذا السيرفر مع وضع الأمان في الاعتبار:

- **Helmet**: لتعزيز أمان HTTP headers
- **CORS**: للتحكم في الوصول من أصول مختلفة
- **Rate Limiting**: لمنع هجمات القوة الغاشمة
- **CSRF Protection**: لمنع هجمات تزوير الطلبات عبر المواقع
- **XSS Protection**: لمنع هجمات البرمجة النصية عبر المواقع
- **JWT Authentication**: للتحقق من هوية المستخدمين
- **HTTPS**: لتشفير الاتصال بين العميل والخادم
- **Encryption Utilities**: لتشفير البيانات الحساسة

## هيكل المشروع

```
secure-server/
├── app/                    # قسم الأداة (GFX)
│   ├── config/            # إعدادات GFX
│   ├── controllers/       # وحدات التحكم
│   ├── models/            # نماذج البيانات
│   ├── routes/            # مسارات API
│   ├── services/          # خدمات GFX
│   └── test_gfx_api.js    # ملف اختبار API
├── admin/                 # قسم التحديثات والتحكم
├── license/               # قسم بيانات الترخيص
├── config/                # ملفات الإعدادات العامة
├── logs/                  # سجلات السيرفر
├── middleware/            # وسائط Express
├── routes/                # مسارات API العامة
├── ssl/                   # شهادات SSL
├── utils/                 # أدوات مساعدة
├── locales/               # ملفات الترجمة
├── backups/               # النسخ الاحتياطية لـ GFX
├── .env                   # متغيرات البيئة
├── .gitignore             # ملفات مستثناة من Git
├── package.json           # تبعيات المشروع
├── README.md              # توثيق المشروع
└── server.js              # نقطة دخول السيرفر
```

## 🎮 استخدام أداة GFX

### المتطلبات الإضافية لـ GFX:
- **ADB (Android Debug Bridge)** - مثبت في مجلد `platform-tools/`
- **محاكي Android** أو جهاز Android متصل
- **PUBG Mobile** مثبت على الجهاز/المحاكي

### خطوات الاستخدام:

#### 1. تحضير البيئة
```bash
# تأكد من وجود ADB
./platform-tools/adb.exe devices

# تشغيل المحاكي أو توصيل الجهاز
```

#### 2. تشغيل السيرفر
```bash
npm start
# أو للتطوير
npm run dev
```

#### 3. تسجيل الدخول والحصول على التوكن
```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "admin123"}'
```

#### 4. استخدام GFX API

**الحصول على قائمة الأجهزة:**
```bash
curl -X GET http://localhost:3000/api/tool/adb/devices \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**الاتصال بجهاز:**
```bash
curl -X POST http://localhost:3000/api/tool/adb/connect \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"deviceId": "emulator-5554"}'
```

**قراءة إعدادات الرسومات:**
```bash
curl -X GET http://localhost:3000/api/tool/graphics/settings \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**تحديث إعدادات الرسومات:**
```bash
curl -X PUT http://localhost:3000/api/tool/graphics/settings \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "graphics": "Ultra HD",
    "framerate": "Ultra",
    "style": "Realistic",
    "shadow": "Enable"
  }'
```

**تطبيق الإعدادات:**
```bash
curl -X POST http://localhost:3000/api/tool/graphics/apply \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**تشغيل PUBG Mobile:**
```bash
curl -X POST http://localhost:3000/api/tool/pubg/launch \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**إدارة برنامج GFX:**
```bash
# تشغيل برنامج GFX
curl -X POST http://localhost:3000/api/tool/gfx/start \
  -H "Authorization: Bearer YOUR_TOKEN"

# إيقاف برنامج GFX
curl -X POST http://localhost:3000/api/tool/gfx/stop \
  -H "Authorization: Bearer YOUR_TOKEN"

# حالة برنامج GFX
curl -X GET http://localhost:3000/api/tool/gfx/status \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 🧪 اختبار API

يمكنك استخدام ملفات الاختبار المدمجة:
```bash
# اختبار GFX API
npm run test-gfx

# اختبار إدارة برنامج GFX
npm run test-gfx-management

# اختبار سريع للسيرفر
npm run quick-test
```

### 📚 التوثيق الكامل

للحصول على التوثيق الكامل لـ GFX API، راجع:
- `app/GFX_API_DOCUMENTATION.md` - توثيق شامل لجميع endpoints
- `app/config/gfx.js` - جميع الإعدادات القابلة للتخصيص

## المساهمة

المساهمات مرحب بها! يرجى اتباع هذه الخطوات:

1. Fork المستودع
2. إنشاء فرع للميزة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'إضافة ميزة رائعة'`)
4. Push إلى الفرع (`git push origin feature/amazing-feature`)
5. فتح طلب سحب

## الترخيص

هذا المشروع مرخص بموجب [MIT License](LICENSE).
