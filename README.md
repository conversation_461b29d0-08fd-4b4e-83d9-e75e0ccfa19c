# سيرفر آمن ومشفر

سيرفر Node.js آمن ومشفر يعمل على مدار الساعة مع ميزات أمان متقدمة.

## الميزات

- ✅ تشفير HTTPS
- ✅ مصادقة JWT
- ✅ حماية من هجمات CSRF
- ✅ حماية من هجمات XSS
- ✅ تقييد معدل الطلبات (Rate limiting)
- ✅ تشفير البيانات الحساسة
- ✅ تسجيل الأحداث (Logging)
- ✅ معالجة الأخطاء
- ✅ تشغيل مستمر باستخدام PM2
- ✅ ضغط HTTP

## متطلبات النظام

- Node.js v18 أو أحدث
- npm v9 أو أحدث

## التثبيت

1. استنساخ المستودع:
```bash
git clone https://github.com/yourusername/secure-server.git
cd secure-server
```

2. تثبيت التبعيات:
```bash
npm install
```

3. إنشاء ملف `.env` (أو تعديل الملف الموجود):
```
PORT=3000
NODE_ENV=production
JWT_SECRET=your_super_secure_jwt_secret_key_change_this_in_production
JWT_EXPIRES_IN=1d
```

## التشغيل

### وضع التطوير
```bash
npm run dev
```

### وضع الإنتاج
```bash
npm run prod
```

هذا سيبدأ السيرفر باستخدام PM2 للتشغيل المستمر.

## الأمان

تم تصميم هذا السيرفر مع وضع الأمان في الاعتبار:

- **Helmet**: لتعزيز أمان HTTP headers
- **CORS**: للتحكم في الوصول من أصول مختلفة
- **Rate Limiting**: لمنع هجمات القوة الغاشمة
- **CSRF Protection**: لمنع هجمات تزوير الطلبات عبر المواقع
- **XSS Protection**: لمنع هجمات البرمجة النصية عبر المواقع
- **JWT Authentication**: للتحقق من هوية المستخدمين
- **HTTPS**: لتشفير الاتصال بين العميل والخادم
- **Encryption Utilities**: لتشفير البيانات الحساسة

## هيكل المشروع

```
secure-server/
├── config/             # ملفات الإعدادات
├── logs/               # سجلات السيرفر
├── middleware/         # وسائط Express
├── routes/             # مسارات API
├── ssl/                # شهادات SSL
├── utils/              # أدوات مساعدة
├── .env                # متغيرات البيئة
├── .gitignore          # ملفات مستثناة من Git
├── package.json        # تبعيات المشروع
├── README.md           # توثيق المشروع
└── server.js           # نقطة دخول السيرفر
```

## المساهمة

المساهمات مرحب بها! يرجى اتباع هذه الخطوات:

1. Fork المستودع
2. إنشاء فرع للميزة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'إضافة ميزة رائعة'`)
4. Push إلى الفرع (`git push origin feature/amazing-feature`)
5. فتح طلب سحب

## الترخيص

هذا المشروع مرخص بموجب [MIT License](LICENSE).
