#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎮 وحدات أداة التحكم الاحترافية في السيرفر
Server Controller Professional Modules

هذا الملف يجعل مجلد modules حزمة Python قابلة للاستيراد
"""

__version__ = "3.0.0"
__author__ = "JO GAME TOOL Team"
__description__ = "Professional Server Controller Modules"

# استيراد الوحدات الرئيسية
try:
    from .ui_manager import UIManager
    from .database_manager import DatabaseManager
    from .security_manager import SecurityManager
    from .monitoring_manager import MonitoringManager
    from .backup_manager import BackupManager
    from .notification_manager import NotificationManager, ThemeManager, LanguageManager
    
    __all__ = [
        'UIManager',
        'DatabaseManager', 
        'SecurityManager',
        'MonitoringManager',
        'BackupManager',
        'NotificationManager',
        'ThemeManager',
        'LanguageManager'
    ]
    
except ImportError as e:
    print(f"⚠️ تحذير: فشل استيراد بعض الوحدات: {str(e)}")
    print("سيتم استخدام الوحدات الاحتياطية")
    
    # وحدات احتياطية بسيطة
    class FallbackManager:
        def __init__(self, parent):
            self.parent = parent
            
    UIManager = FallbackManager
    DatabaseManager = FallbackManager
    SecurityManager = FallbackManager
    MonitoringManager = FallbackManager
    BackupManager = FallbackManager
    NotificationManager = FallbackManager
    ThemeManager = FallbackManager
    LanguageManager = FallbackManager
    
    __all__ = ['FallbackManager']
