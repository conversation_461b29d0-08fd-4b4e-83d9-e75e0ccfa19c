/**
 * نموذج بيانات الأداة
 */
class Tool {
  constructor(id, name, version, status, config, files) {
    this.id = id;
    this.name = name;
    this.version = version;
    this.status = status; // 'running', 'stopped', 'error'
    this.config = config;
    this.files = files || [];
    this.createdAt = new Date();
    this.updatedAt = new Date();
  }
  
  /**
   * تشغيل الأداة
   */
  start() {
    this.status = 'running';
    this.updatedAt = new Date();
    return {
      success: true,
      message: 'تم تشغيل الأداة بنجاح',
      timestamp: this.updatedAt
    };
  }
  
  /**
   * إيقاف الأداة
   */
  stop() {
    this.status = 'stopped';
    this.updatedAt = new Date();
    return {
      success: true,
      message: 'تم إيقاف الأداة بنجاح',
      timestamp: this.updatedAt
    };
  }
  
  /**
   * تنفيذ أمر في الأداة
   * @param {string} command - الأمر المراد تنفيذه
   */
  executeCommand(command) {
    if (this.status !== 'running') {
      return {
        success: false,
        message: 'لا يمكن تنفيذ الأمر لأن الأداة غير مشغلة',
        timestamp: new Date()
      };
    }
    
    // هنا سيتم تنفيذ الأمر في الأداة
    
    return {
      success: true,
      message: 'تم تنفيذ الأمر بنجاح',
      result: `تم تنفيذ الأمر: ${command}`,
      timestamp: new Date()
    };
  }
  
  /**
   * تحديث إعدادات الأداة
   * @param {Object} newConfig - الإعدادات الجديدة
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    this.updatedAt = new Date();
    return {
      success: true,
      message: 'تم تحديث إعدادات الأداة بنجاح',
      config: this.config,
      timestamp: this.updatedAt
    };
  }
  
  /**
   * إضافة ملف للأداة
   * @param {Object} file - معلومات الملف
   */
  addFile(file) {
    this.files.push({
      ...file,
      id: this.files.length + 1,
      addedAt: new Date()
    });
    this.updatedAt = new Date();
    return {
      success: true,
      message: 'تم إضافة الملف بنجاح',
      file: this.files[this.files.length - 1],
      timestamp: this.updatedAt
    };
  }
  
  /**
   * حذف ملف من الأداة
   * @param {number} fileId - معرف الملف
   */
  removeFile(fileId) {
    const fileIndex = this.files.findIndex(file => file.id === fileId);
    
    if (fileIndex === -1) {
      return {
        success: false,
        message: 'الملف غير موجود',
        timestamp: new Date()
      };
    }
    
    const removedFile = this.files.splice(fileIndex, 1)[0];
    this.updatedAt = new Date();
    
    return {
      success: true,
      message: 'تم حذف الملف بنجاح',
      file: removedFile,
      timestamp: this.updatedAt
    };
  }
  
  /**
   * الحصول على معلومات الأداة
   */
  getInfo() {
    return {
      id: this.id,
      name: this.name,
      version: this.version,
      status: this.status,
      config: this.config,
      filesCount: this.files.length,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

module.exports = Tool;
