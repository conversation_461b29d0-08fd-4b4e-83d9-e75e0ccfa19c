#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
💾 مدير النسخ الاحتياطية الاحترافي - Professional Backup Manager
مسؤول عن إنشاء وإدارة النسخ الاحتياطية
"""

import os
import zipfile
import shutil
import hashlib
import threading
import time
from datetime import datetime, timedelta

class BackupManager:
    """مدير النسخ الاحتياطية الاحترافي"""
    
    def __init__(self, parent):
        """تهيئة مدير النسخ الاحتياطية"""
        self.parent = parent
        self.backup_active = False
        self.backup_thread = None
        
        self.parent.logger.info("💾 تم تهيئة مدير النسخ الاحتياطية الاحترافي")
        
    def create_backup(self, backup_type="manual", include_logs=True):
        """إنشاء نسخة احتياطية"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"backup_{backup_type}_{timestamp}.zip"
            backup_path = os.path.join("backups", backup_filename)
            
            # إنشاء ملف ZIP
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as backup_zip:
                # إضافة قاعدة البيانات
                if os.path.exists("data/server_controller_pro.db"):
                    backup_zip.write("data/server_controller_pro.db", "database/server_controller_pro.db")
                    
                # إضافة الإعدادات
                if os.path.exists("data/settings.json"):
                    backup_zip.write("data/settings.json", "settings/settings.json")
                    
                # إضافة مفتاح التشفير
                if os.path.exists("data/encryption.key"):
                    backup_zip.write("data/encryption.key", "security/encryption.key")
                    
                # إضافة السجلات (اختياري)
                if include_logs and os.path.exists("logs"):
                    for root, dirs, files in os.walk("logs"):
                        for file in files:
                            file_path = os.path.join(root, file)
                            archive_path = os.path.join("logs", os.path.relpath(file_path, "logs"))
                            backup_zip.write(file_path, archive_path)
                            
            # حساب checksum
            checksum = self.calculate_file_checksum(backup_path)
            file_size = os.path.getsize(backup_path)
            
            # حفظ معلومات النسخة الاحتياطية
            backup_id = self.parent.db_manager.record_backup(
                filename=backup_filename,
                file_path=backup_path,
                file_size=file_size,
                backup_type=backup_type,
                compression_type="zip",
                checksum=checksum
            )
            
            self.parent.logger.info(f"✅ تم إنشاء نسخة احتياطية: {backup_filename}")
            
            return backup_id, backup_path
            
        except Exception as e:
            self.parent.logger.error(f"❌ فشل إنشاء النسخة الاحتياطية: {str(e)}")
            return None, None
            
    def calculate_file_checksum(self, file_path):
        """حساب checksum للملف"""
        try:
            hash_sha256 = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception as e:
            self.parent.logger.error(f"❌ فشل حساب checksum: {str(e)}")
            return None
            
    def start_auto_backup(self):
        """بدء النسخ الاحتياطي التلقائي"""
        if not self.backup_active:
            self.backup_active = True
            self.backup_thread = threading.Thread(target=self.auto_backup_loop, daemon=True)
            self.backup_thread.start()
            self.parent.logger.info("▶️ تم بدء النسخ الاحتياطي التلقائي")
            
    def stop_auto_backup(self):
        """إيقاف النسخ الاحتياطي التلقائي"""
        self.backup_active = False
        if self.backup_thread:
            self.backup_thread.join(timeout=5)
        self.parent.logger.info("⏹️ تم إيقاف النسخ الاحتياطي التلقائي")
        
    def auto_backup_loop(self):
        """حلقة النسخ الاحتياطي التلقائي"""
        while self.backup_active:
            try:
                # إنشاء نسخة احتياطية تلقائية
                self.create_backup("automatic", include_logs=False)
                
                # تنظيف النسخ القديمة
                self.cleanup_old_backups()
                
                # انتظار حتى الموعد التالي (24 ساعة افتراضياً)
                backup_interval = self.parent.db_manager.get_setting("backup", "backup_interval", 24)
                time.sleep(backup_interval * 3600)
                
            except Exception as e:
                self.parent.logger.error(f"❌ خطأ في النسخ الاحتياطي التلقائي: {str(e)}")
                time.sleep(3600)  # انتظار ساعة في حالة الخطأ
                
    def cleanup_old_backups(self, keep_days=30):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            cutoff_date = datetime.now() - timedelta(days=keep_days)
            
            # الحصول على قائمة النسخ الاحتياطية
            backups = self.parent.db_manager.get_backups()
            
            for backup in backups:
                backup_date = datetime.fromisoformat(backup[8])  # created_at
                
                if backup_date < cutoff_date:
                    # حذف الملف
                    file_path = backup[2]  # file_path
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        
                    # حذف السجل من قاعدة البيانات
                    # سيتم إضافة وظيفة الحذف لاحقاً
                    
                    self.parent.logger.info(f"🗑️ تم حذف نسخة احتياطية قديمة: {backup[1]}")
                    
        except Exception as e:
            self.parent.logger.error(f"❌ فشل تنظيف النسخ الاحتياطية القديمة: {str(e)}")
            
    def restore_backup(self, backup_path):
        """استعادة نسخة احتياطية"""
        try:
            if not os.path.exists(backup_path):
                raise FileNotFoundError("ملف النسخة الاحتياطية غير موجود")
                
            # إنشاء نسخة احتياطية من الحالة الحالية قبل الاستعادة
            current_backup_id, _ = self.create_backup("pre_restore")
            
            # استخراج النسخة الاحتياطية
            with zipfile.ZipFile(backup_path, 'r') as backup_zip:
                backup_zip.extractall("temp_restore")
                
            # استعادة الملفات
            self.restore_files_from_temp()
            
            # تنظيف الملفات المؤقتة
            shutil.rmtree("temp_restore")
            
            self.parent.logger.info(f"✅ تم استعادة النسخة الاحتياطية: {backup_path}")
            
            return True
            
        except Exception as e:
            self.parent.logger.error(f"❌ فشل استعادة النسخة الاحتياطية: {str(e)}")
            return False
            
    def restore_files_from_temp(self):
        """استعادة الملفات من المجلد المؤقت"""
        try:
            # استعادة قاعدة البيانات
            temp_db = "temp_restore/database/server_controller_pro.db"
            if os.path.exists(temp_db):
                shutil.copy2(temp_db, "data/server_controller_pro.db")
                
            # استعادة الإعدادات
            temp_settings = "temp_restore/settings/settings.json"
            if os.path.exists(temp_settings):
                shutil.copy2(temp_settings, "data/settings.json")
                
            # استعادة مفتاح التشفير
            temp_key = "temp_restore/security/encryption.key"
            if os.path.exists(temp_key):
                shutil.copy2(temp_key, "data/encryption.key")
                
        except Exception as e:
            self.parent.logger.error(f"❌ فشل استعادة الملفات: {str(e)}")
            raise
            
    def verify_backup_integrity(self, backup_path, expected_checksum):
        """التحقق من سلامة النسخة الاحتياطية"""
        try:
            actual_checksum = self.calculate_file_checksum(backup_path)
            return actual_checksum == expected_checksum
        except Exception as e:
            self.parent.logger.error(f"❌ فشل التحقق من سلامة النسخة الاحتياطية: {str(e)}")
            return False
            
    def get_backup_info(self, backup_path):
        """الحصول على معلومات النسخة الاحتياطية"""
        try:
            info = {
                'file_path': backup_path,
                'file_size': os.path.getsize(backup_path),
                'created_at': datetime.fromtimestamp(os.path.getctime(backup_path)),
                'checksum': self.calculate_file_checksum(backup_path)
            }
            
            # فحص محتويات النسخة الاحتياطية
            with zipfile.ZipFile(backup_path, 'r') as backup_zip:
                info['contents'] = backup_zip.namelist()
                info['compressed_size'] = sum(zinfo.compress_size for zinfo in backup_zip.infolist())
                info['uncompressed_size'] = sum(zinfo.file_size for zinfo in backup_zip.infolist())
                
            return info
            
        except Exception as e:
            self.parent.logger.error(f"❌ فشل الحصول على معلومات النسخة الاحتياطية: {str(e)}")
            return None
