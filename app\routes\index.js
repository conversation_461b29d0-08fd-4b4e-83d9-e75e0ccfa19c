/**
 * مسارات قسم الأداة وملفاتها
 */
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middleware/auth');
const logger = require('../../utils/logger');

// استيراد وحدات التحكم بالأداة
const toolController = require('../controllers/toolController');

/**
 * @route   GET /api/tool/status
 * @desc    الحصول على حالة الأداة
 * @access  عام
 */
router.get('/status', toolController.getStatus);

/**
 * @route   GET /api/tool/files
 * @desc    الحصول على قائمة ملفات الأداة
 * @access  خاص (يتطلب مصادقة)
 */
router.get('/files', authenticate, toolController.getFiles);

/**
 * @route   POST /api/tool/execute
 * @desc    تنفيذ أمر في الأداة
 * @access  خاص (يتطلب مصادقة)
 */
router.post('/execute', authenticate, toolController.executeCommand);

/**
 * @route   GET /api/tool/logs
 * @desc    الحصول على سجلات الأداة
 * @access  خاص (يتطلب مصادقة)
 */
router.get('/logs', authenticate, toolController.getLogs);

/**
 * @route   POST /api/tool/start
 * @desc    تشغيل الأداة
 * @access  خاص (يتطلب مصادقة)
 */
router.post('/start', authenticate, toolController.startTool);

/**
 * @route   POST /api/tool/stop
 * @desc    إيقاف الأداة
 * @access  خاص (يتطلب مصادقة)
 */
router.post('/stop', authenticate, toolController.stopTool);

/**
 * @route   POST /api/tool/config
 * @desc    تحديث إعدادات الأداة
 * @access  خاص (يتطلب مصادقة)
 */
router.post('/config', authenticate, toolController.updateConfig);

/**
 * @route   POST /api/tool/files
 * @desc    إضافة ملف للأداة
 * @access  خاص (يتطلب مصادقة)
 */
router.post('/files', authenticate, toolController.addFile);

/**
 * @route   DELETE /api/tool/files/:fileId
 * @desc    حذف ملف من الأداة
 * @access  خاص (يتطلب مصادقة)
 */
router.delete('/files/:fileId', authenticate, toolController.removeFile);

// ===== مسارات GFX الجديدة =====

/**
 * @route   POST /api/tool/adb/connect
 * @desc    الاتصال بـ ADB
 * @access  خاص (يتطلب مصادقة)
 */
router.post('/adb/connect', authenticate, toolController.connectADB);

/**
 * @route   POST /api/tool/adb/disconnect
 * @desc    قطع الاتصال بـ ADB
 * @access  خاص (يتطلب مصادقة)
 */
router.post('/adb/disconnect', authenticate, toolController.disconnectADB);

/**
 * @route   GET /api/tool/adb/status
 * @desc    الحصول على حالة الاتصال بـ ADB
 * @access  خاص (يتطلب مصادقة)
 */
router.get('/adb/status', authenticate, toolController.getADBStatus);

/**
 * @route   GET /api/tool/adb/devices
 * @desc    الحصول على قائمة الأجهزة المتصلة
 * @access  خاص (يتطلب مصادقة)
 */
router.get('/adb/devices', authenticate, toolController.getConnectedDevices);

/**
 * @route   GET /api/tool/graphics/settings
 * @desc    قراءة إعدادات الرسومات من الجهاز
 * @access  خاص (يتطلب مصادقة)
 */
router.get('/graphics/settings', authenticate, toolController.getGraphicsSettings);

/**
 * @route   PUT /api/tool/graphics/settings
 * @desc    تحديث إعدادات الرسومات
 * @access  خاص (يتطلب مصادقة)
 */
router.put('/graphics/settings', authenticate, toolController.updateGraphicsSettings);

/**
 * @route   POST /api/tool/graphics/apply
 * @desc    تطبيق إعدادات الرسومات على الجهاز
 * @access  خاص (يتطلب مصادقة)
 */
router.post('/graphics/apply', authenticate, toolController.applyGraphicsSettings);

/**
 * @route   GET /api/tool/graphics/options
 * @desc    الحصول على الخيارات المتاحة لإعدادات الرسومات
 * @access  خاص (يتطلب مصادقة)
 */
router.get('/graphics/options', authenticate, toolController.getGraphicsOptions);

/**
 * @route   POST /api/tool/pubg/launch
 * @desc    تشغيل PUBG Mobile
 * @access  خاص (يتطلب مصادقة)
 */
router.post('/pubg/launch', authenticate, toolController.launchPUBG);

// ===== مسارات إدارة برنامج GFX =====

/**
 * @route   POST /api/tool/gfx/start
 * @desc    تشغيل برنامج GFX
 * @access  خاص (يتطلب مصادقة)
 */
router.post('/gfx/start', authenticate, toolController.startGFXProgram);

/**
 * @route   POST /api/tool/gfx/stop
 * @desc    إيقاف برنامج GFX
 * @access  خاص (يتطلب مصادقة)
 */
router.post('/gfx/stop', authenticate, toolController.stopGFXProgram);

/**
 * @route   POST /api/tool/gfx/restart
 * @desc    إعادة تشغيل برنامج GFX
 * @access  خاص (يتطلب مصادقة)
 */
router.post('/gfx/restart', authenticate, toolController.restartGFXProgram);

/**
 * @route   GET /api/tool/gfx/status
 * @desc    الحصول على حالة برنامج GFX
 * @access  خاص (يتطلب مصادقة)
 */
router.get('/gfx/status', authenticate, toolController.getGFXProgramStatus);

/**
 * @route   PUT /api/tool/gfx/files
 * @desc    تحديث ملفات GFX
 * @access  خاص (يتطلب مصادقة)
 */
router.put('/gfx/files', authenticate, toolController.updateGFXFiles);

// ===== مسارات التحميل =====

/**
 * @route   GET /api/tool/download/controller
 * @desc    تحميل أداة التحكم في السيرفر
 * @access  عام (لا يتطلب مصادقة)
 */
router.get('/download/controller', toolController.downloadServerController);

/**
 * @route   GET /api/tool/download/gfx
 * @desc    تحميل برنامج GFX
 * @access  خاص (يتطلب مصادقة)
 */
router.get('/download/gfx', authenticate, toolController.downloadGFX);

/**
 * @route   GET /api/tool/download/info
 * @desc    معلومات الملفات المتاحة للتحميل
 * @access  عام
 */
router.get('/download/info', toolController.getDownloadInfo);

module.exports = router;
