/**
 * مسارات قسم الأداة وملفاتها
 */
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middleware/auth');
const logger = require('../../utils/logger');

// استيراد وحدات التحكم بالأداة
const toolController = require('../controllers/toolController');

/**
 * @route   GET /api/tool/status
 * @desc    الحصول على حالة الأداة
 * @access  عام
 */
router.get('/status', toolController.getStatus);

/**
 * @route   GET /api/tool/files
 * @desc    الحصول على قائمة ملفات الأداة
 * @access  خاص (يتطلب مصادقة)
 */
router.get('/files', authenticate, toolController.getFiles);

/**
 * @route   POST /api/tool/execute
 * @desc    تنفيذ أمر في الأداة
 * @access  خاص (يتطلب مصادقة)
 */
router.post('/execute', authenticate, toolController.executeCommand);

/**
 * @route   GET /api/tool/logs
 * @desc    الحصول على سجلات الأداة
 * @access  خاص (يتطلب مصادقة)
 */
router.get('/logs', authenticate, toolController.getLogs);

/**
 * @route   POST /api/tool/start
 * @desc    تشغيل الأداة
 * @access  خاص (يتطلب مصادقة)
 */
router.post('/start', authenticate, toolController.startTool);

/**
 * @route   POST /api/tool/stop
 * @desc    إيقاف الأداة
 * @access  خاص (يتطلب مصادقة)
 */
router.post('/stop', authenticate, toolController.stopTool);

/**
 * @route   POST /api/tool/config
 * @desc    تحديث إعدادات الأداة
 * @access  خاص (يتطلب مصادقة)
 */
router.post('/config', authenticate, toolController.updateConfig);

/**
 * @route   POST /api/tool/files
 * @desc    إضافة ملف للأداة
 * @access  خاص (يتطلب مصادقة)
 */
router.post('/files', authenticate, toolController.addFile);

/**
 * @route   DELETE /api/tool/files/:fileId
 * @desc    حذف ملف من الأداة
 * @access  خاص (يتطلب مصادقة)
 */
router.delete('/files/:fileId', authenticate, toolController.removeFile);

module.exports = router;
