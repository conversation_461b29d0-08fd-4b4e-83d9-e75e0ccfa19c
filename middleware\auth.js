const jwt = require('jsonwebtoken');
const config = require('../config/config');

/**
 * وسيط للتحقق من المصادقة باستخدام JWT
 */
exports.authenticate = (req, res, next) => {
  try {
    // استخراج التوكن من الهيدر
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ 
        success: false, 
        message: 'غير مصرح به - يرجى تسجيل الدخول' 
      });
    }

    const token = authHeader.split(' ')[1];
    
    // التحقق من التوكن
    const decoded = jwt.verify(token, config.jwt.secret);
    
    // إضافة معلومات المستخدم إلى الطلب
    req.user = decoded;
    
    next();
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ 
        success: false, 
        message: 'انتهت صلاحية الجلسة - يرجى تسجيل الدخول مرة أخرى' 
      });
    }
    
    return res.status(401).json({ 
      success: false, 
      message: 'غير مصرح به - توكن غير صالح' 
    });
  }
};

/**
 * وسيط للتحقق من الأدوار والصلاحيات
 */
exports.authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ 
        success: false, 
        message: 'غير مصرح به - يرجى تسجيل الدخول' 
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ 
        success: false, 
        message: 'محظور - ليس لديك صلاحية للوصول إلى هذا المورد' 
      });
    }

    next();
  };
};
