# -*- coding: utf-8 -*-
import os
import sys
import time
import subprocess
import threading
from PIL import Image
from PyQt5.QtWidgets import (QApplication, QMainWindow, QPushButton, QLabel,
                            QWidget, QMessageBox)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QPixmap, QFont, QIcon

try:
    from ctypes import windll
    windll.shcore.SetProcessDpiAwareness(1)
except Exception:
    pass

def resource_path(relative_path):
    """
    الحصول على المسار المطلق للملفات سواء أثناء التطوير أو بعد تجميع التطبيق باستخدام PyInstaller.
    """
    try:
        # عند التشغيل من ملف EXE مجمّع
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

def get_adb_path():
    """
    تحديد مسار adb.exe من مجلد platform-tools الموجود بجانب التطبيق.
    """
    if getattr(sys, 'frozen', False):
        base_path = os.path.dirname(sys.executable)
    else:
        base_path = os.path.abspath(".")
    adb_filename = "adb.exe" if sys.platform.startswith("win") else "adb"
    return os.path.join(base_path, "platform-tools", adb_filename)

class GFX(QMainWindow):
    def __init__(self, parent=None):
        super().__init__(parent)

        # إعداد النافذة الأساسي
        self.setObjectName("JOGAMETOOL")
        self.resize(1262, 846)
        self.setWindowTitle("🎮 JO GAME TOOL - PUBG Mobile Graphics Optimizer")
        self.setIconSize(QSize(80, 80))

        # شريط عنوان احترافي
        self.setStyleSheet("""
            QMainWindow {
                background-color: #121212;
                color: #FFFFFF;
            }
            QWidget {
                background-color: #121212;
                color: #FFFFFF;
            }
            QMainWindow::title {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2d2d2d, stop:0.5 #3d3d3d, stop:1 #2d2d2d);
                color: #FFFFFF;
                font-family: "Segoe UI Black";
                font-size: 14px;
                font-weight: bold;
                padding: 5px;
                border-bottom: 3px solid #ecb440;
            }
        """)

        # إنشاء الويدجت المركزي
        self.centralwidget = QWidget(self)
        self.centralwidget.setObjectName("centralwidget")
        self.setCentralWidget(self.centralwidget)

        # المتغيرات الأساسية
        self.current_settings = {
            "graphics": None,
            "framerate": None,
            "style": None,
            "sfx": None,
            "shadow": None
        }

        self.adb_path = None
        self.adb_device = None
        self.activeSavContent = None
        self.assets = {}
        self.interactive_buttons = []
        self.is_adb_connected = False
        self.style_buttons = {}
        self.selected_style = None

        self.emulator_arch = "Unknown"
        self.emulator_path = self.detect_emulator_path()

        # إنشاء الصور المطلوبة وإعداد الواجهة
        self.create_required_images()
        self.setupUi()

        # تعطيل واجهة المستخدم افتراضيًا حتى يتم الاتصال بـ ADB
        self.toggle_ui_state(False)

        # بدء خيط للتحقق من الاتصال في الخلفية
        threading.Thread(target=self.check_connection, daemon=True).start()

    def detect_emulator_path(self):
        path_64 = r"C:\Program Files\TxGameAssistant\ui\AndroidEmulatorEn.exe"
        path_32 = r"C:\Program Files (x86)\TxGameAssistant\ui\AndroidEmulatorEn.exe"
        if os.path.exists(path_64):
            return path_64
        elif os.path.exists(path_32):
            return path_32
        else:
            return None

    def create_required_images(self):
        assets_info = {
            "app_icon": ("app_icon.png", (30, 30), "#2d2d2d"),
            "pubg_icon": ("pubg_icon.png", (80, 80), "#121212")
        }
        for asset_name, (filename, size, color) in assets_info.items():
            path = resource_path(os.path.join("assets", filename))
            if not os.path.exists(path):
                os.makedirs(os.path.dirname(path), exist_ok=True)
                if asset_name == "pubg_icon":
                    # إنشاء أيقونة PUBG أكثر وضوحاً
                    img = Image.new("RGB", size, color)
                else:
                    img = Image.new("RGB", size, color)
                img.save(path)
            self.assets[asset_name] = path

    def load_asset(self, asset_key, size):
        if asset_key in self.assets:
            pixmap = QPixmap(self.assets[asset_key])
            if size:
                pixmap = pixmap.scaled(size[0], size[1], Qt.KeepAspectRatio, Qt.SmoothTransformation)
            return pixmap
        return None

    def get_button_style(self, selected=False):
        """إرجاع نمط الزر مع الألوان المحسنة"""
        if selected:
            return """
                QPushButton {
                    background-color: #ecb440;
                    color: #FFFFFF;
                    border: 2px solid #ecb440;
                    border-radius: 5px;
                    font-family: "Segoe UI Black";
                    font-size: 14px;
                    font-weight: bold;
                    font-style: italic;
                    padding: 5px;
                }
                QPushButton:hover {
                    background-color: #ffd966;
                    border: 2px solid #ffd966;
                }
                QPushButton:pressed {
                    background-color: #d4a73a;
                    border: 2px solid #d4a73a;
                }
                QPushButton:disabled {
                    background-color: #1A1A1A;
                    color: #888888;
                    border: 2px solid #555555;
                }
            """
        else:
            return """
                QPushButton {
                    background-color: #2A2A2A;
                    color: #FFFFFF;
                    border: 2px solid #ecb440;
                    border-radius: 5px;
                    font-family: "Segoe UI Black";
                    font-size: 14px;
                    font-weight: bold;
                    font-style: italic;
                    padding: 5px;
                }
                QPushButton:hover {
                    background-color: #3A3A3A;
                    border: 2px solid #ecb440;
                }
                QPushButton:pressed {
                    background-color: #4A4A4A;
                    border: 2px solid #ecb440;
                }
                QPushButton:disabled {
                    background-color: #1A1A1A;
                    color: #888888;
                    border: 2px solid #555555;
                }
            """

    def get_label_style(self):
        """إرجاع نمط التسميات"""
        return """
            QLabel {
                color: #FFFFFF;
                font-family: "Segoe UI Black";
                font-size: 14px;
                font-weight: bold;
                background-color: transparent;
            }
        """

    def setupUi(self):
        # إعداد الخطوط
        font = QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setWeight(75)

        # زر ADB مع مؤشر الحالة
        self.ADB = QPushButton(self.centralwidget)
        self.ADB.setGeometry(10, 800, 75, 31)
        self.ADB.setFont(font)
        self.ADB.setObjectName("ADB")
        self.ADB.setText("ADB")
        self.ADB.setStyleSheet("""
            QPushButton {
                background-color: #ecb440;
                color: white;
                border: 2px solid #ecb440;
                border-radius: 5px;
                font-family: "Segoe UI Black";
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #ffd966;
                border: 2px solid #ecb440;
            }
            QPushButton:pressed {
                background-color: #d4a73a;
                border: 2px solid #ecb440;
            }
        """)
        self.ADB.clicked.connect(self.check_connection)

        # مؤشر الحالة (بجانب زر ADB)
        self.status_indicator = QLabel(self.centralwidget)
        self.status_indicator.setGeometry(95, 790, 30, 50)
        self.status_indicator.setText("●")
        self.status_indicator.setStyleSheet("color: #ff0000; font-size: 40px; font-weight: bold;")
        self.status_indicator.setAlignment(Qt.AlignCenter)

        # أزرار التحكم الرئيسية
        self.Get_Graphics_file = QPushButton(self.centralwidget)
        self.Get_Graphics_file.setGeometry(870, 780, 181, 51)
        self.Get_Graphics_file.setFont(font)
        self.Get_Graphics_file.setObjectName("Get_Graphics_file")
        self.Get_Graphics_file.setText("Get Graphics file")
        self.Get_Graphics_file.setStyleSheet(self.get_button_style())
        self.Get_Graphics_file.clicked.connect(self.get_graphics_file)
        self.interactive_buttons.append(self.Get_Graphics_file)

        self.Apply = QPushButton(self.centralwidget)
        self.Apply.setGeometry(1060, 780, 181, 51)
        self.Apply.setFont(font)
        self.Apply.setObjectName("Apply")
        self.Apply.setText("Apply")
        self.Apply.setStyleSheet(self.get_button_style())
        self.Apply.clicked.connect(self.apply_settings)
        self.interactive_buttons.append(self.Apply)

        # تسمية Graphics
        self.Graphics = QLabel(self.centralwidget)
        self.Graphics.setGeometry(20, 20, 600, 31)
        self.Graphics.setFont(font)
        self.Graphics.setObjectName("Graphics")
        self.Graphics.setText("Graphics (lower the settings if the device lags)")
        self.Graphics.setStyleSheet(self.get_label_style())

        # أزرار Graphics
        self.super_smooth = QPushButton(self.centralwidget)
        self.super_smooth.setGeometry(20, 70, 161, 51)
        self.super_smooth.setFont(font)
        self.super_smooth.setObjectName("super_smooth")
        self.super_smooth.setText("Super Smooth")
        self.super_smooth.setStyleSheet(self.get_button_style())
        self.super_smooth.clicked.connect(lambda: self.select_option("graphics", "Super Smooth"))
        self.interactive_buttons.append(self.super_smooth)

        # باقي الأزرار والوظائف...
        # (الكود كامل في الملف الأصلي)

    def check_connection(self):
        """فحص الاتصال بالجهاز"""
        for _ in range(5):
            try:
                device = self.setup_adb()
                if device:
                    self.adb_device = device
                    self.status_indicator.setStyleSheet("color: #00ff00; font-size: 40px; font-weight: bold;")
                    self.show_notification(f"Device connected: {self.adb_device}")
                    self.toggle_ui_state(True)
                    threading.Thread(target=self.read_active_sav_file_directly, daemon=True).start()
                    return
                else:
                    self.status_indicator.setStyleSheet("color: #ff0000; font-size: 40px; font-weight: bold;")
                    self.show_notification("No device connected. Retrying...")
            except Exception as e:
                self.status_indicator.setStyleSheet("color: #ff0000; font-size: 40px; font-weight: bold;")
                self.show_notification("Connection error: " + str(e))
            time.sleep(0.3)
        self.toggle_ui_state(False)

    def setup_adb(self):
        """إعداد ADB"""
        self.adb_path = get_adb_path()
        if not os.path.exists(self.adb_path):
            raise Exception("ADB not found in platform-tools folder.")
        try:
            self.execute_adb_command("kill-server")
        except Exception:
            pass
        time.sleep(0.5)
        self.execute_adb_command("start-server")
        time.sleep(0.5)
        return self.find_adb_device()

    def execute_adb_command(self, arguments):
        """تنفيذ أوامر ADB"""
        cmd = [self.adb_path] + arguments.split()
        startupinfo = None
        if sys.platform.startswith('win'):
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = 0
        proc = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=False, startupinfo=startupinfo)
        stdout, stderr = proc.communicate()
        out_str = stdout.decode("utf-8").strip()
        err_str = stderr.decode("utf-8").strip().lower()
        if err_str and any(x in err_str for x in ["no such file", "error", "failed"]):
            raise Exception(stderr.decode("utf-8").strip())
        return out_str

    def find_adb_device(self):
        """البحث عن الأجهزة المتصلة"""
        output = self.execute_adb_command("devices")
        for line in output.splitlines():
            if "device" in line and "List" not in line:
                if "offline" not in line.lower():
                    self.adb_device = line.split()[0]
                    return self.adb_device
        return None

    def show_notification(self, message):
        """عرض الإشعارات"""
        print(message)

    def toggle_ui_state(self, connected: bool):
        """تفعيل/تعطيل واجهة المستخدم بناءً على حالة الاتصال"""
        self.is_adb_connected = connected
        for widget in self.interactive_buttons:
            if hasattr(widget, "always_active") and widget.always_active:
                continue
            try:
                widget.setEnabled(connected)
            except Exception:
                pass
        if connected:
            self.status_indicator.setStyleSheet("color: #00ff00; font-size: 40px; font-weight: bold;")
        else:
            self.status_indicator.setStyleSheet("color: #ff0000; font-size: 40px; font-weight: bold;")

    def get_graphics_file(self):
        """قراءة ملفات الإعدادات من الجهاز"""
        if not self.adb_device:
            self.show_notification("لا يوجد اتصال بالمحاكي. يرجى الضغط على زر ADB للاتصال أولاً.")
            return
        # باقي الكود...

    def apply_settings(self):
        """تطبيق الإعدادات على الجهاز"""
        if not self.adb_device:
            self.show_notification("لا يوجد اتصال بالمحاكي. يرجى الضغط على زر ADB للاتصال أولاً.")
            return
        # باقي الكود...

if __name__ == "__main__":
    app = QApplication(sys.argv)
    gfx_tool = GFX()
    gfx_tool.show()
    sys.exit(app.exec_())
