#!/usr/bin/env node

/**
 * سكريبت إعداد GFX - PUBG Mobile Graphics Optimizer
 * يقوم بإعداد البيئة المطلوبة لتشغيل أداة GFX
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

console.log('🎮 مرحباً بك في إعداد GFX - محسن رسومات PUBG Mobile');
console.log('================================================\n');

/**
 * فحص متطلبات النظام
 */
async function checkSystemRequirements() {
  console.log('🔍 فحص متطلبات النظام...');
  
  try {
    // فحص Node.js
    const nodeVersion = process.version;
    console.log(`✅ Node.js: ${nodeVersion}`);
    
    if (parseInt(nodeVersion.slice(1)) < 18) {
      console.error('❌ يتطلب Node.js الإصدار 18 أو أحدث');
      process.exit(1);
    }
    
    // فحص npm
    const { stdout: npmVersion } = await execAsync('npm --version');
    console.log(`✅ npm: v${npmVersion.trim()}`);
    
    console.log('✅ متطلبات النظام مستوفاة\n');
    
  } catch (error) {
    console.error('❌ فشل في فحص متطلبات النظام:', error.message);
    process.exit(1);
  }
}

/**
 * إنشاء المجلدات المطلوبة
 */
function createRequiredDirectories() {
  console.log('📁 إنشاء المجلدات المطلوبة...');
  
  const directories = [
    'platform-tools',
    'backups',
    'backups/gfx',
    'logs',
    'ssl'
  ];
  
  directories.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`✅ تم إنشاء مجلد: ${dir}`);
    } else {
      console.log(`📁 مجلد موجود: ${dir}`);
    }
  });
  
  console.log('✅ تم إنشاء جميع المجلدات المطلوبة\n');
}

/**
 * فحص وجود ADB
 */
function checkADB() {
  console.log('🔧 فحص ADB (Android Debug Bridge)...');
  
  const adbPaths = [
    './platform-tools/adb.exe',
    './platform-tools/adb',
    'adb.exe',
    'adb'
  ];
  
  let adbFound = false;
  
  for (const adbPath of adbPaths) {
    if (fs.existsSync(adbPath)) {
      console.log(`✅ تم العثور على ADB: ${adbPath}`);
      adbFound = true;
      break;
    }
  }
  
  if (!adbFound) {
    console.log('⚠️  لم يتم العثور على ADB');
    console.log('📥 يرجى تحميل Android Platform Tools من:');
    console.log('   https://developer.android.com/studio/releases/platform-tools');
    console.log('   ووضع ملفات ADB في مجلد platform-tools/\n');
  } else {
    console.log('✅ ADB جاهز للاستخدام\n');
  }
  
  return adbFound;
}

/**
 * إنشاء ملف .env إذا لم يكن موجوداً
 */
function createEnvFile() {
  console.log('⚙️  فحص ملف .env...');
  
  if (!fs.existsSync('.env')) {
    const envContent = `# متغيرات السيرفر
PORT=3000
NODE_ENV=development

# مفتاح سري لتوقيع JWT
JWT_SECRET=your_super_secure_jwt_secret_key_change_this_in_production
JWT_EXPIRES_IN=1d

# إعدادات CORS
CORS_ORIGIN=*

# حدود معدل الطلبات
RATE_LIMIT_WINDOW_MS=15*60*1000  # 15 دقيقة
RATE_LIMIT_MAX=100  # 100 طلب لكل IP خلال فترة النافذة

# إعدادات GFX
ADB_PATH=./platform-tools/adb.exe
ADB_TIMEOUT=30000
GFX_SIMULATION_MODE=false
GFX_SAFE_MODE=false
GFX_VERBOSE_LEVEL=info
`;
    
    fs.writeFileSync('.env', envContent);
    console.log('✅ تم إنشاء ملف .env');
  } else {
    console.log('📄 ملف .env موجود');
  }
  
  console.log('✅ إعدادات البيئة جاهزة\n');
}

/**
 * تثبيت التبعيات
 */
async function installDependencies() {
  console.log('📦 تثبيت التبعيات...');
  
  try {
    console.log('⏳ جاري تثبيت حزم npm...');
    await execAsync('npm install');
    console.log('✅ تم تثبيت جميع التبعيات بنجاح\n');
  } catch (error) {
    console.error('❌ فشل في تثبيت التبعيات:', error.message);
    console.log('💡 جرب تشغيل: npm install يدوياً\n');
  }
}

/**
 * اختبار الاتصال بـ ADB
 */
async function testADBConnection() {
  console.log('🔌 اختبار الاتصال بـ ADB...');
  
  try {
    const adbPath = fs.existsSync('./platform-tools/adb.exe') ? 
      './platform-tools/adb.exe' : 'adb';
    
    const { stdout } = await execAsync(`${adbPath} devices`);
    console.log('📱 أجهزة متصلة:');
    console.log(stdout);
    
    const devices = stdout.split('\n')
      .slice(1)
      .filter(line => line.trim() && line.includes('device'))
      .length;
    
    if (devices > 0) {
      console.log(`✅ تم العثور على ${devices} جهاز متصل`);
    } else {
      console.log('⚠️  لا توجد أجهزة متصلة');
      console.log('💡 تأكد من:');
      console.log('   - تشغيل محاكي Android');
      console.log('   - أو توصيل جهاز Android مع تفعيل USB Debugging');
    }
    
  } catch (error) {
    console.log('❌ فشل في الاتصال بـ ADB:', error.message);
    console.log('💡 تأكد من تثبيت ADB بشكل صحيح');
  }
  
  console.log('');
}

/**
 * إنشاء ملف تجريبي
 */
function createSampleFiles() {
  console.log('📝 إنشاء ملفات تجريبية...');
  
  // إنشاء ملف اختبار سريع
  const quickTestContent = `#!/usr/bin/env node

/**
 * اختبار سريع لـ GFX API
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function quickTest() {
  try {
    console.log('🧪 اختبار سريع لـ GFX API...');
    
    // اختبار الصفحة الرئيسية
    const response = await axios.get(BASE_URL);
    console.log('✅ السيرفر يعمل:', response.data.message);
    
    // اختبار حالة الأداة (بدون مصادقة)
    try {
      const toolResponse = await axios.get(\`\${BASE_URL}/api/tool/status\`);
      console.log('✅ أداة GFX جاهزة:', toolResponse.data.tool.name);
    } catch (error) {
      console.log('⚠️  أداة GFX تتطلب مصادقة');
    }
    
    console.log('🎉 الاختبار السريع مكتمل!');
    
  } catch (error) {
    console.error('❌ فشل الاختبار:', error.message);
    console.log('💡 تأكد من تشغيل السيرفر: npm start');
  }
}

if (require.main === module) {
  quickTest();
}

module.exports = quickTest;
`;
  
  fs.writeFileSync('quick_test.js', quickTestContent);
  console.log('✅ تم إنشاء ملف quick_test.js');
  
  console.log('✅ تم إنشاء جميع الملفات التجريبية\n');
}

/**
 * عرض معلومات الإكمال
 */
function showCompletionInfo() {
  console.log('🎉 تم إكمال إعداد GFX بنجاح!');
  console.log('=====================================\n');
  
  console.log('📋 الخطوات التالية:');
  console.log('');
  console.log('1️⃣  تشغيل السيرفر:');
  console.log('   npm start');
  console.log('   # أو للتطوير: npm run dev');
  console.log('');
  console.log('2️⃣  اختبار سريع:');
  console.log('   node quick_test.js');
  console.log('');
  console.log('3️⃣  اختبار GFX API كامل:');
  console.log('   node app/test_gfx_api.js');
  console.log('');
  console.log('📚 التوثيق:');
  console.log('   - README.md - دليل الاستخدام');
  console.log('   - app/GFX_API_DOCUMENTATION.md - توثيق API');
  console.log('   - app/config/gfx.js - إعدادات GFX');
  console.log('');
  console.log('🌐 الوصول للسيرفر:');
  console.log('   http://localhost:3000');
  console.log('');
  console.log('🎮 استمتع باستخدام GFX - محسن رسومات PUBG Mobile!');
}

/**
 * تشغيل الإعداد الكامل
 */
async function main() {
  try {
    await checkSystemRequirements();
    createRequiredDirectories();
    const adbFound = checkADB();
    createEnvFile();
    await installDependencies();
    
    if (adbFound) {
      await testADBConnection();
    }
    
    createSampleFiles();
    showCompletionInfo();
    
  } catch (error) {
    console.error('❌ فشل في الإعداد:', error.message);
    process.exit(1);
  }
}

// تشغيل الإعداد إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  main();
}

module.exports = {
  checkSystemRequirements,
  createRequiredDirectories,
  checkADB,
  createEnvFile,
  installDependencies,
  testADBConnection,
  createSampleFiles
};
