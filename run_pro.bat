@echo off
chcp 65001 >nul
color 0A
title 🎮 أداة التحكم الاحترافية في السيرفر v3.0 - Server Controller Professional

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    🎮 أداة التحكم الاحترافية في السيرفر v3.0              ██
echo ██       Server Controller Professional Edition               ██
echo ██                                                            ██
echo ██    المطور: فريق JO GAME TOOL                              ██
echo ██    الإصدار: 3.0.0 Professional                           ██
echo ██    التاريخ: 2024-06-23                                    ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

echo 🔍 فحص متطلبات النظام...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo.
    echo 📥 يرجى تثبيت Python 3.8 أو أحدث من:
    echo    https://www.python.org/downloads/
    echo.
    echo 💡 تأكد من إضافة Python إلى PATH أثناء التثبيت
    echo.
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM عرض إصدار Python
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo 🐍 إصدار Python: %PYTHON_VERSION%

REM التحقق من إصدار Python
python -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ يتطلب Python 3.8 أو أحدث
    echo 📥 يرجى تحديث Python من: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ إصدار Python مناسب

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متوفر
    echo يرجى تثبيت pip أولاً
    pause
    exit /b 1
)

echo ✅ pip متوفر

echo.
echo 📦 فحص وتثبيت المكتبات المطلوبة...
echo.

REM تثبيت المكتبات من ملف المتطلبات
if exist "requirements_pro.txt" (
    echo 📋 تثبيت المكتبات من requirements_pro.txt...
    pip install -r requirements_pro.txt --quiet --disable-pip-version-check
    if errorlevel 1 (
        echo ❌ فشل تثبيت بعض المكتبات
        echo 🔄 محاولة تثبيت المكتبات الأساسية فقط...
        pip install requests psutil --quiet --disable-pip-version-check
        if errorlevel 1 (
            echo ❌ فشل تثبيت المكتبات الأساسية
            pause
            exit /b 1
        )
    )
) else (
    echo 📦 تثبيت المكتبات الأساسية...
    pip install requests psutil --quiet --disable-pip-version-check
    if errorlevel 1 (
        echo ❌ فشل تثبيت المكتبات الأساسية
        pause
        exit /b 1
    )
)

echo ✅ تم تثبيت المكتبات بنجاح

REM فحص المكتبات الاختيارية
echo.
echo 🔍 فحص المكتبات الاختيارية...

python -c "import cryptography" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ مكتبة cryptography غير مثبتة (مطلوبة للأمان المتقدم)
    echo 📦 تثبيت cryptography...
    pip install cryptography --quiet --disable-pip-version-check
    if errorlevel 1 (
        echo ❌ فشل تثبيت cryptography - سيتم استخدام التشفير الأساسي
    ) else (
        echo ✅ تم تثبيت cryptography
    )
) else (
    echo ✅ cryptography متوفرة
)

echo.
echo 📁 فحص هيكل الملفات...

REM التحقق من وجود الملف الرئيسي
if not exist "server_controller_pro.py" (
    echo ❌ ملف server_controller_pro.py غير موجود
    echo 📁 يرجى التأكد من وجود الملف في نفس المجلد
    echo.
    echo 📥 يمكنك تحميل الأداة من:
    echo    http://localhost:3000/download
    echo.
    pause
    exit /b 1
)

echo ✅ ملف الأداة الرئيسي موجود

REM التحقق من وجود مجلد الوحدات
if not exist "modules" (
    echo ❌ مجلد modules غير موجود
    echo 📁 يرجى التأكد من وجود جميع ملفات الأداة
    pause
    exit /b 1
)

echo ✅ مجلد الوحدات موجود

REM إنشاء المجلدات المطلوبة
echo.
echo 📂 إعداد هيكل المجلدات...

for %%d in (data logs backups themes languages plugins reports cache temp exports) do (
    if not exist "%%d" (
        mkdir "%%d" >nul 2>&1
        echo ✅ تم إنشاء مجلد %%d
    )
)

echo ✅ تم إعداد جميع المجلدات

echo.
echo 🔧 فحص صلاحيات النظام...

REM فحص صلاحيات الكتابة
echo test > test_write.tmp 2>nul
if exist test_write.tmp (
    del test_write.tmp >nul 2>&1
    echo ✅ صلاحيات الكتابة متوفرة
) else (
    echo ⚠️ قد تحتاج صلاحيات مدير للكتابة في هذا المجلد
)

echo.
echo 🚀 بدء تشغيل أداة التحكم الاحترافية...
echo.
echo ════════════════════════════════════════════════════════════════
echo 🎮 المميزات المتوفرة في الإصدار الاحترافي:
echo ════════════════════════════════════════════════════════════════
echo ✅ واجهة مستخدم احترافية متقدمة
echo ✅ نظام قاعدة بيانات محلية متطور
echo ✅ نظام أمان وتشفير متكامل
echo ✅ مراقبة الأداء في الوقت الفعلي
echo ✅ نسخ احتياطية ذكية ومجدولة
echo ✅ نظام إشعارات متطور
echo ✅ تقارير وإحصائيات مفصلة
echo ✅ إدارة متعددة المستخدمين
echo ✅ نظام سجلات متقدم
echo ✅ واجهة مقسمة لأقسام متخصصة
echo ════════════════════════════════════════════════════════════════
echo.

REM تشغيل الأداة
python server_controller_pro.py

echo.
echo ════════════════════════════════════════════════════════════════
echo 👋 تم إغلاق أداة التحكم الاحترافية في السيرفر
echo.
echo 💾 تم حفظ جميع البيانات والإعدادات تلقائياً
echo 📊 يمكنك مراجعة السجلات في مجلد logs/
echo 💾 النسخ الاحتياطية محفوظة في مجلد backups/
echo.
echo 🎮 شكراً لاستخدام أداة التحكم الاحترافية!
echo ════════════════════════════════════════════════════════════════
echo.
pause
