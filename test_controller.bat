@echo off
chcp 65001 >nul
title 🧪 اختبار أداة التحكم في السيرفر

echo.
echo ========================================
echo 🧪 اختبار أداة التحكم في السيرفر
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM التحقق من وجود المكتبات
python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ تثبيت المكتبات المطلوبة...
    pip install requests
)

echo ✅ المكتبات جاهزة

REM التحقق من وجود ملف الاختبار
if not exist "test_server_controller.py" (
    echo ❌ ملف الاختبار غير موجود
    pause
    exit /b 1
)

echo ✅ ملف الاختبار موجود

echo.
echo 🔄 بدء اختبار الأداة...
echo.

REM تشغيل الاختبار
python test_server_controller.py

echo.
echo 📊 انتهى الاختبار
echo 📁 تحقق من ملف النتائج test_results_*.json
echo.
pause
