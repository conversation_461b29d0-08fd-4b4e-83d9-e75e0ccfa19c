#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎮 أداة التحكم في السيرفر - Server Controller
أداة Python شاملة للتحكم في السيرفر عن بُعد

المميزات:
- تشغيل/إيقاف الأداة
- تحديث الملفات
- مراقبة الحالة
- إدارة GFX
- رفع النسخ الجديدة
"""

import os
import sys
import json
import time
import requests
import threading
import json
import sqlite3
import hashlib
import base64
import zipfile
import shutil
import psutil
import subprocess
from datetime import datetime, timedelta
from pathlib import Path
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
from tkinter.font import Font
import webbrowser
from urllib.parse import urlparse
import configparser
import logging
from typing import Dict, List, Optional, Any
import asyncio
import concurrent.futures

class ServerController:
    def __init__(self):
        """تهيئة أداة التحكم في السيرفر"""
        # إعداد نظام السجلات
        self.setup_logging()

        # إعداد قاعدة البيانات المحلية
        self.setup_database()

        # إعداد الواجهة
        self.root = tk.Tk()
        self.setup_window()

        # إعدادات الاتصال
        self.server_url = "http://localhost:3000"
        self.auth_token = None
        self.is_connected = False
        self.connection_history = []

        # إعدادات المراقبة المتقدمة
        self.monitoring = False
        self.monitor_thread = None
        self.performance_data = []
        self.alerts_enabled = True
        self.auto_reconnect = True

        # إعدادات الأمان
        self.session_timeout = 3600  # ساعة واحدة
        self.last_activity = time.time()
        self.max_retry_attempts = 3
        self.retry_delay = 5

        # إعدادات التحديثات
        self.auto_update_check = True
        self.backup_enabled = True
        self.backup_interval = 24  # ساعة

        # إعدادات الواجهة المتقدمة
        self.theme = "default"
        self.language = "ar"
        self.notifications_enabled = True

        # متغيرات الأداء
        self.request_cache = {}
        self.cache_timeout = 300  # 5 دقائق

        # إنشاء الواجهة المحسنة
        self.create_advanced_widgets()
        self.load_advanced_settings()

        # بدء خدمات الخلفية
        self.start_background_services()

    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("🎮 أداة التحكم في السيرفر - Server Controller")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)

        # تعيين الأيقونة (إذا كانت متوفرة)
        try:
            self.root.iconbitmap("server_icon.ico")
        except:
            pass

        # تعيين الخطوط
        self.font_title = Font(family="Arial", size=14, weight="bold")
        self.font_normal = Font(family="Arial", size=10)
        self.font_small = Font(family="Arial", size=8)

        # ألوان الواجهة
        self.colors = {
            'bg': '#2b2b2b',
            'fg': '#ffffff',
            'accent': '#4CAF50',
            'warning': '#FF9800',
            'error': '#F44336',
            'info': '#2196F3'
        }

        self.root.configure(bg=self.colors['bg'])

    def setup_logging(self):
        """إعداد نظام السجلات المتقدم"""
        # إنشاء مجلد السجلات
        os.makedirs("logs", exist_ok=True)

        # إعداد نظام السجلات
        log_filename = f"logs/server_controller_{datetime.now().strftime('%Y%m%d')}.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info("تم بدء تشغيل أداة التحكم في السيرفر")

    def setup_database(self):
        """إعداد قاعدة البيانات المحلية"""
        try:
            self.db_path = "server_controller.db"
            self.conn = sqlite3.connect(self.db_path, check_same_thread=False)
            self.cursor = self.conn.cursor()

            # إنشاء الجداول
            self.create_tables()
            self.logger.info("تم إعداد قاعدة البيانات بنجاح")

        except Exception as e:
            self.logger.error(f"فشل إعداد قاعدة البيانات: {str(e)}")

    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        # جدول الاتصالات
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS connections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                server_url TEXT NOT NULL,
                username TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                status TEXT,
                response_time REAL
            )
        ''')

        # جدول بيانات الأداء
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS performance_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                cpu_usage REAL,
                memory_usage REAL,
                network_latency REAL,
                server_status TEXT,
                gfx_status TEXT
            )
        ''')

        # جدول السجلات
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS activity_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                action TEXT NOT NULL,
                details TEXT,
                status TEXT,
                user_id TEXT
            )
        ''')

        # جدول الإعدادات
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                key TEXT PRIMARY KEY,
                value TEXT,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # جدول النسخ الاحتياطية
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS backups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename TEXT NOT NULL,
                file_path TEXT NOT NULL,
                file_size INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                backup_type TEXT
            )
        ''')

        self.conn.commit()

    def start_background_services(self):
        """بدء الخدمات في الخلفية"""
        # خدمة فحص انتهاء الجلسة
        threading.Thread(target=self.session_monitor, daemon=True).start()

        # خدمة النسخ الاحتياطي التلقائي
        if self.backup_enabled:
            threading.Thread(target=self.auto_backup_service, daemon=True).start()

        # خدمة فحص التحديثات
        if self.auto_update_check:
            threading.Thread(target=self.update_checker_service, daemon=True).start()

        self.logger.info("تم بدء جميع الخدمات في الخلفية")

    def session_monitor(self):
        """مراقبة انتهاء الجلسة"""
        while True:
            try:
                if self.is_connected:
                    current_time = time.time()
                    if current_time - self.last_activity > self.session_timeout:
                        self.logger.warning("انتهت صلاحية الجلسة")
                        self.root.after(0, self.handle_session_timeout)

                time.sleep(60)  # فحص كل دقيقة

            except Exception as e:
                self.logger.error(f"خطأ في مراقبة الجلسة: {str(e)}")
                time.sleep(60)

    def handle_session_timeout(self):
        """التعامل مع انتهاء الجلسة"""
        if self.is_connected:
            self.disconnect_from_server()
            messagebox.showwarning("انتهاء الجلسة", "انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.")

    def auto_backup_service(self):
        """خدمة النسخ الاحتياطي التلقائي"""
        while True:
            try:
                if self.backup_enabled:
                    self.create_automatic_backup()

                # انتظار حتى الموعد التالي
                time.sleep(self.backup_interval * 3600)  # تحويل الساعات إلى ثواني

            except Exception as e:
                self.logger.error(f"خطأ في النسخ الاحتياطي التلقائي: {str(e)}")
                time.sleep(3600)  # انتظار ساعة في حالة الخطأ

    def update_checker_service(self):
        """خدمة فحص التحديثات"""
        while True:
            try:
                if self.auto_update_check and self.is_connected:
                    self.check_for_updates()

                # فحص كل 6 ساعات
                time.sleep(6 * 3600)

            except Exception as e:
                self.logger.error(f"خطأ في فحص التحديثات: {str(e)}")
                time.sleep(3600)

    def create_advanced_widgets(self):
        """إنشاء عناصر الواجهة المتقدمة"""
        # إنشاء شريط القوائم المتقدم
        self.create_menu_bar()

        # إنشاء شريط الأدوات
        self.create_toolbar()

        # إنشاء النوت بوك للتبويبات مع تحسينات
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # التبويبات المحسنة
        self.create_advanced_connection_tab()
        self.create_advanced_tool_management_tab()
        self.create_advanced_gfx_management_tab()
        self.create_advanced_updates_tab()
        self.create_advanced_monitoring_tab()
        self.create_advanced_logs_tab()
        self.create_settings_tab()
        self.create_analytics_tab()
        self.create_security_tab()

        # شريط الحالة المتقدم
        self.create_advanced_status_bar()

        # إعداد اختصارات لوحة المفاتيح
        self.setup_keyboard_shortcuts()

    def create_menu_bar(self):
        """إنشاء شريط القوائم المتقدم"""
        self.menubar = tk.Menu(self.root)
        self.root.config(menu=self.menubar)

        # قائمة الملف
        file_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="اتصال جديد", command=self.new_connection, accelerator="Ctrl+N")
        file_menu.add_command(label="حفظ الإعدادات", command=self.save_advanced_settings, accelerator="Ctrl+S")
        file_menu.add_separator()
        file_menu.add_command(label="استيراد الإعدادات", command=self.import_settings)
        file_menu.add_command(label="تصدير الإعدادات", command=self.export_settings)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.on_closing, accelerator="Ctrl+Q")

        # قائمة التحرير
        edit_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="تحرير", menu=edit_menu)
        edit_menu.add_command(label="تفضيلات", command=self.open_preferences, accelerator="Ctrl+P")
        edit_menu.add_command(label="مسح السجلات", command=self.clear_logs)
        edit_menu.add_command(label="إعادة تعيين الإعدادات", command=self.reset_settings)

        # قائمة الأدوات
        tools_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="أدوات", menu=tools_menu)
        tools_menu.add_command(label="اختبار الاتصال", command=self.test_connection)
        tools_menu.add_command(label="تشخيص النظام", command=self.system_diagnostics)
        tools_menu.add_command(label="نسخة احتياطية", command=self.create_backup)
        tools_menu.add_command(label="تحديث الأداة", command=self.check_for_updates)

        # قائمة العرض
        view_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="عرض", menu=view_menu)
        view_menu.add_command(label="ملء الشاشة", command=self.toggle_fullscreen, accelerator="F11")
        view_menu.add_command(label="تكبير", command=self.zoom_in, accelerator="Ctrl++")
        view_menu.add_command(label="تصغير", command=self.zoom_out, accelerator="Ctrl+-")
        view_menu.add_command(label="حجم طبيعي", command=self.zoom_reset, accelerator="Ctrl+0")

        # قائمة المساعدة
        help_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل الاستخدام", command=self.show_help)
        help_menu.add_command(label="اختصارات لوحة المفاتيح", command=self.show_shortcuts)
        help_menu.add_command(label="تحقق من التحديثات", command=self.check_for_updates)
        help_menu.add_separator()
        help_menu.add_command(label="حول البرنامج", command=self.show_about)

    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        self.toolbar = ttk.Frame(self.root)
        self.toolbar.pack(fill=tk.X, padx=5, pady=2)

        # أزرار شريط الأدوات
        ttk.Button(self.toolbar, text="🔗 اتصال", command=self.quick_connect).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="❌ قطع", command=self.disconnect_from_server).pack(side=tk.LEFT, padx=2)
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)

        ttk.Button(self.toolbar, text="▶️ تشغيل", command=self.start_tool).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="⏹️ إيقاف", command=self.stop_tool).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="🔄 تحديث", command=self.refresh_all).pack(side=tk.LEFT, padx=2)
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)

        ttk.Button(self.toolbar, text="📊 مراقبة", command=self.toggle_monitoring).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="💾 نسخة احتياطية", command=self.create_backup).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="⚙️ إعدادات", command=self.open_preferences).pack(side=tk.LEFT, padx=2)

        # مؤشر الحالة في شريط الأدوات
        self.toolbar_status = ttk.Label(self.toolbar, text="❌ غير متصل")
        self.toolbar_status.pack(side=tk.RIGHT, padx=10)

    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        self.root.bind('<Control-n>', lambda e: self.new_connection())
        self.root.bind('<Control-s>', lambda e: self.save_advanced_settings())
        self.root.bind('<Control-q>', lambda e: self.on_closing())
        self.root.bind('<Control-p>', lambda e: self.open_preferences())
        self.root.bind('<F11>', lambda e: self.toggle_fullscreen())
        self.root.bind('<Control-plus>', lambda e: self.zoom_in())
        self.root.bind('<Control-minus>', lambda e: self.zoom_out())
        self.root.bind('<Control-0>', lambda e: self.zoom_reset())
        self.root.bind('<F5>', lambda e: self.refresh_all())
        self.root.bind('<Control-t>', lambda e: self.test_connection())

    def create_widgets(self):
        """إنشاء عناصر الواجهة (للتوافق مع الكود القديم)"""
        self.create_advanced_widgets()

    def create_connection_tab(self):
        """تبويب إعدادات الاتصال"""
        tab = ttk.Frame(self.notebook)
        self.notebook.add(tab, text="🔗 الاتصال")

        # إطار إعدادات الاتصال
        conn_frame = ttk.LabelFrame(tab, text="إعدادات الاتصال", padding=10)
        conn_frame.pack(fill=tk.X, padx=10, pady=5)

        # عنوان السيرفر
        ttk.Label(conn_frame, text="عنوان السيرفر:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.server_url_var = tk.StringVar(value=self.server_url)
        self.server_url_entry = ttk.Entry(conn_frame, textvariable=self.server_url_var, width=40)
        self.server_url_entry.grid(row=0, column=1, columnspan=2, sticky=tk.W+tk.E, pady=2)

        # بيانات تسجيل الدخول
        ttk.Label(conn_frame, text="البريد الإلكتروني:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.email_var = tk.StringVar(value="<EMAIL>")
        self.email_entry = ttk.Entry(conn_frame, textvariable=self.email_var, width=30)
        self.email_entry.grid(row=1, column=1, sticky=tk.W+tk.E, pady=2)

        ttk.Label(conn_frame, text="كلمة المرور:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.password_var = tk.StringVar(value="admin123")
        self.password_entry = ttk.Entry(conn_frame, textvariable=self.password_var, show="*", width=30)
        self.password_entry.grid(row=2, column=1, sticky=tk.W+tk.E, pady=2)

        # أزرار الاتصال
        buttons_frame = ttk.Frame(conn_frame)
        buttons_frame.grid(row=3, column=0, columnspan=3, pady=10)

        self.connect_btn = ttk.Button(buttons_frame, text="🔗 اتصال", command=self.connect_to_server)
        self.connect_btn.pack(side=tk.LEFT, padx=5)

        self.disconnect_btn = ttk.Button(buttons_frame, text="❌ قطع الاتصال", command=self.disconnect_from_server, state=tk.DISABLED)
        self.disconnect_btn.pack(side=tk.LEFT, padx=5)

        self.test_btn = ttk.Button(buttons_frame, text="🧪 اختبار الاتصال", command=self.test_connection)
        self.test_btn.pack(side=tk.LEFT, padx=5)

        # حالة الاتصال
        self.connection_status = ttk.Label(conn_frame, text="❌ غير متصل", foreground="red")
        self.connection_status.grid(row=4, column=0, columnspan=3, pady=5)

        # معلومات السيرفر
        info_frame = ttk.LabelFrame(tab, text="معلومات السيرفر", padding=10)
        info_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        self.server_info_text = scrolledtext.ScrolledText(info_frame, height=10, state=tk.DISABLED)
        self.server_info_text.pack(fill=tk.BOTH, expand=True)

    def create_tool_management_tab(self):
        """تبويب إدارة الأداة العامة"""
        tab = ttk.Frame(self.notebook)
        self.notebook.add(tab, text="🛠️ إدارة الأداة")

        # إطار حالة الأداة
        status_frame = ttk.LabelFrame(tab, text="حالة الأداة", padding=10)
        status_frame.pack(fill=tk.X, padx=10, pady=5)

        # معلومات الأداة
        self.tool_name_label = ttk.Label(status_frame, text="اسم الأداة: غير محدد")
        self.tool_name_label.grid(row=0, column=0, sticky=tk.W, pady=2)

        self.tool_version_label = ttk.Label(status_frame, text="الإصدار: غير محدد")
        self.tool_version_label.grid(row=0, column=1, sticky=tk.W, pady=2)

        self.tool_status_label = ttk.Label(status_frame, text="الحالة: غير محدد")
        self.tool_status_label.grid(row=1, column=0, sticky=tk.W, pady=2)

        self.tool_files_label = ttk.Label(status_frame, text="عدد الملفات: غير محدد")
        self.tool_files_label.grid(row=1, column=1, sticky=tk.W, pady=2)

        # أزرار التحكم
        control_frame = ttk.LabelFrame(tab, text="التحكم في الأداة", padding=10)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack()

        self.start_tool_btn = ttk.Button(buttons_frame, text="▶️ تشغيل الأداة", command=self.start_tool)
        self.start_tool_btn.pack(side=tk.LEFT, padx=5)

        self.stop_tool_btn = ttk.Button(buttons_frame, text="⏹️ إيقاف الأداة", command=self.stop_tool)
        self.stop_tool_btn.pack(side=tk.LEFT, padx=5)

        self.refresh_tool_btn = ttk.Button(buttons_frame, text="🔄 تحديث الحالة", command=self.refresh_tool_status)
        self.refresh_tool_btn.pack(side=tk.LEFT, padx=5)

        # قائمة الملفات
        files_frame = ttk.LabelFrame(tab, text="ملفات الأداة", padding=10)
        files_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # جدول الملفات
        columns = ('ID', 'اسم الملف', 'الحجم', 'آخر تحديث')
        self.files_tree = ttk.Treeview(files_frame, columns=columns, show='headings', height=8)

        for col in columns:
            self.files_tree.heading(col, text=col)
            self.files_tree.column(col, width=150)

        scrollbar = ttk.Scrollbar(files_frame, orient=tk.VERTICAL, command=self.files_tree.yview)
        self.files_tree.configure(yscrollcommand=scrollbar.set)

        self.files_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_gfx_management_tab(self):
        """تبويب إدارة GFX"""
        tab = ttk.Frame(self.notebook)
        self.notebook.add(tab, text="🎮 إدارة GFX")

        # حالة برنامج GFX
        gfx_status_frame = ttk.LabelFrame(tab, text="حالة برنامج GFX", padding=10)
        gfx_status_frame.pack(fill=tk.X, padx=10, pady=5)

        self.gfx_status_label = ttk.Label(gfx_status_frame, text="الحالة: غير محدد")
        self.gfx_status_label.grid(row=0, column=0, sticky=tk.W, pady=2)

        self.gfx_process_label = ttk.Label(gfx_status_frame, text="معرف العملية: غير محدد")
        self.gfx_process_label.grid(row=0, column=1, sticky=tk.W, pady=2)

        # أزرار التحكم في GFX
        gfx_control_frame = ttk.LabelFrame(tab, text="التحكم في برنامج GFX", padding=10)
        gfx_control_frame.pack(fill=tk.X, padx=10, pady=5)

        gfx_buttons_frame = ttk.Frame(gfx_control_frame)
        gfx_buttons_frame.pack()

        self.start_gfx_btn = ttk.Button(gfx_buttons_frame, text="▶️ تشغيل GFX", command=self.start_gfx)
        self.start_gfx_btn.pack(side=tk.LEFT, padx=5)

        self.stop_gfx_btn = ttk.Button(gfx_buttons_frame, text="⏹️ إيقاف GFX", command=self.stop_gfx)
        self.stop_gfx_btn.pack(side=tk.LEFT, padx=5)

        self.restart_gfx_btn = ttk.Button(gfx_buttons_frame, text="🔄 إعادة تشغيل GFX", command=self.restart_gfx)
        self.restart_gfx_btn.pack(side=tk.LEFT, padx=5)

        self.refresh_gfx_btn = ttk.Button(gfx_buttons_frame, text="🔄 تحديث حالة GFX", command=self.refresh_gfx_status)
        self.refresh_gfx_btn.pack(side=tk.LEFT, padx=5)

        # إدارة الأجهزة
        devices_frame = ttk.LabelFrame(tab, text="الأجهزة المتصلة", padding=10)
        devices_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        self.devices_listbox = tk.Listbox(devices_frame, height=6)
        self.devices_listbox.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)

        devices_scrollbar = ttk.Scrollbar(devices_frame, orient=tk.VERTICAL, command=self.devices_listbox.yview)
        self.devices_listbox.configure(yscrollcommand=devices_scrollbar.set)
        devices_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        devices_btn_frame = ttk.Frame(devices_frame)
        devices_btn_frame.pack(fill=tk.X, pady=5)

        self.refresh_devices_btn = ttk.Button(devices_btn_frame, text="🔄 تحديث الأجهزة", command=self.refresh_devices)
        self.refresh_devices_btn.pack(side=tk.LEFT, padx=5)

        self.connect_device_btn = ttk.Button(devices_btn_frame, text="🔗 اتصال بالجهاز", command=self.connect_device)
        self.connect_device_btn.pack(side=tk.LEFT, padx=5)

    def create_updates_tab(self):
        """تبويب التحديثات"""
        tab = ttk.Frame(self.notebook)
        self.notebook.add(tab, text="📦 التحديثات")

        # رفع الملفات
        upload_frame = ttk.LabelFrame(tab, text="رفع الملفات", padding=10)
        upload_frame.pack(fill=tk.X, padx=10, pady=5)

        # اختيار الملف
        file_frame = ttk.Frame(upload_frame)
        file_frame.pack(fill=tk.X, pady=5)

        ttk.Label(file_frame, text="الملف المحدد:").pack(side=tk.LEFT)
        self.selected_file_var = tk.StringVar(value="لم يتم اختيار ملف")
        self.selected_file_label = ttk.Label(file_frame, textvariable=self.selected_file_var)
        self.selected_file_label.pack(side=tk.LEFT, padx=10)

        self.browse_file_btn = ttk.Button(file_frame, text="📁 اختيار ملف", command=self.browse_file)
        self.browse_file_btn.pack(side=tk.RIGHT)

        # معرف الملف
        id_frame = ttk.Frame(upload_frame)
        id_frame.pack(fill=tk.X, pady=5)

        ttk.Label(id_frame, text="معرف الملف:").pack(side=tk.LEFT)
        self.file_id_var = tk.StringVar(value="1")
        self.file_id_entry = ttk.Entry(id_frame, textvariable=self.file_id_var, width=10)
        self.file_id_entry.pack(side=tk.LEFT, padx=10)

        self.upload_file_btn = ttk.Button(id_frame, text="📤 رفع الملف", command=self.upload_file)
        self.upload_file_btn.pack(side=tk.RIGHT)

        # تحديث النظام
        system_update_frame = ttk.LabelFrame(tab, text="تحديث النظام", padding=10)
        system_update_frame.pack(fill=tk.X, padx=10, pady=5)

        update_buttons_frame = ttk.Frame(system_update_frame)
        update_buttons_frame.pack()

        self.backup_btn = ttk.Button(update_buttons_frame, text="💾 إنشاء نسخة احتياطية", command=self.create_backup)
        self.backup_btn.pack(side=tk.LEFT, padx=5)

        self.restore_btn = ttk.Button(update_buttons_frame, text="🔄 استعادة النسخة الاحتياطية", command=self.restore_backup)
        self.restore_btn.pack(side=tk.LEFT, padx=5)

        self.download_controller_btn = ttk.Button(update_buttons_frame, text="📥 تحميل أداة التحكم", command=self.download_controller_from_server)
        self.download_controller_btn.pack(side=tk.LEFT, padx=5)

        # سجل التحديثات
        update_log_frame = ttk.LabelFrame(tab, text="سجل التحديثات", padding=10)
        update_log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        self.update_log_text = scrolledtext.ScrolledText(update_log_frame, height=10, state=tk.DISABLED)
        self.update_log_text.pack(fill=tk.BOTH, expand=True)

    def create_monitoring_tab(self):
        """تبويب المراقبة"""
        tab = ttk.Frame(self.notebook)
        self.notebook.add(tab, text="📊 المراقبة")

        # إعدادات المراقبة
        monitor_settings_frame = ttk.LabelFrame(tab, text="إعدادات المراقبة", padding=10)
        monitor_settings_frame.pack(fill=tk.X, padx=10, pady=5)

        settings_frame = ttk.Frame(monitor_settings_frame)
        settings_frame.pack()

        ttk.Label(settings_frame, text="فترة التحديث (ثانية):").pack(side=tk.LEFT)
        self.monitor_interval_var = tk.StringVar(value="5")
        self.monitor_interval_entry = ttk.Entry(settings_frame, textvariable=self.monitor_interval_var, width=10)
        self.monitor_interval_entry.pack(side=tk.LEFT, padx=10)

        self.start_monitor_btn = ttk.Button(settings_frame, text="▶️ بدء المراقبة", command=self.start_monitoring)
        self.start_monitor_btn.pack(side=tk.LEFT, padx=5)

        self.stop_monitor_btn = ttk.Button(settings_frame, text="⏹️ إيقاف المراقبة", command=self.stop_monitoring, state=tk.DISABLED)
        self.stop_monitor_btn.pack(side=tk.LEFT, padx=5)

        # معلومات المراقبة
        monitor_info_frame = ttk.LabelFrame(tab, text="معلومات المراقبة", padding=10)
        monitor_info_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        self.monitor_text = scrolledtext.ScrolledText(monitor_info_frame, height=15, state=tk.DISABLED)
        self.monitor_text.pack(fill=tk.BOTH, expand=True)

    def create_logs_tab(self):
        """تبويب السجلات"""
        tab = ttk.Frame(self.notebook)
        self.notebook.add(tab, text="📝 السجلات")

        # أزرار السجلات
        logs_buttons_frame = ttk.Frame(tab)
        logs_buttons_frame.pack(fill=tk.X, padx=10, pady=5)

        self.clear_logs_btn = ttk.Button(logs_buttons_frame, text="🗑️ مسح السجلات", command=self.clear_logs)
        self.clear_logs_btn.pack(side=tk.LEFT, padx=5)

        self.save_logs_btn = ttk.Button(logs_buttons_frame, text="💾 حفظ السجلات", command=self.save_logs)
        self.save_logs_btn.pack(side=tk.LEFT, padx=5)

        self.refresh_logs_btn = ttk.Button(logs_buttons_frame, text="🔄 تحديث السجلات", command=self.refresh_logs)
        self.refresh_logs_btn.pack(side=tk.LEFT, padx=5)

        # منطقة السجلات
        self.logs_text = scrolledtext.ScrolledText(tab, height=20, state=tk.DISABLED)
        self.logs_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)

        self.status_label = ttk.Label(self.status_bar, text="جاهز")
        self.status_label.pack(side=tk.LEFT, padx=5)

        self.time_label = ttk.Label(self.status_bar, text="")
        self.time_label.pack(side=tk.RIGHT, padx=5)

        self.update_time()

    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)

    # ===== وظائف الاتصال =====

    def connect_to_server(self):
        """الاتصال بالسيرفر"""
        try:
            self.server_url = self.server_url_var.get()
            email = self.email_var.get()
            password = self.password_var.get()

            if not all([self.server_url, email, password]):
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
                return

            self.log_message("محاولة الاتصال بالسيرفر...")
            self.status_label.config(text="جاري الاتصال...")

            # تسجيل الدخول
            login_data = {
                "email": email,
                "password": password
            }

            response = requests.post(
                f"{self.server_url}/api/auth/login",
                json=login_data,
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.auth_token = data.get('token')
                    self.is_connected = True

                    self.connection_status.config(text="✅ متصل", foreground="green")
                    self.connect_btn.config(state=tk.DISABLED)
                    self.disconnect_btn.config(state=tk.NORMAL)

                    self.log_message("تم الاتصال بالسيرفر بنجاح")
                    self.status_label.config(text="متصل")

                    # تحديث معلومات السيرفر
                    self.update_server_info()

                    messagebox.showinfo("نجح", "تم الاتصال بالسيرفر بنجاح")
                else:
                    raise Exception(data.get('message', 'فشل تسجيل الدخول'))
            else:
                raise Exception(f"خطأ HTTP: {response.status_code}")

        except Exception as e:
            self.log_message(f"فشل الاتصال: {str(e)}")
            self.status_label.config(text="فشل الاتصال")
            messagebox.showerror("خطأ في الاتصال", f"فشل الاتصال بالسيرفر:\n{str(e)}")

    def disconnect_from_server(self):
        """قطع الاتصال من السيرفر"""
        self.auth_token = None
        self.is_connected = False

        self.connection_status.config(text="❌ غير متصل", foreground="red")
        self.connect_btn.config(state=tk.NORMAL)
        self.disconnect_btn.config(state=tk.DISABLED)

        self.log_message("تم قطع الاتصال من السيرفر")
        self.status_label.config(text="غير متصل")

        # إيقاف المراقبة إذا كانت تعمل
        if self.monitoring:
            self.stop_monitoring()

        messagebox.showinfo("تم", "تم قطع الاتصال من السيرفر")

    def test_connection(self):
        """اختبار الاتصال"""
        try:
            self.log_message("اختبار الاتصال...")
            self.status_label.config(text="جاري اختبار الاتصال...")

            response = requests.get(f"{self.server_url_var.get()}/", timeout=5)

            if response.status_code == 200:
                self.log_message("اختبار الاتصال نجح")
                self.status_label.config(text="اختبار الاتصال نجح")
                messagebox.showinfo("نجح", "السيرفر متاح ويعمل بشكل طبيعي")
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.log_message(f"فشل اختبار الاتصال: {str(e)}")
            self.status_label.config(text="فشل اختبار الاتصال")
            messagebox.showerror("فشل", f"فشل اختبار الاتصال:\n{str(e)}")

    def get_headers(self):
        """الحصول على headers للطلبات"""
        if not self.auth_token:
            raise Exception("غير متصل بالسيرفر")

        return {
            'Authorization': f'Bearer {self.auth_token}',
            'Content-Type': 'application/json'
        }

    # ===== وظائف إدارة الأداة =====

    def start_tool(self):
        """تشغيل الأداة"""
        try:
            if not self.is_connected:
                messagebox.showerror("خطأ", "يجب الاتصال بالسيرفر أولاً")
                return

            self.log_message("تشغيل الأداة...")
            self.status_label.config(text="جاري تشغيل الأداة...")

            response = requests.post(
                f"{self.server_url}/api/tool/start",
                headers=self.get_headers(),
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_message("تم تشغيل الأداة بنجاح")
                    self.status_label.config(text="تم تشغيل الأداة")
                    self.refresh_tool_status()
                    messagebox.showinfo("نجح", "تم تشغيل الأداة بنجاح")
                else:
                    raise Exception(data.get('message', 'فشل تشغيل الأداة'))
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.log_message(f"فشل تشغيل الأداة: {str(e)}")
            self.status_label.config(text="فشل تشغيل الأداة")
            messagebox.showerror("خطأ", f"فشل تشغيل الأداة:\n{str(e)}")

    def stop_tool(self):
        """إيقاف الأداة"""
        try:
            if not self.is_connected:
                messagebox.showerror("خطأ", "يجب الاتصال بالسيرفر أولاً")
                return

            self.log_message("إيقاف الأداة...")
            self.status_label.config(text="جاري إيقاف الأداة...")

            response = requests.post(
                f"{self.server_url}/api/tool/stop",
                headers=self.get_headers(),
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_message("تم إيقاف الأداة بنجاح")
                    self.status_label.config(text="تم إيقاف الأداة")
                    self.refresh_tool_status()
                    messagebox.showinfo("نجح", "تم إيقاف الأداة بنجاح")
                else:
                    raise Exception(data.get('message', 'فشل إيقاف الأداة'))
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.log_message(f"فشل إيقاف الأداة: {str(e)}")
            self.status_label.config(text="فشل إيقاف الأداة")
            messagebox.showerror("خطأ", f"فشل إيقاف الأداة:\n{str(e)}")

    def refresh_tool_status(self):
        """تحديث حالة الأداة"""
        try:
            if not self.is_connected:
                return

            response = requests.get(
                f"{self.server_url}/api/tool/status",
                headers=self.get_headers(),
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    tool = data.get('tool', {})

                    self.tool_name_label.config(text=f"اسم الأداة: {tool.get('name', 'غير محدد')}")
                    self.tool_version_label.config(text=f"الإصدار: {tool.get('version', 'غير محدد')}")
                    self.tool_status_label.config(text=f"الحالة: {tool.get('status', 'غير محدد')}")
                    self.tool_files_label.config(text=f"عدد الملفات: {tool.get('filesCount', 'غير محدد')}")

                    # تحديث قائمة الملفات
                    self.update_files_list(tool.get('files', []))

        except Exception as e:
            self.log_message(f"فشل تحديث حالة الأداة: {str(e)}")

    def update_files_list(self, files):
        """تحديث قائمة الملفات"""
        # مسح القائمة الحالية
        for item in self.files_tree.get_children():
            self.files_tree.delete(item)

        # إضافة الملفات الجديدة
        for file in files:
            self.files_tree.insert('', 'end', values=(
                file.get('id', ''),
                file.get('name', ''),
                f"{file.get('size', 0)} بايت",
                file.get('lastUpdated', '')
            ))

    # ===== وظائف إدارة GFX =====

    def start_gfx(self):
        """تشغيل برنامج GFX"""
        try:
            if not self.is_connected:
                messagebox.showerror("خطأ", "يجب الاتصال بالسيرفر أولاً")
                return

            self.log_message("تشغيل برنامج GFX...")
            self.status_label.config(text="جاري تشغيل GFX...")

            response = requests.post(
                f"{self.server_url}/api/tool/gfx/start",
                headers=self.get_headers(),
                timeout=15
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_message(f"تم تشغيل GFX بنجاح - معرف العملية: {data.get('processId')}")
                    self.status_label.config(text="تم تشغيل GFX")
                    self.refresh_gfx_status()
                    messagebox.showinfo("نجح", f"تم تشغيل برنامج GFX بنجاح\nمعرف العملية: {data.get('processId')}")
                else:
                    raise Exception(data.get('message', 'فشل تشغيل GFX'))
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.log_message(f"فشل تشغيل GFX: {str(e)}")
            self.status_label.config(text="فشل تشغيل GFX")
            messagebox.showerror("خطأ", f"فشل تشغيل برنامج GFX:\n{str(e)}")

    def stop_gfx(self):
        """إيقاف برنامج GFX"""
        try:
            if not self.is_connected:
                messagebox.showerror("خطأ", "يجب الاتصال بالسيرفر أولاً")
                return

            self.log_message("إيقاف برنامج GFX...")
            self.status_label.config(text="جاري إيقاف GFX...")

            response = requests.post(
                f"{self.server_url}/api/tool/gfx/stop",
                headers=self.get_headers(),
                timeout=15
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_message("تم إيقاف GFX بنجاح")
                    self.status_label.config(text="تم إيقاف GFX")
                    self.refresh_gfx_status()
                    messagebox.showinfo("نجح", "تم إيقاف برنامج GFX بنجاح")
                else:
                    raise Exception(data.get('message', 'فشل إيقاف GFX'))
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.log_message(f"فشل إيقاف GFX: {str(e)}")
            self.status_label.config(text="فشل إيقاف GFX")
            messagebox.showerror("خطأ", f"فشل إيقاف برنامج GFX:\n{str(e)}")

    def restart_gfx(self):
        """إعادة تشغيل برنامج GFX"""
        try:
            if not self.is_connected:
                messagebox.showerror("خطأ", "يجب الاتصال بالسيرفر أولاً")
                return

            self.log_message("إعادة تشغيل برنامج GFX...")
            self.status_label.config(text="جاري إعادة تشغيل GFX...")

            response = requests.post(
                f"{self.server_url}/api/tool/gfx/restart",
                headers=self.get_headers(),
                timeout=20
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_message(f"تم إعادة تشغيل GFX بنجاح - معرف العملية: {data.get('processId')}")
                    self.status_label.config(text="تم إعادة تشغيل GFX")
                    self.refresh_gfx_status()
                    messagebox.showinfo("نجح", f"تم إعادة تشغيل برنامج GFX بنجاح\nمعرف العملية الجديد: {data.get('processId')}")
                else:
                    raise Exception(data.get('message', 'فشل إعادة تشغيل GFX'))
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.log_message(f"فشل إعادة تشغيل GFX: {str(e)}")
            self.status_label.config(text="فشل إعادة تشغيل GFX")
            messagebox.showerror("خطأ", f"فشل إعادة تشغيل برنامج GFX:\n{str(e)}")

    def refresh_gfx_status(self):
        """تحديث حالة برنامج GFX"""
        try:
            if not self.is_connected:
                return

            response = requests.get(
                f"{self.server_url}/api/tool/gfx/status",
                headers=self.get_headers(),
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    status = data.get('status', 'غير محدد')
                    process_id = data.get('processId', 'غير محدد')
                    is_running = data.get('isRunning', False)

                    self.gfx_status_label.config(text=f"الحالة: {status}")
                    self.gfx_process_label.config(text=f"معرف العملية: {process_id}")

                    # تغيير لون الحالة
                    if is_running:
                        self.gfx_status_label.config(foreground="green")
                    else:
                        self.gfx_status_label.config(foreground="red")

        except Exception as e:
            self.log_message(f"فشل تحديث حالة GFX: {str(e)}")

    def refresh_devices(self):
        """تحديث قائمة الأجهزة"""
        try:
            if not self.is_connected:
                messagebox.showerror("خطأ", "يجب الاتصال بالسيرفر أولاً")
                return

            self.log_message("تحديث قائمة الأجهزة...")

            response = requests.get(
                f"{self.server_url}/api/tool/adb/devices",
                headers=self.get_headers(),
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    devices = data.get('devices', [])

                    # مسح القائمة الحالية
                    self.devices_listbox.delete(0, tk.END)

                    # إضافة الأجهزة الجديدة
                    for device in devices:
                        device_info = f"{device.get('id')} - {device.get('status')} ({device.get('type', 'unknown')})"
                        self.devices_listbox.insert(tk.END, device_info)

                    self.log_message(f"تم العثور على {len(devices)} جهاز")
                    messagebox.showinfo("تم", f"تم العثور على {len(devices)} جهاز متصل")
                else:
                    raise Exception(data.get('message', 'فشل الحصول على قائمة الأجهزة'))
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.log_message(f"فشل تحديث قائمة الأجهزة: {str(e)}")
            messagebox.showerror("خطأ", f"فشل تحديث قائمة الأجهزة:\n{str(e)}")

    def connect_device(self):
        """الاتصال بجهاز محدد"""
        try:
            if not self.is_connected:
                messagebox.showerror("خطأ", "يجب الاتصال بالسيرفر أولاً")
                return

            selection = self.devices_listbox.curselection()
            if not selection:
                messagebox.showwarning("تحذير", "يرجى اختيار جهاز من القائمة")
                return

            device_info = self.devices_listbox.get(selection[0])
            device_id = device_info.split(' - ')[0]

            self.log_message(f"الاتصال بالجهاز: {device_id}")

            response = requests.post(
                f"{self.server_url}/api/tool/adb/connect",
                headers=self.get_headers(),
                json={"deviceId": device_id},
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_message(f"تم الاتصال بالجهاز {device_id} بنجاح")
                    messagebox.showinfo("نجح", f"تم الاتصال بالجهاز {device_id} بنجاح")
                else:
                    raise Exception(data.get('message', 'فشل الاتصال بالجهاز'))
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.log_message(f"فشل الاتصال بالجهاز: {str(e)}")
            messagebox.showerror("خطأ", f"فشل الاتصال بالجهاز:\n{str(e)}")

    # ===== وظائف التحديثات =====

    def browse_file(self):
        """اختيار ملف للرفع"""
        file_path = filedialog.askopenfilename(
            title="اختيار ملف للرفع",
            filetypes=[
                ("Python files", "*.py"),
                ("JavaScript files", "*.js"),
                ("JSON files", "*.json"),
                ("Text files", "*.txt"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            self.selected_file_var.set(file_path)
            self.log_message(f"تم اختيار الملف: {file_path}")

    def upload_file(self):
        """رفع ملف إلى السيرفر"""
        try:
            if not self.is_connected:
                messagebox.showerror("خطأ", "يجب الاتصال بالسيرفر أولاً")
                return

            file_path = self.selected_file_var.get()
            if file_path == "لم يتم اختيار ملف" or not os.path.exists(file_path):
                messagebox.showerror("خطأ", "يرجى اختيار ملف صالح")
                return

            file_id = self.file_id_var.get()
            if not file_id:
                messagebox.showerror("خطأ", "يرجى إدخال معرف الملف")
                return

            self.log_message(f"رفع الملف: {file_path}")
            self.status_label.config(text="جاري رفع الملف...")

            # قراءة محتوى الملف
            with open(file_path, 'r', encoding='utf-8') as f:
                file_content = f.read()

            # إرسال الملف
            response = requests.put(
                f"{self.server_url}/api/tool/gfx/files",
                headers=self.get_headers(),
                json={
                    "fileId": int(file_id),
                    "fileContent": file_content
                },
                timeout=30
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    file_info = data.get('file', {})
                    backup_path = data.get('backupPath', '')

                    self.log_message(f"تم رفع الملف بنجاح: {file_info.get('name')}")
                    self.log_message(f"النسخة الاحتياطية: {backup_path}")
                    self.status_label.config(text="تم رفع الملف")

                    # تحديث سجل التحديثات
                    self.update_log_text.config(state=tk.NORMAL)
                    self.update_log_text.insert(tk.END, f"[{datetime.now().strftime('%H:%M:%S')}] تم رفع الملف: {file_info.get('name')}\n")
                    self.update_log_text.insert(tk.END, f"الحجم الجديد: {file_info.get('size')} بايت\n")
                    self.update_log_text.insert(tk.END, f"النسخة الاحتياطية: {backup_path}\n\n")
                    self.update_log_text.config(state=tk.DISABLED)
                    self.update_log_text.see(tk.END)

                    messagebox.showinfo("نجح", f"تم رفع الملف بنجاح\nالحجم الجديد: {file_info.get('size')} بايت")
                else:
                    raise Exception(data.get('message', 'فشل رفع الملف'))
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.log_message(f"فشل رفع الملف: {str(e)}")
            self.status_label.config(text="فشل رفع الملف")
            messagebox.showerror("خطأ", f"فشل رفع الملف:\n{str(e)}")

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            if not self.is_connected:
                messagebox.showerror("خطأ", "يجب الاتصال بالسيرفر أولاً")
                return

            self.log_message("إنشاء نسخة احتياطية...")
            self.status_label.config(text="جاري إنشاء نسخة احتياطية...")

            # هنا يمكن إضافة API endpoint للنسخ الاحتياطي
            # في الوقت الحالي سنعرض رسالة
            messagebox.showinfo("معلومات", "ميزة النسخ الاحتياطي قيد التطوير")

        except Exception as e:
            self.log_message(f"فشل إنشاء النسخة الاحتياطية: {str(e)}")
            messagebox.showerror("خطأ", f"فشل إنشاء النسخة الاحتياطية:\n{str(e)}")

    def restore_backup(self):
        """استعادة النسخة الاحتياطية"""
        try:
            if not self.is_connected:
                messagebox.showerror("خطأ", "يجب الاتصال بالسيرفر أولاً")
                return

            self.log_message("استعادة النسخة الاحتياطية...")
            self.status_label.config(text="جاري استعادة النسخة الاحتياطية...")

            # هنا يمكن إضافة API endpoint لاستعادة النسخ الاحتياطية
            # في الوقت الحالي سنعرض رسالة
            messagebox.showinfo("معلومات", "ميزة استعادة النسخ الاحتياطية قيد التطوير")

        except Exception as e:
            self.log_message(f"فشل استعادة النسخة الاحتياطية: {str(e)}")
            messagebox.showerror("خطأ", f"فشل استعادة النسخة الاحتياطية:\n{str(e)}")

    def download_controller_from_server(self):
        """تحميل أداة التحكم من السيرفر"""
        try:
            self.log_message("تحميل أداة التحكم من السيرفر...")
            self.status_label.config(text="جاري تحميل أداة التحكم...")

            # طلب معلومات التحميل أولاً
            response = requests.get(
                f"{self.server_url}/api/tool/download/info",
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    downloads = data.get('downloads', [])
                    controller_info = None

                    # البحث عن أداة التحكم
                    for download in downloads:
                        if download.get('id') == 'controller':
                            controller_info = download
                            break

                    if controller_info:
                        # تحميل الملف
                        download_url = f"{self.server_url}{controller_info['downloadUrl']}"

                        response = requests.get(download_url, timeout=30)

                        if response.status_code == 200:
                            # اختيار مكان الحفظ
                            save_path = filedialog.asksaveasfilename(
                                title="حفظ أداة التحكم",
                                defaultextension=".py",
                                filetypes=[("Python files", "*.py"), ("All files", "*.*")],
                                initialvalue="server_controller_new.py"
                            )

                            if save_path:
                                # حفظ الملف
                                with open(save_path, 'wb') as f:
                                    f.write(response.content)

                                self.log_message(f"تم تحميل أداة التحكم بنجاح: {save_path}")
                                self.status_label.config(text="تم تحميل أداة التحكم")

                                # تحديث سجل التحديثات
                                self.update_log_text.config(state=tk.NORMAL)
                                self.update_log_text.insert(tk.END, f"[{datetime.now().strftime('%H:%M:%S')}] تم تحميل أداة التحكم\n")
                                self.update_log_text.insert(tk.END, f"الحجم: {controller_info.get('size')} بايت\n")
                                self.update_log_text.insert(tk.END, f"المسار: {save_path}\n\n")
                                self.update_log_text.config(state=tk.DISABLED)
                                self.update_log_text.see(tk.END)

                                messagebox.showinfo("نجح", f"تم تحميل أداة التحكم بنجاح\nالمسار: {save_path}")
                            else:
                                self.log_message("تم إلغاء التحميل")
                                self.status_label.config(text="تم إلغاء التحميل")
                        else:
                            raise Exception(f"فشل تحميل الملف: HTTP {response.status_code}")
                    else:
                        raise Exception("أداة التحكم غير متوفرة للتحميل")
                else:
                    raise Exception(data.get('message', 'فشل الحصول على معلومات التحميل'))
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.log_message(f"فشل تحميل أداة التحكم: {str(e)}")
            self.status_label.config(text="فشل تحميل أداة التحكم")
            messagebox.showerror("خطأ", f"فشل تحميل أداة التحكم:\n{str(e)}")

    # ===== الوظائف المتقدمة الجديدة =====

    def new_connection(self):
        """إنشاء اتصال جديد"""
        if self.is_connected:
            result = messagebox.askyesno("اتصال جديد", "هل تريد قطع الاتصال الحالي وإنشاء اتصال جديد؟")
            if result:
                self.disconnect_from_server()
            else:
                return

        # فتح نافذة اتصال جديد
        self.open_new_connection_dialog()

    def open_new_connection_dialog(self):
        """فتح نافذة اتصال جديد"""
        dialog = tk.Toplevel(self.root)
        dialog.title("اتصال جديد")
        dialog.geometry("400x300")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # محتوى النافذة
        ttk.Label(dialog, text="إعدادات الاتصال الجديد", font=self.font_title).pack(pady=10)

        # عنوان السيرفر
        ttk.Label(dialog, text="عنوان السيرفر:").pack(anchor=tk.W, padx=20)
        server_entry = ttk.Entry(dialog, width=50)
        server_entry.pack(padx=20, pady=5)
        server_entry.insert(0, self.server_url)

        # البريد الإلكتروني
        ttk.Label(dialog, text="البريد الإلكتروني:").pack(anchor=tk.W, padx=20)
        email_entry = ttk.Entry(dialog, width=50)
        email_entry.pack(padx=20, pady=5)
        email_entry.insert(0, self.email_var.get() if hasattr(self, 'email_var') else "")

        # كلمة المرور
        ttk.Label(dialog, text="كلمة المرور:").pack(anchor=tk.W, padx=20)
        password_entry = ttk.Entry(dialog, width=50, show="*")
        password_entry.pack(padx=20, pady=5)

        # أزرار التحكم
        buttons_frame = ttk.Frame(dialog)
        buttons_frame.pack(pady=20)

        def connect_new():
            self.server_url = server_entry.get()
            if hasattr(self, 'email_var'):
                self.email_var.set(email_entry.get())
                self.password_var.set(password_entry.get())
            dialog.destroy()
            self.connect_to_server()

        ttk.Button(buttons_frame, text="اتصال", command=connect_new).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="إلغاء", command=dialog.destroy).pack(side=tk.LEFT, padx=5)

    def quick_connect(self):
        """اتصال سريع"""
        if not self.is_connected:
            self.connect_to_server()
        else:
            messagebox.showinfo("معلومات", "أنت متصل بالفعل")

    def toggle_monitoring(self):
        """تبديل حالة المراقبة"""
        if self.monitoring:
            self.stop_monitoring()
        else:
            self.start_monitoring()

    def refresh_all(self):
        """تحديث جميع البيانات"""
        if self.is_connected:
            self.refresh_tool_status()
            self.refresh_gfx_status()
            self.refresh_devices()
            self.log_message("تم تحديث جميع البيانات")
        else:
            messagebox.showwarning("تحذير", "يجب الاتصال بالسيرفر أولاً")

    def open_preferences(self):
        """فتح نافذة التفضيلات"""
        prefs_window = tk.Toplevel(self.root)
        prefs_window.title("التفضيلات")
        prefs_window.geometry("500x400")
        prefs_window.resizable(False, False)
        prefs_window.transient(self.root)
        prefs_window.grab_set()

        # إنشاء تبويبات التفضيلات
        prefs_notebook = ttk.Notebook(prefs_window)
        prefs_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # تبويب عام
        general_tab = ttk.Frame(prefs_notebook)
        prefs_notebook.add(general_tab, text="عام")

        # تبويب الأمان
        security_tab = ttk.Frame(prefs_notebook)
        prefs_notebook.add(security_tab, text="الأمان")

        # تبويب المراقبة
        monitoring_tab = ttk.Frame(prefs_notebook)
        prefs_notebook.add(monitoring_tab, text="المراقبة")

        # إعدادات عامة
        ttk.Label(general_tab, text="الإعدادات العامة", font=self.font_title).pack(pady=10)

        ttk.Checkbutton(general_tab, text="تفعيل الإشعارات").pack(anchor=tk.W, padx=20)
        ttk.Checkbutton(general_tab, text="إعادة الاتصال التلقائي").pack(anchor=tk.W, padx=20)
        ttk.Checkbutton(general_tab, text="فحص التحديثات تلقائياً").pack(anchor=tk.W, padx=20)

        # أزرار التحكم
        buttons_frame = ttk.Frame(prefs_window)
        buttons_frame.pack(pady=10)

        ttk.Button(buttons_frame, text="حفظ", command=prefs_window.destroy).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="إلغاء", command=prefs_window.destroy).pack(side=tk.LEFT, padx=5)

    def toggle_fullscreen(self):
        """تبديل ملء الشاشة"""
        current_state = self.root.attributes('-fullscreen')
        self.root.attributes('-fullscreen', not current_state)

    def zoom_in(self):
        """تكبير الواجهة"""
        current_font_size = self.font_normal.actual()['size']
        new_size = min(current_font_size + 1, 20)
        self.font_normal.configure(size=new_size)

    def zoom_out(self):
        """تصغير الواجهة"""
        current_font_size = self.font_normal.actual()['size']
        new_size = max(current_font_size - 1, 8)
        self.font_normal.configure(size=new_size)

    def zoom_reset(self):
        """إعادة تعيين حجم الخط"""
        self.font_normal.configure(size=10)

    def show_help(self):
        """عرض دليل المساعدة"""
        help_window = tk.Toplevel(self.root)
        help_window.title("دليل الاستخدام")
        help_window.geometry("600x500")

        help_text = scrolledtext.ScrolledText(help_window, wrap=tk.WORD)
        help_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        help_content = """
🎮 دليل استخدام أداة التحكم في السيرفر

📋 نظرة عامة:
أداة شاملة للتحكم في السيرفر وإدارة GFX عن بُعد

🔗 الاتصال:
1. أدخل عنوان السيرفر
2. أدخل بيانات تسجيل الدخول
3. اضغط "اتصال"

🛠️ إدارة الأداة:
- تشغيل/إيقاف الأداة
- مراقبة الحالة
- عرض الملفات

🎮 إدارة GFX:
- تشغيل/إيقاف برنامج GFX
- إدارة الأجهزة المتصلة
- مراقبة العمليات

📦 التحديثات:
- رفع ملفات جديدة
- إنشاء نسخ احتياطية
- تحديث النظام

📊 المراقبة:
- مراقبة الأداء المباشر
- إحصائيات مفصلة
- تنبيهات تلقائية

⌨️ اختصارات لوحة المفاتيح:
Ctrl+N: اتصال جديد
Ctrl+S: حفظ الإعدادات
Ctrl+P: التفضيلات
F5: تحديث البيانات
F11: ملء الشاشة
Ctrl+Q: خروج
        """

        help_text.insert(tk.END, help_content)
        help_text.config(state=tk.DISABLED)

    def show_shortcuts(self):
        """عرض اختصارات لوحة المفاتيح"""
        shortcuts_window = tk.Toplevel(self.root)
        shortcuts_window.title("اختصارات لوحة المفاتيح")
        shortcuts_window.geometry("400x300")

        shortcuts_text = scrolledtext.ScrolledText(shortcuts_window, wrap=tk.WORD)
        shortcuts_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        shortcuts_content = """
⌨️ اختصارات لوحة المفاتيح

📁 ملف:
Ctrl+N - اتصال جديد
Ctrl+S - حفظ الإعدادات
Ctrl+Q - خروج

✏️ تحرير:
Ctrl+P - التفضيلات

🔧 أدوات:
Ctrl+T - اختبار الاتصال
F5 - تحديث البيانات

👁️ عرض:
F11 - ملء الشاشة
Ctrl++ - تكبير
Ctrl+- - تصغير
Ctrl+0 - حجم طبيعي

🎮 عام:
Esc - إلغاء العملية الحالية
Enter - تأكيد
Tab - الانتقال بين العناصر
        """

        shortcuts_text.insert(tk.END, shortcuts_content)
        shortcuts_text.config(state=tk.DISABLED)

    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = f"""
🎮 أداة التحكم في السيرفر
Server Controller

الإصدار: 2.0.0 Professional
تاريخ الإصدار: {datetime.now().strftime('%Y-%m-%d')}

المطور: فريق JO GAME TOOL
الوصف: أداة Python شاملة للتحكم في السيرفر وإدارة GFX

المميزات:
✅ واجهة احترافية متقدمة
✅ إدارة كاملة للسيرفر
✅ مراقبة الأداء المباشر
✅ نظام أمان متكامل
✅ نسخ احتياطية تلقائية
✅ تحديثات تلقائية

© 2024 JO GAME TOOL. جميع الحقوق محفوظة.
        """

        messagebox.showinfo("حول البرنامج", about_text)

    def system_diagnostics(self):
        """تشخيص النظام"""
        diag_window = tk.Toplevel(self.root)
        diag_window.title("تشخيص النظام")
        diag_window.geometry("500x400")

        diag_text = scrolledtext.ScrolledText(diag_window, wrap=tk.WORD)
        diag_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # جمع معلومات النظام
        try:
            import platform
            system_info = f"""
🔍 تشخيص النظام - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

💻 معلومات النظام:
نظام التشغيل: {platform.system()} {platform.release()}
المعمارية: {platform.architecture()[0]}
المعالج: {platform.processor()}
اسم الجهاز: {platform.node()}

🐍 معلومات Python:
إصدار Python: {platform.python_version()}
المترجم: {platform.python_implementation()}

📊 معلومات الأداة:
حالة الاتصال: {'متصل' if self.is_connected else 'غير متصل'}
عنوان السيرفر: {self.server_url}
حالة المراقبة: {'مفعلة' if self.monitoring else 'معطلة'}

💾 قاعدة البيانات:
مسار قاعدة البيانات: {self.db_path}
حالة الاتصال: {'متصل' if hasattr(self, 'conn') else 'غير متصل'}

📁 الملفات:
مجلد السجلات: logs/
ملف الإعدادات: server_controller_settings.json
ملف قاعدة البيانات: server_controller.db
            """

            diag_text.insert(tk.END, system_info)

        except Exception as e:
            diag_text.insert(tk.END, f"خطأ في جمع معلومات النظام: {str(e)}")

        diag_text.config(state=tk.DISABLED)

    def check_for_updates(self):
        """فحص التحديثات"""
        try:
            if not self.is_connected:
                messagebox.showwarning("تحذير", "يجب الاتصال بالسيرفر لفحص التحديثات")
                return

            self.log_message("فحص التحديثات...")

            # فحص التحديثات من السيرفر
            response = requests.get(
                f"{self.server_url}/api/tool/download/info",
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    # مقارنة الإصدارات
                    server_version = data.get('serverInfo', {}).get('version', '1.0.0')
                    current_version = "2.0.0"

                    if server_version > current_version:
                        result = messagebox.askyesno(
                            "تحديث متوفر",
                            f"يتوفر إصدار جديد: {server_version}\nهل تريد تحميله؟"
                        )
                        if result:
                            self.download_controller_from_server()
                    else:
                        messagebox.showinfo("لا توجد تحديثات", "أنت تستخدم أحدث إصدار")

        except Exception as e:
            self.log_message(f"فشل فحص التحديثات: {str(e)}")
            messagebox.showerror("خطأ", f"فشل فحص التحديثات:\n{str(e)}")

    def create_automatic_backup(self):
        """إنشاء نسخة احتياطية تلقائية"""
        try:
            backup_dir = "backups"
            os.makedirs(backup_dir, exist_ok=True)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"server_controller_backup_{timestamp}.zip"
            backup_path = os.path.join(backup_dir, backup_filename)

            # إنشاء ملف ZIP
            with zipfile.ZipFile(backup_path, 'w') as backup_zip:
                # إضافة ملفات الإعدادات
                if os.path.exists("server_controller_settings.json"):
                    backup_zip.write("server_controller_settings.json")

                # إضافة قاعدة البيانات
                if os.path.exists(self.db_path):
                    backup_zip.write(self.db_path)

                # إضافة السجلات
                if os.path.exists("logs"):
                    for root, dirs, files in os.walk("logs"):
                        for file in files:
                            file_path = os.path.join(root, file)
                            backup_zip.write(file_path)

            # حفظ معلومات النسخة الاحتياطية في قاعدة البيانات
            file_size = os.path.getsize(backup_path)
            self.cursor.execute('''
                INSERT INTO backups (filename, file_path, file_size, backup_type)
                VALUES (?, ?, ?, ?)
            ''', (backup_filename, backup_path, file_size, 'automatic'))
            self.conn.commit()

            self.logger.info(f"تم إنشاء نسخة احتياطية تلقائية: {backup_path}")

        except Exception as e:
            self.logger.error(f"فشل إنشاء النسخة الاحتياطية التلقائية: {str(e)}")

    def import_settings(self):
        """استيراد الإعدادات"""
        file_path = filedialog.askopenfilename(
            title="استيراد الإعدادات",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                # تطبيق الإعدادات
                for key, value in settings.items():
                    if hasattr(self, key):
                        setattr(self, key, value)

                self.log_message(f"تم استيراد الإعدادات من: {file_path}")
                messagebox.showinfo("نجح", "تم استيراد الإعدادات بنجاح")

            except Exception as e:
                self.log_message(f"فشل استيراد الإعدادات: {str(e)}")
                messagebox.showerror("خطأ", f"فشل استيراد الإعدادات:\n{str(e)}")

    def export_settings(self):
        """تصدير الإعدادات"""
        file_path = filedialog.asksaveasfilename(
            title="تصدير الإعدادات",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if file_path:
            try:
                settings = {
                    'server_url': self.server_url,
                    'theme': self.theme,
                    'language': self.language,
                    'notifications_enabled': self.notifications_enabled,
                    'auto_reconnect': self.auto_reconnect,
                    'session_timeout': self.session_timeout,
                    'backup_enabled': self.backup_enabled,
                    'backup_interval': self.backup_interval
                }

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(settings, f, ensure_ascii=False, indent=2)

                self.log_message(f"تم تصدير الإعدادات إلى: {file_path}")
                messagebox.showinfo("نجح", "تم تصدير الإعدادات بنجاح")

            except Exception as e:
                self.log_message(f"فشل تصدير الإعدادات: {str(e)}")
                messagebox.showerror("خطأ", f"فشل تصدير الإعدادات:\n{str(e)}")

    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        result = messagebox.askyesno(
            "إعادة تعيين الإعدادات",
            "هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟"
        )

        if result:
            # إعادة تعيين الإعدادات
            self.theme = "default"
            self.language = "ar"
            self.notifications_enabled = True
            self.auto_reconnect = True
            self.session_timeout = 3600
            self.backup_enabled = True
            self.backup_interval = 24

            self.log_message("تم إعادة تعيين الإعدادات")
            messagebox.showinfo("تم", "تم إعادة تعيين الإعدادات بنجاح")

    def load_advanced_settings(self):
        """تحميل الإعدادات المتقدمة"""
        try:
            # تحميل من قاعدة البيانات
            self.cursor.execute('SELECT key, value FROM settings')
            settings = dict(self.cursor.fetchall())

            # تطبيق الإعدادات
            for key, value in settings.items():
                if hasattr(self, key):
                    # تحويل القيم النصية إلى الأنواع المناسبة
                    if key in ['session_timeout', 'backup_interval']:
                        setattr(self, key, int(value))
                    elif key in ['notifications_enabled', 'auto_reconnect', 'backup_enabled']:
                        setattr(self, key, value.lower() == 'true')
                    else:
                        setattr(self, key, value)

            self.logger.info("تم تحميل الإعدادات المتقدمة")

        except Exception as e:
            self.logger.error(f"فشل تحميل الإعدادات المتقدمة: {str(e)}")

    def save_advanced_settings(self):
        """حفظ الإعدادات المتقدمة"""
        try:
            settings = {
                'server_url': self.server_url,
                'theme': self.theme,
                'language': self.language,
                'notifications_enabled': str(self.notifications_enabled),
                'auto_reconnect': str(self.auto_reconnect),
                'session_timeout': str(self.session_timeout),
                'backup_enabled': str(self.backup_enabled),
                'backup_interval': str(self.backup_interval)
            }

            # حفظ في قاعدة البيانات
            for key, value in settings.items():
                self.cursor.execute('''
                    INSERT OR REPLACE INTO settings (key, value, updated_at)
                    VALUES (?, ?, CURRENT_TIMESTAMP)
                ''', (key, value))

            self.conn.commit()
            self.logger.info("تم حفظ الإعدادات المتقدمة")

        except Exception as e:
            self.logger.error(f"فشل حفظ الإعدادات المتقدمة: {str(e)}")

    # ===== وظائف المراقبة =====

    def start_monitoring(self):
        """بدء المراقبة"""
        try:
            if not self.is_connected:
                messagebox.showerror("خطأ", "يجب الاتصال بالسيرفر أولاً")
                return

            if self.monitoring:
                messagebox.showwarning("تحذير", "المراقبة تعمل بالفعل")
                return

            interval = int(self.monitor_interval_var.get())
            if interval < 1:
                messagebox.showerror("خطأ", "فترة التحديث يجب أن تكون أكبر من 0")
                return

            self.monitoring = True
            self.start_monitor_btn.config(state=tk.DISABLED)
            self.stop_monitor_btn.config(state=tk.NORMAL)

            # بدء خيط المراقبة
            self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
            self.monitor_thread.start()

            self.log_message(f"بدء المراقبة كل {interval} ثانية")
            messagebox.showinfo("تم", f"تم بدء المراقبة كل {interval} ثانية")

        except ValueError:
            messagebox.showerror("خطأ", "فترة التحديث يجب أن تكون رقم صحيح")
        except Exception as e:
            self.log_message(f"فشل بدء المراقبة: {str(e)}")
            messagebox.showerror("خطأ", f"فشل بدء المراقبة:\n{str(e)}")

    def stop_monitoring(self):
        """إيقاف المراقبة"""
        self.monitoring = False
        self.start_monitor_btn.config(state=tk.NORMAL)
        self.stop_monitor_btn.config(state=tk.DISABLED)

        self.log_message("تم إيقاف المراقبة")
        messagebox.showinfo("تم", "تم إيقاف المراقبة")

    def monitor_loop(self):
        """حلقة المراقبة"""
        while self.monitoring:
            try:
                interval = int(self.monitor_interval_var.get())

                # جمع معلومات المراقبة
                monitor_data = self.collect_monitoring_data()

                # تحديث واجهة المراقبة
                self.root.after(0, self.update_monitoring_display, monitor_data)

                # انتظار الفترة المحددة
                time.sleep(interval)

            except Exception as e:
                self.log_message(f"خطأ في المراقبة: {str(e)}")
                time.sleep(5)  # انتظار 5 ثواني في حالة الخطأ

    def collect_monitoring_data(self):
        """جمع بيانات المراقبة"""
        data = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'tool_status': 'غير محدد',
            'gfx_status': 'غير محدد',
            'devices_count': 0,
            'server_responsive': False
        }

        try:
            # حالة الأداة
            response = requests.get(
                f"{self.server_url}/api/tool/status",
                headers=self.get_headers(),
                timeout=5
            )
            if response.status_code == 200:
                tool_data = response.json()
                if tool_data.get('success'):
                    data['tool_status'] = tool_data.get('tool', {}).get('status', 'غير محدد')
                    data['server_responsive'] = True

        except:
            pass

        try:
            # حالة GFX
            response = requests.get(
                f"{self.server_url}/api/tool/gfx/status",
                headers=self.get_headers(),
                timeout=5
            )
            if response.status_code == 200:
                gfx_data = response.json()
                if gfx_data.get('success'):
                    data['gfx_status'] = gfx_data.get('status', 'غير محدد')

        except:
            pass

        try:
            # عدد الأجهزة
            response = requests.get(
                f"{self.server_url}/api/tool/adb/devices",
                headers=self.get_headers(),
                timeout=5
            )
            if response.status_code == 200:
                devices_data = response.json()
                if devices_data.get('success'):
                    data['devices_count'] = len(devices_data.get('devices', []))

        except:
            pass

        return data

    def update_monitoring_display(self, data):
        """تحديث عرض المراقبة"""
        try:
            self.monitor_text.config(state=tk.NORMAL)

            # إضافة البيانات الجديدة
            monitor_info = f"""
[{data['timestamp']}]
حالة السيرفر: {'متجاوب' if data['server_responsive'] else 'غير متجاوب'}
حالة الأداة: {data['tool_status']}
حالة GFX: {data['gfx_status']}
عدد الأجهزة: {data['devices_count']}
{'='*50}
"""

            self.monitor_text.insert(tk.END, monitor_info)

            # الحفاظ على آخر 100 سطر فقط
            lines = self.monitor_text.get('1.0', tk.END).split('\n')
            if len(lines) > 100:
                self.monitor_text.delete('1.0', f'{len(lines)-100}.0')

            self.monitor_text.config(state=tk.DISABLED)
            self.monitor_text.see(tk.END)

        except Exception as e:
            self.log_message(f"خطأ في تحديث المراقبة: {str(e)}")

    # ===== وظائف السجلات =====

    def log_message(self, message):
        """إضافة رسالة إلى السجل"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            log_entry = f"[{timestamp}] {message}\n"

            self.logs_text.config(state=tk.NORMAL)
            self.logs_text.insert(tk.END, log_entry)

            # الحفاظ على آخر 1000 سطر فقط
            lines = self.logs_text.get('1.0', tk.END).split('\n')
            if len(lines) > 1000:
                self.logs_text.delete('1.0', f'{len(lines)-1000}.0')

            self.logs_text.config(state=tk.DISABLED)
            self.logs_text.see(tk.END)

        except Exception as e:
            print(f"خطأ في تسجيل الرسالة: {str(e)}")

    def clear_logs(self):
        """مسح السجلات"""
        self.logs_text.config(state=tk.NORMAL)
        self.logs_text.delete('1.0', tk.END)
        self.logs_text.config(state=tk.DISABLED)
        self.log_message("تم مسح السجلات")

    def save_logs(self):
        """حفظ السجلات في ملف"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="حفظ السجلات",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )

            if file_path:
                logs_content = self.logs_text.get('1.0', tk.END)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(logs_content)

                self.log_message(f"تم حفظ السجلات في: {file_path}")
                messagebox.showinfo("تم", f"تم حفظ السجلات في:\n{file_path}")

        except Exception as e:
            self.log_message(f"فشل حفظ السجلات: {str(e)}")
            messagebox.showerror("خطأ", f"فشل حفظ السجلات:\n{str(e)}")

    def refresh_logs(self):
        """تحديث السجلات"""
        self.log_message("تم تحديث السجلات")

    # ===== وظائف إضافية =====

    def update_server_info(self):
        """تحديث معلومات السيرفر"""
        try:
            # الحصول على معلومات الأداة
            response = requests.get(
                f"{self.server_url}/api/tool/status",
                headers=self.get_headers(),
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    tool = data.get('tool', {})

                    server_info = f"""
معلومات السيرفر:
العنوان: {self.server_url}
حالة الاتصال: متصل ✅

معلومات الأداة:
الاسم: {tool.get('name', 'غير محدد')}
الإصدار: {tool.get('version', 'غير محدد')}
الحالة: {tool.get('status', 'غير محدد')}
عدد الملفات: {tool.get('filesCount', 'غير محدد')}

الإعدادات:
GFX مفعل: {'نعم' if tool.get('config', {}).get('gfxEnabled') else 'لا'}
مسار ADB: {tool.get('config', {}).get('adbPath', 'غير محدد')}
الذاكرة القصوى: {tool.get('config', {}).get('maxMemory', 'غير محدد')} MB

تاريخ الإنشاء: {tool.get('createdAt', 'غير محدد')}
آخر تحديث: {tool.get('updatedAt', 'غير محدد')}
"""

                    self.server_info_text.config(state=tk.NORMAL)
                    self.server_info_text.delete('1.0', tk.END)
                    self.server_info_text.insert('1.0', server_info)
                    self.server_info_text.config(state=tk.DISABLED)

        except Exception as e:
            self.log_message(f"فشل تحديث معلومات السيرفر: {str(e)}")

    def load_settings(self):
        """تحميل الإعدادات المحفوظة"""
        try:
            settings_file = "server_controller_settings.json"
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                self.server_url_var.set(settings.get('server_url', 'http://localhost:3000'))
                self.email_var.set(settings.get('email', '<EMAIL>'))
                # لا نحفظ كلمة المرور لأسباب أمنية

        except Exception as e:
            self.log_message(f"فشل تحميل الإعدادات: {str(e)}")

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            settings = {
                'server_url': self.server_url_var.get(),
                'email': self.email_var.get()
                # لا نحفظ كلمة المرور لأسباب أمنية
            }

            settings_file = "server_controller_settings.json"
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.log_message(f"فشل حفظ الإعدادات: {str(e)}")

    def on_closing(self):
        """عند إغلاق البرنامج"""
        try:
            # إيقاف المراقبة
            if self.monitoring:
                self.stop_monitoring()

            # حفظ الإعدادات
            self.save_settings()

            # قطع الاتصال
            if self.is_connected:
                self.disconnect_from_server()

            self.root.destroy()

        except Exception as e:
            print(f"خطأ عند الإغلاق: {str(e)}")
            self.root.destroy()

    def run(self):
        """تشغيل البرنامج"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.log_message("تم تشغيل أداة التحكم في السيرفر")
        self.root.mainloop()

# ===== تشغيل البرنامج =====

if __name__ == "__main__":
    try:
        app = ServerController()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل البرنامج: {str(e)}")
        input("اضغط Enter للخروج...")
