#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎮 أداة التحكم في السيرفر - Server Controller
أداة Python شاملة للتحكم في السيرفر عن بُعد

المميزات:
- تشغيل/إيقاف الأداة
- تحديث الملفات
- مراقبة الحالة
- إدارة GFX
- رفع النسخ الجديدة
"""

import os
import sys
import json
import time
import requests
import threading
from datetime import datetime
from pathlib import Path
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
from tkinter.font import Font
import webbrowser

class ServerController:
    def __init__(self):
        """تهيئة أداة التحكم في السيرفر"""
        self.root = tk.Tk()
        self.setup_window()

        # إعدادات الاتصال
        self.server_url = "http://localhost:3000"
        self.auth_token = None
        self.is_connected = False

        # إعدادات المراقبة
        self.monitoring = False
        self.monitor_thread = None

        # إنشاء الواجهة
        self.create_widgets()
        self.load_settings()

    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("🎮 أداة التحكم في السيرفر - Server Controller")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)

        # تعيين الأيقونة (إذا كانت متوفرة)
        try:
            self.root.iconbitmap("server_icon.ico")
        except:
            pass

        # تعيين الخطوط
        self.font_title = Font(family="Arial", size=14, weight="bold")
        self.font_normal = Font(family="Arial", size=10)
        self.font_small = Font(family="Arial", size=8)

        # ألوان الواجهة
        self.colors = {
            'bg': '#2b2b2b',
            'fg': '#ffffff',
            'accent': '#4CAF50',
            'warning': '#FF9800',
            'error': '#F44336',
            'info': '#2196F3'
        }

        self.root.configure(bg=self.colors['bg'])

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إنشاء النوت بوك للتبويبات
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # تبويب الاتصال
        self.create_connection_tab()

        # تبويب إدارة الأداة
        self.create_tool_management_tab()

        # تبويب إدارة GFX
        self.create_gfx_management_tab()

        # تبويب التحديثات
        self.create_updates_tab()

        # تبويب المراقبة
        self.create_monitoring_tab()

        # تبويب السجلات
        self.create_logs_tab()

        # شريط الحالة
        self.create_status_bar()

    def create_connection_tab(self):
        """تبويب إعدادات الاتصال"""
        tab = ttk.Frame(self.notebook)
        self.notebook.add(tab, text="🔗 الاتصال")

        # إطار إعدادات الاتصال
        conn_frame = ttk.LabelFrame(tab, text="إعدادات الاتصال", padding=10)
        conn_frame.pack(fill=tk.X, padx=10, pady=5)

        # عنوان السيرفر
        ttk.Label(conn_frame, text="عنوان السيرفر:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.server_url_var = tk.StringVar(value=self.server_url)
        self.server_url_entry = ttk.Entry(conn_frame, textvariable=self.server_url_var, width=40)
        self.server_url_entry.grid(row=0, column=1, columnspan=2, sticky=tk.W+tk.E, pady=2)

        # بيانات تسجيل الدخول
        ttk.Label(conn_frame, text="البريد الإلكتروني:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.email_var = tk.StringVar(value="<EMAIL>")
        self.email_entry = ttk.Entry(conn_frame, textvariable=self.email_var, width=30)
        self.email_entry.grid(row=1, column=1, sticky=tk.W+tk.E, pady=2)

        ttk.Label(conn_frame, text="كلمة المرور:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.password_var = tk.StringVar(value="admin123")
        self.password_entry = ttk.Entry(conn_frame, textvariable=self.password_var, show="*", width=30)
        self.password_entry.grid(row=2, column=1, sticky=tk.W+tk.E, pady=2)

        # أزرار الاتصال
        buttons_frame = ttk.Frame(conn_frame)
        buttons_frame.grid(row=3, column=0, columnspan=3, pady=10)

        self.connect_btn = ttk.Button(buttons_frame, text="🔗 اتصال", command=self.connect_to_server)
        self.connect_btn.pack(side=tk.LEFT, padx=5)

        self.disconnect_btn = ttk.Button(buttons_frame, text="❌ قطع الاتصال", command=self.disconnect_from_server, state=tk.DISABLED)
        self.disconnect_btn.pack(side=tk.LEFT, padx=5)

        self.test_btn = ttk.Button(buttons_frame, text="🧪 اختبار الاتصال", command=self.test_connection)
        self.test_btn.pack(side=tk.LEFT, padx=5)

        # حالة الاتصال
        self.connection_status = ttk.Label(conn_frame, text="❌ غير متصل", foreground="red")
        self.connection_status.grid(row=4, column=0, columnspan=3, pady=5)

        # معلومات السيرفر
        info_frame = ttk.LabelFrame(tab, text="معلومات السيرفر", padding=10)
        info_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        self.server_info_text = scrolledtext.ScrolledText(info_frame, height=10, state=tk.DISABLED)
        self.server_info_text.pack(fill=tk.BOTH, expand=True)

    def create_tool_management_tab(self):
        """تبويب إدارة الأداة العامة"""
        tab = ttk.Frame(self.notebook)
        self.notebook.add(tab, text="🛠️ إدارة الأداة")

        # إطار حالة الأداة
        status_frame = ttk.LabelFrame(tab, text="حالة الأداة", padding=10)
        status_frame.pack(fill=tk.X, padx=10, pady=5)

        # معلومات الأداة
        self.tool_name_label = ttk.Label(status_frame, text="اسم الأداة: غير محدد")
        self.tool_name_label.grid(row=0, column=0, sticky=tk.W, pady=2)

        self.tool_version_label = ttk.Label(status_frame, text="الإصدار: غير محدد")
        self.tool_version_label.grid(row=0, column=1, sticky=tk.W, pady=2)

        self.tool_status_label = ttk.Label(status_frame, text="الحالة: غير محدد")
        self.tool_status_label.grid(row=1, column=0, sticky=tk.W, pady=2)

        self.tool_files_label = ttk.Label(status_frame, text="عدد الملفات: غير محدد")
        self.tool_files_label.grid(row=1, column=1, sticky=tk.W, pady=2)

        # أزرار التحكم
        control_frame = ttk.LabelFrame(tab, text="التحكم في الأداة", padding=10)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack()

        self.start_tool_btn = ttk.Button(buttons_frame, text="▶️ تشغيل الأداة", command=self.start_tool)
        self.start_tool_btn.pack(side=tk.LEFT, padx=5)

        self.stop_tool_btn = ttk.Button(buttons_frame, text="⏹️ إيقاف الأداة", command=self.stop_tool)
        self.stop_tool_btn.pack(side=tk.LEFT, padx=5)

        self.refresh_tool_btn = ttk.Button(buttons_frame, text="🔄 تحديث الحالة", command=self.refresh_tool_status)
        self.refresh_tool_btn.pack(side=tk.LEFT, padx=5)

        # قائمة الملفات
        files_frame = ttk.LabelFrame(tab, text="ملفات الأداة", padding=10)
        files_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # جدول الملفات
        columns = ('ID', 'اسم الملف', 'الحجم', 'آخر تحديث')
        self.files_tree = ttk.Treeview(files_frame, columns=columns, show='headings', height=8)

        for col in columns:
            self.files_tree.heading(col, text=col)
            self.files_tree.column(col, width=150)

        scrollbar = ttk.Scrollbar(files_frame, orient=tk.VERTICAL, command=self.files_tree.yview)
        self.files_tree.configure(yscrollcommand=scrollbar.set)

        self.files_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_gfx_management_tab(self):
        """تبويب إدارة GFX"""
        tab = ttk.Frame(self.notebook)
        self.notebook.add(tab, text="🎮 إدارة GFX")

        # حالة برنامج GFX
        gfx_status_frame = ttk.LabelFrame(tab, text="حالة برنامج GFX", padding=10)
        gfx_status_frame.pack(fill=tk.X, padx=10, pady=5)

        self.gfx_status_label = ttk.Label(gfx_status_frame, text="الحالة: غير محدد")
        self.gfx_status_label.grid(row=0, column=0, sticky=tk.W, pady=2)

        self.gfx_process_label = ttk.Label(gfx_status_frame, text="معرف العملية: غير محدد")
        self.gfx_process_label.grid(row=0, column=1, sticky=tk.W, pady=2)

        # أزرار التحكم في GFX
        gfx_control_frame = ttk.LabelFrame(tab, text="التحكم في برنامج GFX", padding=10)
        gfx_control_frame.pack(fill=tk.X, padx=10, pady=5)

        gfx_buttons_frame = ttk.Frame(gfx_control_frame)
        gfx_buttons_frame.pack()

        self.start_gfx_btn = ttk.Button(gfx_buttons_frame, text="▶️ تشغيل GFX", command=self.start_gfx)
        self.start_gfx_btn.pack(side=tk.LEFT, padx=5)

        self.stop_gfx_btn = ttk.Button(gfx_buttons_frame, text="⏹️ إيقاف GFX", command=self.stop_gfx)
        self.stop_gfx_btn.pack(side=tk.LEFT, padx=5)

        self.restart_gfx_btn = ttk.Button(gfx_buttons_frame, text="🔄 إعادة تشغيل GFX", command=self.restart_gfx)
        self.restart_gfx_btn.pack(side=tk.LEFT, padx=5)

        self.refresh_gfx_btn = ttk.Button(gfx_buttons_frame, text="🔄 تحديث حالة GFX", command=self.refresh_gfx_status)
        self.refresh_gfx_btn.pack(side=tk.LEFT, padx=5)

        # إدارة الأجهزة
        devices_frame = ttk.LabelFrame(tab, text="الأجهزة المتصلة", padding=10)
        devices_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        self.devices_listbox = tk.Listbox(devices_frame, height=6)
        self.devices_listbox.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)

        devices_scrollbar = ttk.Scrollbar(devices_frame, orient=tk.VERTICAL, command=self.devices_listbox.yview)
        self.devices_listbox.configure(yscrollcommand=devices_scrollbar.set)
        devices_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        devices_btn_frame = ttk.Frame(devices_frame)
        devices_btn_frame.pack(fill=tk.X, pady=5)

        self.refresh_devices_btn = ttk.Button(devices_btn_frame, text="🔄 تحديث الأجهزة", command=self.refresh_devices)
        self.refresh_devices_btn.pack(side=tk.LEFT, padx=5)

        self.connect_device_btn = ttk.Button(devices_btn_frame, text="🔗 اتصال بالجهاز", command=self.connect_device)
        self.connect_device_btn.pack(side=tk.LEFT, padx=5)

    def create_updates_tab(self):
        """تبويب التحديثات"""
        tab = ttk.Frame(self.notebook)
        self.notebook.add(tab, text="📦 التحديثات")

        # رفع الملفات
        upload_frame = ttk.LabelFrame(tab, text="رفع الملفات", padding=10)
        upload_frame.pack(fill=tk.X, padx=10, pady=5)

        # اختيار الملف
        file_frame = ttk.Frame(upload_frame)
        file_frame.pack(fill=tk.X, pady=5)

        ttk.Label(file_frame, text="الملف المحدد:").pack(side=tk.LEFT)
        self.selected_file_var = tk.StringVar(value="لم يتم اختيار ملف")
        self.selected_file_label = ttk.Label(file_frame, textvariable=self.selected_file_var)
        self.selected_file_label.pack(side=tk.LEFT, padx=10)

        self.browse_file_btn = ttk.Button(file_frame, text="📁 اختيار ملف", command=self.browse_file)
        self.browse_file_btn.pack(side=tk.RIGHT)

        # معرف الملف
        id_frame = ttk.Frame(upload_frame)
        id_frame.pack(fill=tk.X, pady=5)

        ttk.Label(id_frame, text="معرف الملف:").pack(side=tk.LEFT)
        self.file_id_var = tk.StringVar(value="1")
        self.file_id_entry = ttk.Entry(id_frame, textvariable=self.file_id_var, width=10)
        self.file_id_entry.pack(side=tk.LEFT, padx=10)

        self.upload_file_btn = ttk.Button(id_frame, text="📤 رفع الملف", command=self.upload_file)
        self.upload_file_btn.pack(side=tk.RIGHT)

        # تحديث النظام
        system_update_frame = ttk.LabelFrame(tab, text="تحديث النظام", padding=10)
        system_update_frame.pack(fill=tk.X, padx=10, pady=5)

        update_buttons_frame = ttk.Frame(system_update_frame)
        update_buttons_frame.pack()

        self.backup_btn = ttk.Button(update_buttons_frame, text="💾 إنشاء نسخة احتياطية", command=self.create_backup)
        self.backup_btn.pack(side=tk.LEFT, padx=5)

        self.restore_btn = ttk.Button(update_buttons_frame, text="🔄 استعادة النسخة الاحتياطية", command=self.restore_backup)
        self.restore_btn.pack(side=tk.LEFT, padx=5)

        # سجل التحديثات
        update_log_frame = ttk.LabelFrame(tab, text="سجل التحديثات", padding=10)
        update_log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        self.update_log_text = scrolledtext.ScrolledText(update_log_frame, height=10, state=tk.DISABLED)
        self.update_log_text.pack(fill=tk.BOTH, expand=True)

    def create_monitoring_tab(self):
        """تبويب المراقبة"""
        tab = ttk.Frame(self.notebook)
        self.notebook.add(tab, text="📊 المراقبة")

        # إعدادات المراقبة
        monitor_settings_frame = ttk.LabelFrame(tab, text="إعدادات المراقبة", padding=10)
        monitor_settings_frame.pack(fill=tk.X, padx=10, pady=5)

        settings_frame = ttk.Frame(monitor_settings_frame)
        settings_frame.pack()

        ttk.Label(settings_frame, text="فترة التحديث (ثانية):").pack(side=tk.LEFT)
        self.monitor_interval_var = tk.StringVar(value="5")
        self.monitor_interval_entry = ttk.Entry(settings_frame, textvariable=self.monitor_interval_var, width=10)
        self.monitor_interval_entry.pack(side=tk.LEFT, padx=10)

        self.start_monitor_btn = ttk.Button(settings_frame, text="▶️ بدء المراقبة", command=self.start_monitoring)
        self.start_monitor_btn.pack(side=tk.LEFT, padx=5)

        self.stop_monitor_btn = ttk.Button(settings_frame, text="⏹️ إيقاف المراقبة", command=self.stop_monitoring, state=tk.DISABLED)
        self.stop_monitor_btn.pack(side=tk.LEFT, padx=5)

        # معلومات المراقبة
        monitor_info_frame = ttk.LabelFrame(tab, text="معلومات المراقبة", padding=10)
        monitor_info_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        self.monitor_text = scrolledtext.ScrolledText(monitor_info_frame, height=15, state=tk.DISABLED)
        self.monitor_text.pack(fill=tk.BOTH, expand=True)

    def create_logs_tab(self):
        """تبويب السجلات"""
        tab = ttk.Frame(self.notebook)
        self.notebook.add(tab, text="📝 السجلات")

        # أزرار السجلات
        logs_buttons_frame = ttk.Frame(tab)
        logs_buttons_frame.pack(fill=tk.X, padx=10, pady=5)

        self.clear_logs_btn = ttk.Button(logs_buttons_frame, text="🗑️ مسح السجلات", command=self.clear_logs)
        self.clear_logs_btn.pack(side=tk.LEFT, padx=5)

        self.save_logs_btn = ttk.Button(logs_buttons_frame, text="💾 حفظ السجلات", command=self.save_logs)
        self.save_logs_btn.pack(side=tk.LEFT, padx=5)

        self.refresh_logs_btn = ttk.Button(logs_buttons_frame, text="🔄 تحديث السجلات", command=self.refresh_logs)
        self.refresh_logs_btn.pack(side=tk.LEFT, padx=5)

        # منطقة السجلات
        self.logs_text = scrolledtext.ScrolledText(tab, height=20, state=tk.DISABLED)
        self.logs_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)

        self.status_label = ttk.Label(self.status_bar, text="جاهز")
        self.status_label.pack(side=tk.LEFT, padx=5)

        self.time_label = ttk.Label(self.status_bar, text="")
        self.time_label.pack(side=tk.RIGHT, padx=5)

        self.update_time()

    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)

    # ===== وظائف الاتصال =====

    def connect_to_server(self):
        """الاتصال بالسيرفر"""
        try:
            self.server_url = self.server_url_var.get()
            email = self.email_var.get()
            password = self.password_var.get()

            if not all([self.server_url, email, password]):
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
                return

            self.log_message("محاولة الاتصال بالسيرفر...")
            self.status_label.config(text="جاري الاتصال...")

            # تسجيل الدخول
            login_data = {
                "email": email,
                "password": password
            }

            response = requests.post(
                f"{self.server_url}/api/auth/login",
                json=login_data,
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.auth_token = data.get('token')
                    self.is_connected = True

                    self.connection_status.config(text="✅ متصل", foreground="green")
                    self.connect_btn.config(state=tk.DISABLED)
                    self.disconnect_btn.config(state=tk.NORMAL)

                    self.log_message("تم الاتصال بالسيرفر بنجاح")
                    self.status_label.config(text="متصل")

                    # تحديث معلومات السيرفر
                    self.update_server_info()

                    messagebox.showinfo("نجح", "تم الاتصال بالسيرفر بنجاح")
                else:
                    raise Exception(data.get('message', 'فشل تسجيل الدخول'))
            else:
                raise Exception(f"خطأ HTTP: {response.status_code}")

        except Exception as e:
            self.log_message(f"فشل الاتصال: {str(e)}")
            self.status_label.config(text="فشل الاتصال")
            messagebox.showerror("خطأ في الاتصال", f"فشل الاتصال بالسيرفر:\n{str(e)}")

    def disconnect_from_server(self):
        """قطع الاتصال من السيرفر"""
        self.auth_token = None
        self.is_connected = False

        self.connection_status.config(text="❌ غير متصل", foreground="red")
        self.connect_btn.config(state=tk.NORMAL)
        self.disconnect_btn.config(state=tk.DISABLED)

        self.log_message("تم قطع الاتصال من السيرفر")
        self.status_label.config(text="غير متصل")

        # إيقاف المراقبة إذا كانت تعمل
        if self.monitoring:
            self.stop_monitoring()

        messagebox.showinfo("تم", "تم قطع الاتصال من السيرفر")

    def test_connection(self):
        """اختبار الاتصال"""
        try:
            self.log_message("اختبار الاتصال...")
            self.status_label.config(text="جاري اختبار الاتصال...")

            response = requests.get(f"{self.server_url_var.get()}/", timeout=5)

            if response.status_code == 200:
                self.log_message("اختبار الاتصال نجح")
                self.status_label.config(text="اختبار الاتصال نجح")
                messagebox.showinfo("نجح", "السيرفر متاح ويعمل بشكل طبيعي")
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.log_message(f"فشل اختبار الاتصال: {str(e)}")
            self.status_label.config(text="فشل اختبار الاتصال")
            messagebox.showerror("فشل", f"فشل اختبار الاتصال:\n{str(e)}")

    def get_headers(self):
        """الحصول على headers للطلبات"""
        if not self.auth_token:
            raise Exception("غير متصل بالسيرفر")

        return {
            'Authorization': f'Bearer {self.auth_token}',
            'Content-Type': 'application/json'
        }

    # ===== وظائف إدارة الأداة =====

    def start_tool(self):
        """تشغيل الأداة"""
        try:
            if not self.is_connected:
                messagebox.showerror("خطأ", "يجب الاتصال بالسيرفر أولاً")
                return

            self.log_message("تشغيل الأداة...")
            self.status_label.config(text="جاري تشغيل الأداة...")

            response = requests.post(
                f"{self.server_url}/api/tool/start",
                headers=self.get_headers(),
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_message("تم تشغيل الأداة بنجاح")
                    self.status_label.config(text="تم تشغيل الأداة")
                    self.refresh_tool_status()
                    messagebox.showinfo("نجح", "تم تشغيل الأداة بنجاح")
                else:
                    raise Exception(data.get('message', 'فشل تشغيل الأداة'))
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.log_message(f"فشل تشغيل الأداة: {str(e)}")
            self.status_label.config(text="فشل تشغيل الأداة")
            messagebox.showerror("خطأ", f"فشل تشغيل الأداة:\n{str(e)}")

    def stop_tool(self):
        """إيقاف الأداة"""
        try:
            if not self.is_connected:
                messagebox.showerror("خطأ", "يجب الاتصال بالسيرفر أولاً")
                return

            self.log_message("إيقاف الأداة...")
            self.status_label.config(text="جاري إيقاف الأداة...")

            response = requests.post(
                f"{self.server_url}/api/tool/stop",
                headers=self.get_headers(),
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_message("تم إيقاف الأداة بنجاح")
                    self.status_label.config(text="تم إيقاف الأداة")
                    self.refresh_tool_status()
                    messagebox.showinfo("نجح", "تم إيقاف الأداة بنجاح")
                else:
                    raise Exception(data.get('message', 'فشل إيقاف الأداة'))
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.log_message(f"فشل إيقاف الأداة: {str(e)}")
            self.status_label.config(text="فشل إيقاف الأداة")
            messagebox.showerror("خطأ", f"فشل إيقاف الأداة:\n{str(e)}")

    def refresh_tool_status(self):
        """تحديث حالة الأداة"""
        try:
            if not self.is_connected:
                return

            response = requests.get(
                f"{self.server_url}/api/tool/status",
                headers=self.get_headers(),
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    tool = data.get('tool', {})

                    self.tool_name_label.config(text=f"اسم الأداة: {tool.get('name', 'غير محدد')}")
                    self.tool_version_label.config(text=f"الإصدار: {tool.get('version', 'غير محدد')}")
                    self.tool_status_label.config(text=f"الحالة: {tool.get('status', 'غير محدد')}")
                    self.tool_files_label.config(text=f"عدد الملفات: {tool.get('filesCount', 'غير محدد')}")

                    # تحديث قائمة الملفات
                    self.update_files_list(tool.get('files', []))

        except Exception as e:
            self.log_message(f"فشل تحديث حالة الأداة: {str(e)}")

    def update_files_list(self, files):
        """تحديث قائمة الملفات"""
        # مسح القائمة الحالية
        for item in self.files_tree.get_children():
            self.files_tree.delete(item)

        # إضافة الملفات الجديدة
        for file in files:
            self.files_tree.insert('', 'end', values=(
                file.get('id', ''),
                file.get('name', ''),
                f"{file.get('size', 0)} بايت",
                file.get('lastUpdated', '')
            ))

    # ===== وظائف إدارة GFX =====

    def start_gfx(self):
        """تشغيل برنامج GFX"""
        try:
            if not self.is_connected:
                messagebox.showerror("خطأ", "يجب الاتصال بالسيرفر أولاً")
                return

            self.log_message("تشغيل برنامج GFX...")
            self.status_label.config(text="جاري تشغيل GFX...")

            response = requests.post(
                f"{self.server_url}/api/tool/gfx/start",
                headers=self.get_headers(),
                timeout=15
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_message(f"تم تشغيل GFX بنجاح - معرف العملية: {data.get('processId')}")
                    self.status_label.config(text="تم تشغيل GFX")
                    self.refresh_gfx_status()
                    messagebox.showinfo("نجح", f"تم تشغيل برنامج GFX بنجاح\nمعرف العملية: {data.get('processId')}")
                else:
                    raise Exception(data.get('message', 'فشل تشغيل GFX'))
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.log_message(f"فشل تشغيل GFX: {str(e)}")
            self.status_label.config(text="فشل تشغيل GFX")
            messagebox.showerror("خطأ", f"فشل تشغيل برنامج GFX:\n{str(e)}")

    def stop_gfx(self):
        """إيقاف برنامج GFX"""
        try:
            if not self.is_connected:
                messagebox.showerror("خطأ", "يجب الاتصال بالسيرفر أولاً")
                return

            self.log_message("إيقاف برنامج GFX...")
            self.status_label.config(text="جاري إيقاف GFX...")

            response = requests.post(
                f"{self.server_url}/api/tool/gfx/stop",
                headers=self.get_headers(),
                timeout=15
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_message("تم إيقاف GFX بنجاح")
                    self.status_label.config(text="تم إيقاف GFX")
                    self.refresh_gfx_status()
                    messagebox.showinfo("نجح", "تم إيقاف برنامج GFX بنجاح")
                else:
                    raise Exception(data.get('message', 'فشل إيقاف GFX'))
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.log_message(f"فشل إيقاف GFX: {str(e)}")
            self.status_label.config(text="فشل إيقاف GFX")
            messagebox.showerror("خطأ", f"فشل إيقاف برنامج GFX:\n{str(e)}")

    def restart_gfx(self):
        """إعادة تشغيل برنامج GFX"""
        try:
            if not self.is_connected:
                messagebox.showerror("خطأ", "يجب الاتصال بالسيرفر أولاً")
                return

            self.log_message("إعادة تشغيل برنامج GFX...")
            self.status_label.config(text="جاري إعادة تشغيل GFX...")

            response = requests.post(
                f"{self.server_url}/api/tool/gfx/restart",
                headers=self.get_headers(),
                timeout=20
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_message(f"تم إعادة تشغيل GFX بنجاح - معرف العملية: {data.get('processId')}")
                    self.status_label.config(text="تم إعادة تشغيل GFX")
                    self.refresh_gfx_status()
                    messagebox.showinfo("نجح", f"تم إعادة تشغيل برنامج GFX بنجاح\nمعرف العملية الجديد: {data.get('processId')}")
                else:
                    raise Exception(data.get('message', 'فشل إعادة تشغيل GFX'))
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.log_message(f"فشل إعادة تشغيل GFX: {str(e)}")
            self.status_label.config(text="فشل إعادة تشغيل GFX")
            messagebox.showerror("خطأ", f"فشل إعادة تشغيل برنامج GFX:\n{str(e)}")

    def refresh_gfx_status(self):
        """تحديث حالة برنامج GFX"""
        try:
            if not self.is_connected:
                return

            response = requests.get(
                f"{self.server_url}/api/tool/gfx/status",
                headers=self.get_headers(),
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    status = data.get('status', 'غير محدد')
                    process_id = data.get('processId', 'غير محدد')
                    is_running = data.get('isRunning', False)

                    self.gfx_status_label.config(text=f"الحالة: {status}")
                    self.gfx_process_label.config(text=f"معرف العملية: {process_id}")

                    # تغيير لون الحالة
                    if is_running:
                        self.gfx_status_label.config(foreground="green")
                    else:
                        self.gfx_status_label.config(foreground="red")

        except Exception as e:
            self.log_message(f"فشل تحديث حالة GFX: {str(e)}")

    def refresh_devices(self):
        """تحديث قائمة الأجهزة"""
        try:
            if not self.is_connected:
                messagebox.showerror("خطأ", "يجب الاتصال بالسيرفر أولاً")
                return

            self.log_message("تحديث قائمة الأجهزة...")

            response = requests.get(
                f"{self.server_url}/api/tool/adb/devices",
                headers=self.get_headers(),
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    devices = data.get('devices', [])

                    # مسح القائمة الحالية
                    self.devices_listbox.delete(0, tk.END)

                    # إضافة الأجهزة الجديدة
                    for device in devices:
                        device_info = f"{device.get('id')} - {device.get('status')} ({device.get('type', 'unknown')})"
                        self.devices_listbox.insert(tk.END, device_info)

                    self.log_message(f"تم العثور على {len(devices)} جهاز")
                    messagebox.showinfo("تم", f"تم العثور على {len(devices)} جهاز متصل")
                else:
                    raise Exception(data.get('message', 'فشل الحصول على قائمة الأجهزة'))
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.log_message(f"فشل تحديث قائمة الأجهزة: {str(e)}")
            messagebox.showerror("خطأ", f"فشل تحديث قائمة الأجهزة:\n{str(e)}")

    def connect_device(self):
        """الاتصال بجهاز محدد"""
        try:
            if not self.is_connected:
                messagebox.showerror("خطأ", "يجب الاتصال بالسيرفر أولاً")
                return

            selection = self.devices_listbox.curselection()
            if not selection:
                messagebox.showwarning("تحذير", "يرجى اختيار جهاز من القائمة")
                return

            device_info = self.devices_listbox.get(selection[0])
            device_id = device_info.split(' - ')[0]

            self.log_message(f"الاتصال بالجهاز: {device_id}")

            response = requests.post(
                f"{self.server_url}/api/tool/adb/connect",
                headers=self.get_headers(),
                json={"deviceId": device_id},
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_message(f"تم الاتصال بالجهاز {device_id} بنجاح")
                    messagebox.showinfo("نجح", f"تم الاتصال بالجهاز {device_id} بنجاح")
                else:
                    raise Exception(data.get('message', 'فشل الاتصال بالجهاز'))
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.log_message(f"فشل الاتصال بالجهاز: {str(e)}")
            messagebox.showerror("خطأ", f"فشل الاتصال بالجهاز:\n{str(e)}")

    # ===== وظائف التحديثات =====

    def browse_file(self):
        """اختيار ملف للرفع"""
        file_path = filedialog.askopenfilename(
            title="اختيار ملف للرفع",
            filetypes=[
                ("Python files", "*.py"),
                ("JavaScript files", "*.js"),
                ("JSON files", "*.json"),
                ("Text files", "*.txt"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            self.selected_file_var.set(file_path)
            self.log_message(f"تم اختيار الملف: {file_path}")

    def upload_file(self):
        """رفع ملف إلى السيرفر"""
        try:
            if not self.is_connected:
                messagebox.showerror("خطأ", "يجب الاتصال بالسيرفر أولاً")
                return

            file_path = self.selected_file_var.get()
            if file_path == "لم يتم اختيار ملف" or not os.path.exists(file_path):
                messagebox.showerror("خطأ", "يرجى اختيار ملف صالح")
                return

            file_id = self.file_id_var.get()
            if not file_id:
                messagebox.showerror("خطأ", "يرجى إدخال معرف الملف")
                return

            self.log_message(f"رفع الملف: {file_path}")
            self.status_label.config(text="جاري رفع الملف...")

            # قراءة محتوى الملف
            with open(file_path, 'r', encoding='utf-8') as f:
                file_content = f.read()

            # إرسال الملف
            response = requests.put(
                f"{self.server_url}/api/tool/gfx/files",
                headers=self.get_headers(),
                json={
                    "fileId": int(file_id),
                    "fileContent": file_content
                },
                timeout=30
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    file_info = data.get('file', {})
                    backup_path = data.get('backupPath', '')

                    self.log_message(f"تم رفع الملف بنجاح: {file_info.get('name')}")
                    self.log_message(f"النسخة الاحتياطية: {backup_path}")
                    self.status_label.config(text="تم رفع الملف")

                    # تحديث سجل التحديثات
                    self.update_log_text.config(state=tk.NORMAL)
                    self.update_log_text.insert(tk.END, f"[{datetime.now().strftime('%H:%M:%S')}] تم رفع الملف: {file_info.get('name')}\n")
                    self.update_log_text.insert(tk.END, f"الحجم الجديد: {file_info.get('size')} بايت\n")
                    self.update_log_text.insert(tk.END, f"النسخة الاحتياطية: {backup_path}\n\n")
                    self.update_log_text.config(state=tk.DISABLED)
                    self.update_log_text.see(tk.END)

                    messagebox.showinfo("نجح", f"تم رفع الملف بنجاح\nالحجم الجديد: {file_info.get('size')} بايت")
                else:
                    raise Exception(data.get('message', 'فشل رفع الملف'))
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            self.log_message(f"فشل رفع الملف: {str(e)}")
            self.status_label.config(text="فشل رفع الملف")
            messagebox.showerror("خطأ", f"فشل رفع الملف:\n{str(e)}")

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            if not self.is_connected:
                messagebox.showerror("خطأ", "يجب الاتصال بالسيرفر أولاً")
                return

            self.log_message("إنشاء نسخة احتياطية...")
            self.status_label.config(text="جاري إنشاء نسخة احتياطية...")

            # هنا يمكن إضافة API endpoint للنسخ الاحتياطي
            # في الوقت الحالي سنعرض رسالة
            messagebox.showinfo("معلومات", "ميزة النسخ الاحتياطي قيد التطوير")

        except Exception as e:
            self.log_message(f"فشل إنشاء النسخة الاحتياطية: {str(e)}")
            messagebox.showerror("خطأ", f"فشل إنشاء النسخة الاحتياطية:\n{str(e)}")

    def restore_backup(self):
        """استعادة النسخة الاحتياطية"""
        try:
            if not self.is_connected:
                messagebox.showerror("خطأ", "يجب الاتصال بالسيرفر أولاً")
                return

            self.log_message("استعادة النسخة الاحتياطية...")
            self.status_label.config(text="جاري استعادة النسخة الاحتياطية...")

            # هنا يمكن إضافة API endpoint لاستعادة النسخ الاحتياطية
            # في الوقت الحالي سنعرض رسالة
            messagebox.showinfo("معلومات", "ميزة استعادة النسخ الاحتياطية قيد التطوير")

        except Exception as e:
            self.log_message(f"فشل استعادة النسخة الاحتياطية: {str(e)}")
            messagebox.showerror("خطأ", f"فشل استعادة النسخة الاحتياطية:\n{str(e)}")

    # ===== وظائف المراقبة =====

    def start_monitoring(self):
        """بدء المراقبة"""
        try:
            if not self.is_connected:
                messagebox.showerror("خطأ", "يجب الاتصال بالسيرفر أولاً")
                return

            if self.monitoring:
                messagebox.showwarning("تحذير", "المراقبة تعمل بالفعل")
                return

            interval = int(self.monitor_interval_var.get())
            if interval < 1:
                messagebox.showerror("خطأ", "فترة التحديث يجب أن تكون أكبر من 0")
                return

            self.monitoring = True
            self.start_monitor_btn.config(state=tk.DISABLED)
            self.stop_monitor_btn.config(state=tk.NORMAL)

            # بدء خيط المراقبة
            self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
            self.monitor_thread.start()

            self.log_message(f"بدء المراقبة كل {interval} ثانية")
            messagebox.showinfo("تم", f"تم بدء المراقبة كل {interval} ثانية")

        except ValueError:
            messagebox.showerror("خطأ", "فترة التحديث يجب أن تكون رقم صحيح")
        except Exception as e:
            self.log_message(f"فشل بدء المراقبة: {str(e)}")
            messagebox.showerror("خطأ", f"فشل بدء المراقبة:\n{str(e)}")

    def stop_monitoring(self):
        """إيقاف المراقبة"""
        self.monitoring = False
        self.start_monitor_btn.config(state=tk.NORMAL)
        self.stop_monitor_btn.config(state=tk.DISABLED)

        self.log_message("تم إيقاف المراقبة")
        messagebox.showinfo("تم", "تم إيقاف المراقبة")

    def monitor_loop(self):
        """حلقة المراقبة"""
        while self.monitoring:
            try:
                interval = int(self.monitor_interval_var.get())

                # جمع معلومات المراقبة
                monitor_data = self.collect_monitoring_data()

                # تحديث واجهة المراقبة
                self.root.after(0, self.update_monitoring_display, monitor_data)

                # انتظار الفترة المحددة
                time.sleep(interval)

            except Exception as e:
                self.log_message(f"خطأ في المراقبة: {str(e)}")
                time.sleep(5)  # انتظار 5 ثواني في حالة الخطأ

    def collect_monitoring_data(self):
        """جمع بيانات المراقبة"""
        data = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'tool_status': 'غير محدد',
            'gfx_status': 'غير محدد',
            'devices_count': 0,
            'server_responsive': False
        }

        try:
            # حالة الأداة
            response = requests.get(
                f"{self.server_url}/api/tool/status",
                headers=self.get_headers(),
                timeout=5
            )
            if response.status_code == 200:
                tool_data = response.json()
                if tool_data.get('success'):
                    data['tool_status'] = tool_data.get('tool', {}).get('status', 'غير محدد')
                    data['server_responsive'] = True

        except:
            pass

        try:
            # حالة GFX
            response = requests.get(
                f"{self.server_url}/api/tool/gfx/status",
                headers=self.get_headers(),
                timeout=5
            )
            if response.status_code == 200:
                gfx_data = response.json()
                if gfx_data.get('success'):
                    data['gfx_status'] = gfx_data.get('status', 'غير محدد')

        except:
            pass

        try:
            # عدد الأجهزة
            response = requests.get(
                f"{self.server_url}/api/tool/adb/devices",
                headers=self.get_headers(),
                timeout=5
            )
            if response.status_code == 200:
                devices_data = response.json()
                if devices_data.get('success'):
                    data['devices_count'] = len(devices_data.get('devices', []))

        except:
            pass

        return data

    def update_monitoring_display(self, data):
        """تحديث عرض المراقبة"""
        try:
            self.monitor_text.config(state=tk.NORMAL)

            # إضافة البيانات الجديدة
            monitor_info = f"""
[{data['timestamp']}]
حالة السيرفر: {'متجاوب' if data['server_responsive'] else 'غير متجاوب'}
حالة الأداة: {data['tool_status']}
حالة GFX: {data['gfx_status']}
عدد الأجهزة: {data['devices_count']}
{'='*50}
"""

            self.monitor_text.insert(tk.END, monitor_info)

            # الحفاظ على آخر 100 سطر فقط
            lines = self.monitor_text.get('1.0', tk.END).split('\n')
            if len(lines) > 100:
                self.monitor_text.delete('1.0', f'{len(lines)-100}.0')

            self.monitor_text.config(state=tk.DISABLED)
            self.monitor_text.see(tk.END)

        except Exception as e:
            self.log_message(f"خطأ في تحديث المراقبة: {str(e)}")

    # ===== وظائف السجلات =====

    def log_message(self, message):
        """إضافة رسالة إلى السجل"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            log_entry = f"[{timestamp}] {message}\n"

            self.logs_text.config(state=tk.NORMAL)
            self.logs_text.insert(tk.END, log_entry)

            # الحفاظ على آخر 1000 سطر فقط
            lines = self.logs_text.get('1.0', tk.END).split('\n')
            if len(lines) > 1000:
                self.logs_text.delete('1.0', f'{len(lines)-1000}.0')

            self.logs_text.config(state=tk.DISABLED)
            self.logs_text.see(tk.END)

        except Exception as e:
            print(f"خطأ في تسجيل الرسالة: {str(e)}")

    def clear_logs(self):
        """مسح السجلات"""
        self.logs_text.config(state=tk.NORMAL)
        self.logs_text.delete('1.0', tk.END)
        self.logs_text.config(state=tk.DISABLED)
        self.log_message("تم مسح السجلات")

    def save_logs(self):
        """حفظ السجلات في ملف"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="حفظ السجلات",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )

            if file_path:
                logs_content = self.logs_text.get('1.0', tk.END)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(logs_content)

                self.log_message(f"تم حفظ السجلات في: {file_path}")
                messagebox.showinfo("تم", f"تم حفظ السجلات في:\n{file_path}")

        except Exception as e:
            self.log_message(f"فشل حفظ السجلات: {str(e)}")
            messagebox.showerror("خطأ", f"فشل حفظ السجلات:\n{str(e)}")

    def refresh_logs(self):
        """تحديث السجلات"""
        self.log_message("تم تحديث السجلات")

    # ===== وظائف إضافية =====

    def update_server_info(self):
        """تحديث معلومات السيرفر"""
        try:
            # الحصول على معلومات الأداة
            response = requests.get(
                f"{self.server_url}/api/tool/status",
                headers=self.get_headers(),
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    tool = data.get('tool', {})

                    server_info = f"""
معلومات السيرفر:
العنوان: {self.server_url}
حالة الاتصال: متصل ✅

معلومات الأداة:
الاسم: {tool.get('name', 'غير محدد')}
الإصدار: {tool.get('version', 'غير محدد')}
الحالة: {tool.get('status', 'غير محدد')}
عدد الملفات: {tool.get('filesCount', 'غير محدد')}

الإعدادات:
GFX مفعل: {'نعم' if tool.get('config', {}).get('gfxEnabled') else 'لا'}
مسار ADB: {tool.get('config', {}).get('adbPath', 'غير محدد')}
الذاكرة القصوى: {tool.get('config', {}).get('maxMemory', 'غير محدد')} MB

تاريخ الإنشاء: {tool.get('createdAt', 'غير محدد')}
آخر تحديث: {tool.get('updatedAt', 'غير محدد')}
"""

                    self.server_info_text.config(state=tk.NORMAL)
                    self.server_info_text.delete('1.0', tk.END)
                    self.server_info_text.insert('1.0', server_info)
                    self.server_info_text.config(state=tk.DISABLED)

        except Exception as e:
            self.log_message(f"فشل تحديث معلومات السيرفر: {str(e)}")

    def load_settings(self):
        """تحميل الإعدادات المحفوظة"""
        try:
            settings_file = "server_controller_settings.json"
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                self.server_url_var.set(settings.get('server_url', 'http://localhost:3000'))
                self.email_var.set(settings.get('email', '<EMAIL>'))
                # لا نحفظ كلمة المرور لأسباب أمنية

        except Exception as e:
            self.log_message(f"فشل تحميل الإعدادات: {str(e)}")

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            settings = {
                'server_url': self.server_url_var.get(),
                'email': self.email_var.get()
                # لا نحفظ كلمة المرور لأسباب أمنية
            }

            settings_file = "server_controller_settings.json"
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.log_message(f"فشل حفظ الإعدادات: {str(e)}")

    def on_closing(self):
        """عند إغلاق البرنامج"""
        try:
            # إيقاف المراقبة
            if self.monitoring:
                self.stop_monitoring()

            # حفظ الإعدادات
            self.save_settings()

            # قطع الاتصال
            if self.is_connected:
                self.disconnect_from_server()

            self.root.destroy()

        except Exception as e:
            print(f"خطأ عند الإغلاق: {str(e)}")
            self.root.destroy()

    def run(self):
        """تشغيل البرنامج"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.log_message("تم تشغيل أداة التحكم في السيرفر")
        self.root.mainloop()

# ===== تشغيل البرنامج =====

if __name__ == "__main__":
    try:
        app = ServerController()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل البرنامج: {str(e)}")
        input("اضغط Enter للخروج...")
