# 🎮 أداة التحكم في السيرفر - Server Controller

## 📋 نظرة عامة

أداة Python شاملة للتحكم في السيرفر عن بُعد مع واجهة مستخدم رسومية متقدمة. تتيح لك إدارة السيرفر وأداة GFX بالكامل من خلال واجهة سهلة الاستخدام.

## ✨ المميزات

### 🔗 **إدارة الاتصال:**
- اتصال آمن بالسيرفر عبر JWT
- اختبار الاتصال
- حفظ إعدادات الاتصال تلقائياً
- قطع الاتصال الآمن

### 🛠️ **إدارة الأداة العامة:**
- تشغيل/إيقاف الأداة
- مراقبة حالة الأداة
- عرض معلومات الملفات
- تحديث حالة الأداة

### 🎮 **إدارة برنامج GFX:**
- تشغيل/إيقاف برنامج GFX
- إعادة تشغيل البرنامج
- مراقبة معرف العملية
- إدارة الأجهزة المتصلة

### 📦 **إدارة التحديثات:**
- رفع ملفات جديدة
- تحديث ملفات البرنامج
- إنشاء نسخ احتياطية تلقائية
- سجل التحديثات

### 📊 **المراقبة المتقدمة:**
- مراقبة حالة السيرفر في الوقت الفعلي
- مراقبة حالة GFX
- عدد الأجهزة المتصلة
- فترات مراقبة قابلة للتخصيص

### 📝 **إدارة السجلات:**
- سجلات مفصلة لجميع العمليات
- حفظ السجلات في ملفات
- مسح السجلات
- عرض الوقت الحقيقي

## 🚀 التثبيت والتشغيل

### 1. **المتطلبات:**
```bash
# تثبيت Python 3.7 أو أحدث
# تثبيت المكتبات المطلوبة
pip install -r server_controller_requirements.txt
```

### 2. **تشغيل الأداة:**
```bash
python server_controller.py
```

### 3. **الاتصال بالسيرفر:**
1. أدخل عنوان السيرفر (افتراضي: `http://localhost:3000`)
2. أدخل بيانات تسجيل الدخول
3. اضغط "اتصال"

## 🖥️ واجهة المستخدم

### 📑 **التبويبات:**

#### 🔗 **تبويب الاتصال:**
- إعدادات الاتصال
- بيانات تسجيل الدخول
- معلومات السيرفر
- حالة الاتصال

#### 🛠️ **تبويب إدارة الأداة:**
- حالة الأداة
- أزرار التحكم
- قائمة الملفات
- معلومات الأداة

#### 🎮 **تبويب إدارة GFX:**
- حالة برنامج GFX
- أزرار التحكم في GFX
- قائمة الأجهزة المتصلة
- إدارة الاتصال بالأجهزة

#### 📦 **تبويب التحديثات:**
- رفع الملفات
- تحديث النظام
- النسخ الاحتياطية
- سجل التحديثات

#### 📊 **تبويب المراقبة:**
- إعدادات المراقبة
- معلومات المراقبة المباشرة
- إحصائيات النظام

#### 📝 **تبويب السجلات:**
- عرض السجلات
- حفظ السجلات
- مسح السجلات

## 🔧 الاستخدام المفصل

### 1. **الاتصال بالسيرفر:**
```
1. تأكد من تشغيل السيرفر على http://localhost:3000
2. أدخل البريد الإلكتروني: <EMAIL>
3. أدخل كلمة المرور: admin123
4. اضغط "اتصال"
```

### 2. **إدارة الأداة:**
```
- تشغيل الأداة: اضغط "▶️ تشغيل الأداة"
- إيقاف الأداة: اضغط "⏹️ إيقاف الأداة"
- تحديث الحالة: اضغط "🔄 تحديث الحالة"
```

### 3. **إدارة GFX:**
```
- تشغيل GFX: اضغط "▶️ تشغيل GFX"
- إيقاف GFX: اضغط "⏹️ إيقاف GFX"
- إعادة تشغيل: اضغط "🔄 إعادة تشغيل GFX"
- تحديث الأجهزة: اضغط "🔄 تحديث الأجهزة"
```

### 4. **رفع الملفات:**
```
1. اضغط "📁 اختيار ملف"
2. اختر الملف المطلوب
3. أدخل معرف الملف (1-6)
4. اضغط "📤 رفع الملف"
```

### 5. **بدء المراقبة:**
```
1. أدخل فترة التحديث (بالثواني)
2. اضغط "▶️ بدء المراقبة"
3. راقب المعلومات في الوقت الفعلي
4. اضغط "⏹️ إيقاف المراقبة" عند الانتهاء
```

## 📁 معرفات الملفات

| معرف | اسم الملف | الوصف |
|------|-----------|--------|
| 1 | GFX.py | الكود الأساسي لـ GFX |
| 2 | GUI.py | واجهة المستخدم |
| 3 | JO_GAME_TOOL.exe | الملف التنفيذي |
| 4 | platform-tools/adb.exe | أداة ADB |
| 5 | assets/ | ملفات الأصول |
| 6 | README.md | ملف التوثيق |

## 🔒 الأمان

### **المصادقة:**
- جميع العمليات تتطلب مصادقة JWT
- كلمات المرور لا يتم حفظها محلياً
- انتهاء صلاحية الجلسة تلقائياً

### **الاتصال:**
- اتصال آمن عبر HTTPS (إذا كان متاحاً)
- التحقق من صحة الشهادات
- مهلة زمنية للطلبات

## 🐛 استكشاف الأخطاء

### **مشاكل الاتصال:**
```
- تأكد من تشغيل السيرفر
- تحقق من عنوان السيرفر
- تأكد من صحة بيانات تسجيل الدخول
- تحقق من اتصال الإنترنت
```

### **مشاكل GFX:**
```
- تأكد من وجود الملف التنفيذي
- تحقق من أدوات ADB
- تأكد من اتصال الجهاز/المحاكي
- راجع السجلات للتفاصيل
```

### **مشاكل رفع الملفات:**
```
- تأكد من صحة معرف الملف
- تحقق من صيغة الملف
- تأكد من صلاحيات الكتابة
- راجع حجم الملف
```

## 📊 الإحصائيات

### **معلومات المراقبة:**
- حالة السيرفر (متجاوب/غير متجاوب)
- حالة الأداة (running/stopped/error)
- حالة GFX (running/stopped)
- عدد الأجهزة المتصلة

### **معلومات الأداء:**
- وقت الاستجابة
- استخدام الذاكرة
- معرف العملية
- وقت التشغيل

## 🔄 التحديثات المستقبلية

### **ميزات مخططة:**
- [ ] نسخ احتياطية تلقائية
- [ ] استعادة النسخ الاحتياطية
- [ ] إشعارات سطح المكتب
- [ ] تصدير التقارير
- [ ] إعدادات متقدمة
- [ ] دعم عدة سيرفرات

### **تحسينات مخططة:**
- [ ] واجهة أكثر تفاعلية
- [ ] رسوم بيانية للأداء
- [ ] تحديث تلقائي للحالة
- [ ] دعم السمات المختلفة

## 📞 الدعم

### **في حالة وجود مشاكل:**
1. راجع السجلات في تبويب "📝 السجلات"
2. تحقق من حالة الاتصال
3. أعد تشغيل الأداة
4. أعد تشغيل السيرفر إذا لزم الأمر

### **معلومات إضافية:**
- جميع العمليات مسجلة في السجلات
- يمكن حفظ السجلات للمراجعة
- الإعدادات محفوظة تلقائياً

## 🎉 الخلاصة

أداة التحكم في السيرفر توفر واجهة شاملة ومتقدمة لإدارة السيرفر وأداة GFX. مع المراقبة المباشرة وإدارة الملفات والتحكم الكامل في جميع العمليات.

**الأداة جاهزة للاستخدام الفوري! 🚀**
