# متطلبات أداة التحكم الاحترافية في السيرفر
# Server Controller Professional Requirements v3.0

# ===== المكتبات الأساسية =====
requests>=2.28.0
psutil>=5.9.0

# ===== مكتبات الأمان والتشفير =====
cryptography>=3.4.8

# ===== مكتبات اختيارية للمميزات المتقدمة =====
# matplotlib>=3.5.0  # للرسوم البيانية (اختياري)
# pillow>=9.0.0      # لمعالجة الصور (اختياري)
# numpy>=1.21.0      # للحسابات المتقدمة (اختياري)

# ===== مكتبات مدمجة مع Python (لا تحتاج تثبيت) =====
# tkinter - واجهة المستخدم الرسومية
# sqlite3 - قاعدة البيانات المحلية
# threading - المعالجة المتوازية
# json - معالجة البيانات
# hashlib - التشفير الأساسي
# base64 - تشفير البيانات
# secrets - توليد المفاتيح الآمنة
# zipfile - ضغط الملفات
# shutil - عمليات الملفات
# os - عمليات نظام التشغيل
# sys - معلومات Python
# time - التعامل مع الوقت
# datetime - التاريخ والوقت
# pathlib - إدارة المسارات
# logging - نظام السجلات
# configparser - ملفات الإعدادات
# urllib.parse - معالجة الروابط
# webbrowser - فتح المتصفح

# ===== ملاحظات التثبيت =====
# لتثبيت المتطلبات:
# pip install -r requirements_pro.txt

# للتحقق من المتطلبات:
# pip check

# لتحديث المكتبات:
# pip install --upgrade -r requirements_pro.txt
