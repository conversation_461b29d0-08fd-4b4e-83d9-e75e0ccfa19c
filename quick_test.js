#!/usr/bin/env node

/**
 * اختبار سريع لـ GFX API
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function quickTest() {
  try {
    console.log('🧪 اختبار سريع لـ GFX API...');
    
    // اختبار الصفحة الرئيسية
    const response = await axios.get(BASE_URL);
    console.log('✅ السيرفر يعمل:', response.data.message);
    
    // اختبار حالة الأداة (بدون مصادقة)
    try {
      const toolResponse = await axios.get(`${BASE_URL}/api/tool/status`);
      console.log('✅ أداة GFX جاهزة:', toolResponse.data.tool.name);
    } catch (error) {
      console.log('⚠️  أداة GFX تتطلب مصادقة');
    }
    
    console.log('🎉 الاختبار السريع مكتمل!');
    
  } catch (error) {
    console.error('❌ فشل الاختبار:', error.message);
    console.log('💡 تأكد من تشغيل السيرفر: npm start');
  }
}

if (require.main === module) {
  quickTest();
}

module.exports = quickTest;
