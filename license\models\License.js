/**
 * نموذج بيانات الترخيص
 */
class License {
  constructor(id, key, userId, type, features, expiresAt) {
    this.id = id;
    this.key = key;
    this.userId = userId;
    this.type = type; // 'basic', 'standard', 'premium'
    this.features = features || [];
    this.isActive = true;
    this.expiresAt = expiresAt || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000); // افتراضيًا بعد سنة
    this.createdAt = new Date();
    this.activatedAt = new Date();
    this.lastCheckedAt = new Date();
  }
  
  /**
   * التحقق من صلاحية الترخيص
   */
  isValid() {
    this.lastCheckedAt = new Date();
    
    // التحقق من انتهاء الصلاحية
    if (this.expiresAt < this.lastCheckedAt) {
      this.isActive = false;
      return false;
    }
    
    return this.isActive;
  }
  
  /**
   * تفعيل الترخيص
   */
  activate() {
    this.isActive = true;
    this.activatedAt = new Date();
    return {
      success: true,
      message: 'تم تفعيل الترخيص بنجاح',
      license: this.getInfo(),
      timestamp: this.activatedAt
    };
  }
  
  /**
   * تعطيل الترخيص
   */
  deactivate() {
    this.isActive = false;
    return {
      success: true,
      message: 'تم تعطيل الترخيص بنجاح',
      license: this.getInfo(),
      timestamp: new Date()
    };
  }
  
  /**
   * تمديد فترة الترخيص
   * @param {number} days - عدد الأيام
   */
  extend(days) {
    const oldExpiresAt = new Date(this.expiresAt);
    
    // إذا كان الترخيص منتهي، نبدأ من التاريخ الحالي
    if (oldExpiresAt < new Date()) {
      this.expiresAt = new Date(Date.now() + days * 24 * 60 * 60 * 1000);
    } else {
      // وإلا نضيف الأيام إلى تاريخ الانتهاء الحالي
      this.expiresAt = new Date(oldExpiresAt.getTime() + days * 24 * 60 * 60 * 1000);
    }
    
    // إعادة تفعيل الترخيص إذا كان معطلاً
    this.isActive = true;
    
    return {
      success: true,
      message: `تم تمديد الترخيص لمدة ${days} يوم`,
      oldExpiresAt,
      newExpiresAt: this.expiresAt,
      timestamp: new Date()
    };
  }
  
  /**
   * ترقية نوع الترخيص
   * @param {string} newType - نوع الترخيص الجديد
   * @param {Array} newFeatures - الميزات الجديدة
   */
  upgrade(newType, newFeatures) {
    const oldType = this.type;
    const oldFeatures = [...this.features];
    
    this.type = newType;
    this.features = newFeatures || this.features;
    
    return {
      success: true,
      message: `تم ترقية الترخيص من ${oldType} إلى ${newType}`,
      oldType,
      newType,
      oldFeatures,
      newFeatures: this.features,
      timestamp: new Date()
    };
  }
  
  /**
   * التحقق من وجود ميزة معينة في الترخيص
   * @param {string} featureName - اسم الميزة
   */
  hasFeature(featureName) {
    return this.features.includes(featureName);
  }
  
  /**
   * الحصول على معلومات الترخيص
   */
  getInfo() {
    return {
      id: this.id,
      key: this.key.substring(0, 5) + '...' + this.key.substring(this.key.length - 5),
      userId: this.userId,
      type: this.type,
      features: this.features,
      isActive: this.isActive,
      isValid: this.isValid(),
      expiresAt: this.expiresAt,
      daysRemaining: Math.max(0, Math.floor((this.expiresAt - new Date()) / (24 * 60 * 60 * 1000))),
      createdAt: this.createdAt,
      activatedAt: this.activatedAt,
      lastCheckedAt: this.lastCheckedAt
    };
  }
}

module.exports = License;
