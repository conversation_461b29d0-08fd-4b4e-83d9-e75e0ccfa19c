# 🎮 GFX API Documentation - PUBG Mobile Graphics Optimizer

## 📋 نظرة عامة

تم دمج أداة GFX (PUBG Mobile Graphics Optimizer) بالكامل في قسم الأداة في السيرفر. الآن يمكن التحكم في إعدادات رسومات PUBG Mobile من خلال REST API بدلاً من واجهة PyQt5.

## 🔗 Base URL
```
http://localhost:3000/api/tool
```

## 🔐 المصادقة
جميع endpoints تتطلب مصادقة JWT. أضف header التالي:
```
Authorization: Bearer <your_jwt_token>
```

---

## 📱 إدارة ADB

### 1. الحصول على قائمة الأجهزة المتصلة
```http
GET /adb/devices
```

**Response:**
```json
{
  "success": true,
  "message": "تم استرجاع قائمة الأجهزة المتصلة بنجاح",
  "devices": [
    {
      "id": "emulator-5554",
      "status": "connected",
      "type": "emulator"
    }
  ],
  "count": 1,
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### 2. الاتصال بجهاز
```http
POST /adb/connect
```

**Request Body:**
```json
{
  "deviceId": "emulator-5554"  // اختياري - إذا لم يتم تحديده سيتم اختيار الأول
}
```

**Response:**
```json
{
  "success": true,
  "device": {
    "id": "emulator-5554",
    "status": "connected",
    "type": "emulator"
  },
  "message": "تم الاتصال بالجهاز emulator-5554 بنجاح"
}
```

### 3. قطع الاتصال
```http
POST /adb/disconnect
```

**Response:**
```json
{
  "success": true,
  "message": "تم قطع الاتصال بـ ADB",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### 4. حالة الاتصال
```http
GET /adb/status
```

**Response:**
```json
{
  "success": true,
  "message": "تم استرجاع حالة الاتصال بـ ADB بنجاح",
  "adbConnection": {
    "isConnected": true,
    "deviceId": "emulator-5554",
    "emulatorPath": null,
    "emulatorArch": "Unknown",
    "lastConnectionCheck": "2024-01-01T12:00:00.000Z"
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

---

## 🎨 إدارة إعدادات الرسومات

### 1. قراءة الإعدادات الحالية
```http
GET /graphics/settings
```

**Response:**
```json
{
  "success": true,
  "message": "تم قراءة إعدادات الرسومات بنجاح",
  "settings": {
    "graphics": "Balanced",
    "framerate": "Medium",
    "style": "Classic",
    "sfx": "Medium",
    "shadow": "Enable",
    "adbDevice": "emulator-5554",
    "isConnected": true
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### 2. تحديث الإعدادات
```http
PUT /graphics/settings
```

**Request Body:**
```json
{
  "graphics": "Ultra HD",     // اختياري
  "framerate": "Ultra",       // اختياري
  "style": "Realistic",       // اختياري
  "sfx": "High",             // اختياري
  "shadow": "Disable"        // اختياري
}
```

**Response:**
```json
{
  "success": true,
  "message": "تم تحديث إعدادات الرسومات بنجاح",
  "settings": {
    "graphics": "Ultra HD",
    "framerate": "Ultra",
    "style": "Realistic",
    "sfx": "High",
    "shadow": "Disable",
    "adbDevice": "emulator-5554",
    "isConnected": true
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### 3. تطبيق الإعدادات على الجهاز
```http
POST /graphics/apply
```

**Response:**
```json
{
  "success": true,
  "message": "تم تطبيق إعدادات الرسومات بنجاح",
  "appliedSettings": {
    "graphics": "Ultra HD",
    "framerate": "Ultra",
    "style": "Realistic",
    "sfx": "High",
    "shadow": "Disable"
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### 4. الحصول على الخيارات المتاحة
```http
GET /graphics/options
```

**Response:**
```json
{
  "success": true,
  "message": "تم استرجاع الخيارات المتاحة بنجاح",
  "options": {
    "graphics": ["Super Smooth", "Smooth", "Balanced", "HD", "HDR", "Ultra HD"],
    "framerate": ["Low", "Medium", "High", "Ultra"],
    "style": ["Classic", "Colorful", "Realistic", "Soft", "Movie"],
    "shadow": ["Enable", "Disable"],
    "sfx": ["Low", "Medium", "High", "Ultra"]
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

---

## 🎮 تشغيل PUBG Mobile

### تشغيل اللعبة
```http
POST /pubg/launch
```

**Response:**
```json
{
  "success": true,
  "message": "تم تشغيل PUBG Mobile بنجاح",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

---

## 🛠️ إدارة الأداة العامة

### 1. حالة الأداة
```http
GET /status
```

**Response:**
```json
{
  "success": true,
  "message": "تم استرجاع حالة الأداة بنجاح",
  "tool": {
    "id": 1,
    "name": "JO GAME TOOL - PUBG Mobile Graphics Optimizer",
    "version": "2.0.0",
    "status": "stopped",
    "config": {
      "autoStart": false,
      "logLevel": "info",
      "maxMemory": 512,
      "timeout": 30000,
      "gfxEnabled": true,
      "adbPath": "./platform-tools/adb.exe"
    },
    "filesCount": 5,
    "gfxSettings": { /* إعدادات GFX */ },
    "adbConnection": { /* حالة الاتصال */ },
    "createdAt": "2024-01-01T10:00:00.000Z",
    "updatedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

### 2. تشغيل الأداة
```http
POST /start
```

### 3. إيقاف الأداة
```http
POST /stop
```

### 4. ملفات الأداة
```http
GET /files
```

---

## ❌ رموز الأخطاء

| Code | Message | Description |
|------|---------|-------------|
| 400 | لا يوجد اتصال بالمحاكي | يجب الاتصال بـ ADB أولاً |
| 400 | إعداد الرسومات غير صالح | القيمة المرسلة غير مدعومة |
| 401 | غير مصرح به | يجب تسجيل الدخول |
| 500 | فشل الاتصال بـ ADB | خطأ في الاتصال بالجهاز |

---

## 📝 ملاحظات مهمة

1. **متطلبات النظام:**
   - ADB مثبت في `./platform-tools/`
   - محاكي أو جهاز Android متصل
   - PUBG Mobile مثبت على الجهاز

2. **تسلسل العمليات:**
   1. الحصول على قائمة الأجهزة
   2. الاتصال بجهاز
   3. قراءة الإعدادات الحالية
   4. تحديث الإعدادات حسب الحاجة
   5. تطبيق الإعدادات
   6. تشغيل PUBG Mobile

3. **الأمان:**
   - جميع العمليات تتطلب مصادقة
   - يتم تسجيل جميع العمليات
   - التحقق من صحة البيانات المدخلة

4. **الأداء:**
   - العمليات غير متزامنة (async)
   - معالجة الأخطاء شاملة
   - تسجيل مفصل للعمليات
