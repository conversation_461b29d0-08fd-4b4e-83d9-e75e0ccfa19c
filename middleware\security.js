const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const cors = require('cors');
// const csrf = require('csurf'); // معطل مؤقتاً للاختبار
const cookieParser = require('cookie-parser');
const config = require('../config/config');

/**
 * تكوين وسائط الأمان
 */
module.exports = (app) => {
  // استخدام Helmet لتعزيز أمان HTTP headers
  app.use(helmet());

  // تكوين CORS
  app.use(cors({
    origin: config.cors.origin,
    methods: config.cors.methods,
    allowedHeaders: config.cors.allowedHeaders,
    credentials: true
  }));

  // تقييد معدل الطلبات
  const limiter = rateLimit({
    windowMs: config.rateLimit.windowMs,
    max: config.rateLimit.max,
    message: {
      success: false,
      message: 'تم تجاوز الحد المسموح من الطلبات، يرجى المحاولة مرة أخرى لاحقًا'
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
  app.use(limiter);

  // تحليل ملفات تعريف الارتباط
  app.use(cookieParser());

  // حماية CSRF (معطلة مؤقتاً للاختبار)
  // if (config.server.env === 'production') {
  //   app.use(csrf({ cookie: true }));

  //   // وسيط لإرسال توكن CSRF إلى العميل
  //   app.use((req, res, next) => {
  //     res.cookie('XSRF-TOKEN', req.csrfToken(), {
  //       sameSite: 'strict',
  //       secure: true,
  //       httpOnly: false
  //     });
  //     next();
  //   });

  //   // معالجة أخطاء CSRF (فقط في الإنتاج)
  //   app.use((err, req, res, next) => {
  //     if (err.code === 'EBADCSRFTOKEN') {
  //       return res.status(403).json({
  //         success: false,
  //         message: 'خطأ في التحقق من CSRF'
  //       });
  //     }
  //     next(err);
  //   });
  // }
};
