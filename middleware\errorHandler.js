const logger = require('../utils/logger');

/**
 * وسيط لمعالجة الأخطاء
 */
module.exports = (app) => {
  // وسيط للتعامل مع المسارات غير الموجودة
  app.use((req, res, next) => {
    res.status(404).json({
      success: false,
      message: 'المورد غير موجود'
    });
  });

  // وسيط للتعامل مع الأخطاء العامة
  app.use((err, req, res, next) => {
    // تسجيل الخطأ
    logger.error(`${err.name}: ${err.message}`, { 
      stack: err.stack,
      method: req.method,
      path: req.path,
      ip: req.ip
    });

    // تحديد رمز الحالة
    const statusCode = err.statusCode || 500;
    
    // إرسال استجابة الخطأ
    res.status(statusCode).json({
      success: false,
      message: config.server.env === 'production' 
        ? 'حدث خطأ في الخادم' 
        : err.message,
      ...(config.server.env !== 'production' && { stack: err.stack })
    });
  });
};
