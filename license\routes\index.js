/**
 * مسارات قسم بيانات الترخيص
 */
const express = require('express');
const router = express.Router();
const { authenticate, authorize } = require('../../middleware/auth');
const logger = require('../../utils/logger');

// استيراد وحدات التحكم بالتراخيص
const licenseController = require('../controllers/licenseController');

/**
 * @route   GET /api/license/status
 * @desc    التحقق من حالة الترخيص
 * @access  خاص (يتطلب مصادقة)
 */
router.get('/status', authenticate, licenseController.checkStatus);

/**
 * @route   POST /api/license/validate
 * @desc    التحقق من صحة مفتاح الترخيص
 * @access  عام
 */
router.post('/validate', licenseController.validateLicense);

/**
 * @route   POST /api/license/activate
 * @desc    تفعيل الترخيص
 * @access  خاص (يتطلب مصادقة)
 */
router.post('/activate', authenticate, licenseController.activateLicense);

/**
 * @route   POST /api/license/deactivate
 * @desc    تعطيل الترخيص
 * @access  خاص (يتطلب مصادقة)
 */
router.post('/deactivate', authenticate, licenseController.deactivateLicense);

/**
 * @route   GET /api/license/list
 * @desc    الحصول على قائمة التراخيص
 * @access  خاص (يتطلب مصادقة ودور المسؤول)
 */
router.get('/list', authenticate, authorize('admin'), licenseController.getLicenses);

/**
 * @route   POST /api/license/extend
 * @desc    تمديد فترة الترخيص
 * @access  خاص (يتطلب مصادقة ودور المسؤول)
 */
router.post('/extend', authenticate, authorize('admin'), licenseController.extendLicense);

/**
 * @route   POST /api/license/upgrade
 * @desc    ترقية نوع الترخيص
 * @access  خاص (يتطلب مصادقة ودور المسؤول)
 */
router.post('/upgrade', authenticate, authorize('admin'), licenseController.upgradeLicense);

module.exports = router;
