# -*- coding: utf-8 -*-
import os
import sys
import time
import subprocess
import threading
from PIL import Image
from PyQt5.QtWidgets import (QApplication, QMainWindow, QPushButton, QLabel,
                            QWidget, QMessageBox)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QPixmap, QFont, QIcon

try:
    from ctypes import windll
    windll.shcore.SetProcessDpiAwareness(1)
except Exception:
    pass

def resource_path(relative_path):
    """
    الحصول على المسار المطلق للملفات سواء أثناء التطوير أو بعد تجميع التطبيق باستخدام PyInstaller.
    """
    try:
        # عند التشغيل من ملف EXE مجمّع
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

def get_adb_path():
    """
    تحديد مسار adb.exe من مجلد platform-tools الموجود بجانب التطبيق.
    """
    if getattr(sys, 'frozen', False):
        base_path = os.path.dirname(sys.executable)
    else:
        base_path = os.path.abspath(".")
    adb_filename = "adb.exe" if sys.platform.startswith("win") else "adb"
    return os.path.join(base_path, "platform-tools", adb_filename)

class GFX(QMainWindow):
    def __init__(self, parent=None):
        super().__init__(parent)

        # إعداد النافذة الأساسي
        self.setObjectName("JOGAMETOOL")
        self.resize(1262, 846)
        self.setWindowTitle("🎮 JO GAME TOOL - PUBG Mobile Graphics Optimizer")
        self.setIconSize(QSize(80, 80))

        # شريط عنوان احترافي
        self.setStyleSheet("""
            QMainWindow {
                background-color: #121212;
                color: #FFFFFF;
            }
            QWidget {
                background-color: #121212;
                color: #FFFFFF;
            }
            QMainWindow::title {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2d2d2d, stop:0.5 #3d3d3d, stop:1 #2d2d2d);
                color: #FFFFFF;
                font-family: "Segoe UI Black";
                font-size: 14px;
                font-weight: bold;
                padding: 5px;
                border-bottom: 3px solid #ecb440;
            }
        """)
        
        # إنشاء الويدجت المركزي
        self.centralwidget = QWidget(self)
        self.centralwidget.setObjectName("centralwidget")
        self.setCentralWidget(self.centralwidget)



        # المتغيرات الأساسية
        self.current_settings = {
            "graphics": None,
            "framerate": None,
            "style": None,
            "sfx": None,
            "shadow": None
        }
        
        self.adb_path = None
        self.adb_device = None
        self.activeSavContent = None
        self.assets = {}
        self.interactive_buttons = []
        self.is_adb_connected = False
        self.style_buttons = {}
        self.selected_style = None

        self.emulator_arch = "Unknown"
        self.emulator_path = self.detect_emulator_path()





        # إنشاء الصور المطلوبة وإعداد الواجهة
        self.create_required_images()
        self.setupUi()

        # تعطيل واجهة المستخدم افتراضيًا حتى يتم الاتصال بـ ADB
        self.toggle_ui_state(False)

        # بدء خيط للتحقق من الاتصال في الخلفية
        threading.Thread(target=self.check_connection, daemon=True).start()

    def detect_emulator_path(self):
        path_64 = r"C:\Program Files\TxGameAssistant\ui\AndroidEmulatorEn.exe"
        path_32 = r"C:\Program Files (x86)\TxGameAssistant\ui\AndroidEmulatorEn.exe"
        if os.path.exists(path_64):
            return path_64
        elif os.path.exists(path_32):
            return path_32
        else:
            return None

    def create_required_images(self):
        assets_info = {
            "app_icon": ("app_icon.png", (30, 30), "#2d2d2d"),
            "pubg_icon": ("pubg_icon.png", (80, 80), "#121212")
        }
        for asset_name, (filename, size, color) in assets_info.items():
            path = resource_path(os.path.join("assets", filename))
            if not os.path.exists(path):
                os.makedirs(os.path.dirname(path), exist_ok=True)
                if asset_name == "pubg_icon":
                    # إنشاء أيقونة PUBG أكثر وضوحاً
                    img = Image.new("RGB", size, color)
                else:
                    img = Image.new("RGB", size, color)
                img.save(path)
            self.assets[asset_name] = path

    def load_asset(self, asset_key, size):
        if asset_key in self.assets:
            pixmap = QPixmap(self.assets[asset_key])
            if size:
                pixmap = pixmap.scaled(size[0], size[1], Qt.KeepAspectRatio, Qt.SmoothTransformation)
            return pixmap
        return None

    def get_button_style(self, selected=False):
        """إرجاع نمط الزر مع الألوان المحسنة"""
        if selected:
            return """
                QPushButton {
                    background-color: #ecb440;
                    color: #FFFFFF;
                    border: 2px solid #ecb440;
                    border-radius: 5px;
                    font-family: "Segoe UI Black";
                    font-size: 14px;
                    font-weight: bold;
                    font-style: italic;
                    padding: 5px;
                }
                QPushButton:hover {
                    background-color: #ffd966;
                    border: 2px solid #ffd966;
                }
                QPushButton:pressed {
                    background-color: #d4a73a;
                    border: 2px solid #d4a73a;
                }
                QPushButton:disabled {
                    background-color: #1A1A1A;
                    color: #888888;
                    border: 2px solid #555555;
                }
            """
        else:
            return """
                QPushButton {
                    background-color: #2A2A2A;
                    color: #FFFFFF;
                    border: 2px solid #ecb440;
                    border-radius: 5px;
                    font-family: "Segoe UI Black";
                    font-size: 14px;
                    font-weight: bold;
                    font-style: italic;
                    padding: 5px;
                }
                QPushButton:hover {
                    background-color: #3A3A3A;
                    border: 2px solid #ecb440;
                }
                QPushButton:pressed {
                    background-color: #4A4A4A;
                    border: 2px solid #ecb440;
                }
                QPushButton:disabled {
                    background-color: #1A1A1A;
                    color: #888888;
                    border: 2px solid #555555;
                }
            """

    def get_label_style(self):
        """إرجاع نمط التسميات"""
        return """
            QLabel {
                color: #FFFFFF;
                font-family: "Segoe UI Black";
                font-size: 14px;
                font-weight: bold;
                background-color: transparent;
            }
        """

    def setupUi(self):
        # إعداد الخطوط
        font = QFont()
        font.setFamily("Segoe UI Black")
        font.setPointSize(14)
        font.setBold(True)
        font.setWeight(75)





        # زر ADB مع مؤشر الحالة
        self.ADB = QPushButton(self.centralwidget)
        self.ADB.setGeometry(10, 800, 75, 31)
        self.ADB.setFont(font)
        self.ADB.setObjectName("ADB")
        self.ADB.setText("ADB")
        self.ADB.setStyleSheet("""
            QPushButton {
                background-color: #ecb440;
                color: white;
                border: 2px solid #ecb440;
                border-radius: 5px;
                font-family: "Segoe UI Black";
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #ffd966;
                border: 2px solid #ecb440;
            }
            QPushButton:pressed {
                background-color: #d4a73a;
                border: 2px solid #ecb440;
            }
        """)
        self.ADB.clicked.connect(self.check_connection)

        # مؤشر الحالة (بجانب زر ADB)
        self.status_indicator = QLabel(self.centralwidget)
        self.status_indicator.setGeometry(95, 790, 30, 50)
        self.status_indicator.setText("●")
        self.status_indicator.setStyleSheet("color: #ff0000; font-size: 40px; font-weight: bold;")
        self.status_indicator.setAlignment(Qt.AlignCenter)

        # أزرار التحكم الرئيسية
        self.Get_Graphics_file = QPushButton(self.centralwidget)
        self.Get_Graphics_file.setGeometry(870, 780, 181, 51)
        self.Get_Graphics_file.setFont(font)
        self.Get_Graphics_file.setObjectName("Get_Graphics_file")
        self.Get_Graphics_file.setText("Get Graphics file")
        self.Get_Graphics_file.setStyleSheet(self.get_button_style())
        self.Get_Graphics_file.clicked.connect(self.get_graphics_file)
        self.interactive_buttons.append(self.Get_Graphics_file)

        self.Apply = QPushButton(self.centralwidget)
        self.Apply.setGeometry(1060, 780, 181, 51)
        self.Apply.setFont(font)
        self.Apply.setObjectName("Apply")
        self.Apply.setText("Apply")
        self.Apply.setStyleSheet(self.get_button_style())
        self.Apply.clicked.connect(self.apply_settings)
        self.interactive_buttons.append(self.Apply)

        # تسمية Graphics
        self.Graphics = QLabel(self.centralwidget)
        self.Graphics.setGeometry(20, 20, 600, 31)
        self.Graphics.setFont(font)
        self.Graphics.setObjectName("Graphics")
        self.Graphics.setText("Graphics (lower the settings if the device lags)")
        self.Graphics.setStyleSheet(self.get_label_style())

        # أزرار Graphics
        self.super_smooth = QPushButton(self.centralwidget)
        self.super_smooth.setGeometry(20, 70, 161, 51)
        self.super_smooth.setFont(font)
        self.super_smooth.setObjectName("super_smooth")
        self.super_smooth.setText("Super Smooth")
        self.super_smooth.setStyleSheet(self.get_button_style())
        self.super_smooth.clicked.connect(lambda: self.select_option("graphics", "Super Smooth"))
        self.interactive_buttons.append(self.super_smooth)

        self.smooth = QPushButton(self.centralwidget)
        self.smooth.setGeometry(190, 70, 161, 51)
        self.smooth.setFont(font)
        self.smooth.setObjectName("smooth")
        self.smooth.setText("Smooth")
        self.smooth.setStyleSheet(self.get_button_style())
        self.smooth.clicked.connect(lambda: self.select_option("graphics", "Smooth"))
        self.interactive_buttons.append(self.smooth)

        self.Balansed = QPushButton(self.centralwidget)
        self.Balansed.setGeometry(360, 70, 161, 51)
        self.Balansed.setFont(font)
        self.Balansed.setObjectName("Balansed")
        self.Balansed.setText("Balanced")
        self.Balansed.setStyleSheet(self.get_button_style())
        self.Balansed.clicked.connect(lambda: self.select_option("graphics", "Balanced"))
        self.interactive_buttons.append(self.Balansed)

        self.HD = QPushButton(self.centralwidget)
        self.HD.setGeometry(530, 70, 161, 51)
        self.HD.setFont(font)
        self.HD.setObjectName("HD")
        self.HD.setText("HD")
        self.HD.setStyleSheet(self.get_button_style())
        self.HD.clicked.connect(lambda: self.select_option("graphics", "HD"))
        self.interactive_buttons.append(self.HD)

        self.HDR = QPushButton(self.centralwidget)
        self.HDR.setGeometry(700, 70, 161, 51)
        self.HDR.setFont(font)
        self.HDR.setObjectName("HDR")
        self.HDR.setText("HDR")
        self.HDR.setStyleSheet(self.get_button_style())
        self.HDR.clicked.connect(lambda: self.select_option("graphics", "HDR"))
        self.interactive_buttons.append(self.HDR)

        self.Ultra_HD = QPushButton(self.centralwidget)
        self.Ultra_HD.setGeometry(870, 70, 161, 51)
        self.Ultra_HD.setFont(font)
        self.Ultra_HD.setObjectName("Ultra_HD")
        self.Ultra_HD.setText("Ultra HD")
        self.Ultra_HD.setStyleSheet(self.get_button_style())
        self.Ultra_HD.clicked.connect(lambda: self.select_option("graphics", "Ultra HD"))
        self.interactive_buttons.append(self.Ultra_HD)

        # تسمية Frame Rate
        self.Frame_Rate = QLabel(self.centralwidget)
        self.Frame_Rate.setGeometry(20, 150, 600, 21)
        self.Frame_Rate.setFont(font)
        self.Frame_Rate.setObjectName("Frame_Rate")
        self.Frame_Rate.setText("Frame Rate (lower the settings if the device lags)")
        self.Frame_Rate.setStyleSheet(self.get_label_style())

        # أزرار Frame Rate
        self.Low = QPushButton(self.centralwidget)
        self.Low.setGeometry(20, 180, 161, 51)
        self.Low.setFont(font)
        self.Low.setObjectName("Low")
        self.Low.setText("Low")
        self.Low.setStyleSheet(self.get_button_style())
        self.Low.clicked.connect(lambda: self.select_option("framerate", "Low"))
        self.interactive_buttons.append(self.Low)

        self.Medium = QPushButton(self.centralwidget)
        self.Medium.setGeometry(190, 180, 161, 51)
        self.Medium.setFont(font)
        self.Medium.setObjectName("Medium")
        self.Medium.setText("Medium")
        self.Medium.setStyleSheet(self.get_button_style())
        self.Medium.clicked.connect(lambda: self.select_option("framerate", "Medium"))
        self.interactive_buttons.append(self.Medium)

        self.High = QPushButton(self.centralwidget)
        self.High.setGeometry(360, 180, 161, 51)
        self.High.setFont(font)
        self.High.setObjectName("High")
        self.High.setText("High")
        self.High.setStyleSheet(self.get_button_style())
        self.High.clicked.connect(lambda: self.select_option("framerate", "High"))
        self.interactive_buttons.append(self.High)

        self.Ultra = QPushButton(self.centralwidget)
        self.Ultra.setGeometry(530, 180, 161, 51)
        self.Ultra.setFont(font)
        self.Ultra.setObjectName("Ultra")
        self.Ultra.setText("Ultra")
        self.Ultra.setStyleSheet(self.get_button_style())
        self.Ultra.clicked.connect(lambda: self.select_option("framerate", "Ultra"))
        self.interactive_buttons.append(self.Ultra)

        self.Extreme = QPushButton(self.centralwidget)
        self.Extreme.setGeometry(700, 180, 161, 51)
        self.Extreme.setFont(font)
        self.Extreme.setObjectName("Extreme")
        self.Extreme.setText("Extreme")
        self.Extreme.setStyleSheet(self.get_button_style())
        self.Extreme.clicked.connect(lambda: self.select_option("framerate", "Extreme"))
        self.interactive_buttons.append(self.Extreme)

        self.Extreme_2 = QPushButton(self.centralwidget)
        self.Extreme_2.setGeometry(870, 180, 161, 51)
        self.Extreme_2.setFont(font)
        self.Extreme_2.setObjectName("Extreme_2")
        self.Extreme_2.setText("Extreme +")
        self.Extreme_2.setStyleSheet(self.get_button_style())
        self.Extreme_2.clicked.connect(lambda: self.select_option("framerate", "Extreme +"))
        self.interactive_buttons.append(self.Extreme_2)

        self.Ultra_Extreme = QPushButton(self.centralwidget)
        self.Ultra_Extreme.setGeometry(1040, 180, 161, 51)
        self.Ultra_Extreme.setFont(font)
        self.Ultra_Extreme.setObjectName("Ultra_Extreme")
        self.Ultra_Extreme.setText("Ultra Extreme")
        self.Ultra_Extreme.setStyleSheet(self.get_button_style())
        self.Ultra_Extreme.clicked.connect(lambda: self.select_option("framerate", "Ultra Extreme"))
        self.interactive_buttons.append(self.Ultra_Extreme)

        # تسمية Style
        self.style = QLabel(self.centralwidget)
        self.style.setGeometry(20, 230, 200, 71)
        self.style.setFont(font)
        self.style.setObjectName("style")
        self.style.setText("Style")
        self.style.setStyleSheet(self.get_label_style())

        # أزرار Style مع الصور
        self.Classic = QPushButton(self.centralwidget)
        self.Classic.setGeometry(20, 310, 164, 164)
        self.Classic.setText("")
        self.Classic.setObjectName("Classic")
        self.Classic.setStyleSheet(self.get_style_button_style())
        self.Classic.clicked.connect(lambda: self.select_style("Classic"))
        self.style_buttons["Classic"] = self.Classic
        self.interactive_buttons.append(self.Classic)



        self.Colorful = QPushButton(self.centralwidget)
        self.Colorful.setGeometry(200, 310, 164, 164)
        self.Colorful.setText("")
        self.Colorful.setObjectName("Colorful")
        self.Colorful.setStyleSheet(self.get_style_button_style())
        self.Colorful.clicked.connect(lambda: self.select_style("Colorful"))
        self.style_buttons["Colorful"] = self.Colorful
        self.interactive_buttons.append(self.Colorful)

        self.Realistic = QPushButton(self.centralwidget)
        self.Realistic.setGeometry(380, 310, 164, 164)
        self.Realistic.setText("")
        self.Realistic.setObjectName("Realistic")
        self.Realistic.setStyleSheet(self.get_style_button_style())
        self.Realistic.clicked.connect(lambda: self.select_style("Realistic"))
        self.style_buttons["Realistic"] = self.Realistic
        self.interactive_buttons.append(self.Realistic)

        self.Soft = QPushButton(self.centralwidget)
        self.Soft.setGeometry(560, 310, 164, 164)
        self.Soft.setText("")
        self.Soft.setObjectName("Soft")
        self.Soft.setStyleSheet(self.get_style_button_style())
        self.Soft.clicked.connect(lambda: self.select_style("Soft"))
        self.style_buttons["Soft"] = self.Soft
        self.interactive_buttons.append(self.Soft)

        self.Movie = QPushButton(self.centralwidget)
        self.Movie.setGeometry(740, 310, 164, 164)
        self.Movie.setText("")
        self.Movie.setObjectName("Movie")
        self.Movie.setStyleSheet(self.get_style_button_style())
        self.Movie.clicked.connect(lambda: self.select_style("Movie"))
        self.style_buttons["Movie"] = self.Movie
        self.interactive_buttons.append(self.Movie)

        # تحميل صور الأنماط
        self.load_style_images()

        # تسمية SFX Quality
        self.label = QLabel(self.centralwidget)
        self.label.setGeometry(20, 500, 200, 41)
        self.label.setFont(font)
        self.label.setObjectName("label")
        self.label.setText("SFX Quality")
        self.label.setStyleSheet(self.get_label_style())

        # أزرار SFX Quality
        self.Low_2 = QPushButton(self.centralwidget)
        self.Low_2.setGeometry(20, 570, 161, 51)
        self.Low_2.setFont(font)
        self.Low_2.setObjectName("Low_2")
        self.Low_2.setText("Low")
        self.Low_2.setStyleSheet(self.get_button_style())
        self.Low_2.clicked.connect(lambda: self.select_option("sfx", "Low"))
        self.interactive_buttons.append(self.Low_2)

        self.High_2 = QPushButton(self.centralwidget)
        self.High_2.setGeometry(190, 570, 161, 51)
        self.High_2.setFont(font)
        self.High_2.setObjectName("High_2")
        self.High_2.setText("High")
        self.High_2.setStyleSheet(self.get_button_style())
        self.High_2.clicked.connect(lambda: self.select_option("sfx", "High"))
        self.interactive_buttons.append(self.High_2)

        self.Ultra_2 = QPushButton(self.centralwidget)
        self.Ultra_2.setGeometry(360, 570, 161, 51)
        self.Ultra_2.setFont(font)
        self.Ultra_2.setObjectName("Ultra_2")
        self.Ultra_2.setText("Ultra")
        self.Ultra_2.setStyleSheet(self.get_button_style())
        self.Ultra_2.clicked.connect(lambda: self.select_option("sfx", "Ultra"))
        self.interactive_buttons.append(self.Ultra_2)

        # تسمية Shadow
        self.label_2 = QLabel(self.centralwidget)
        self.label_2.setGeometry(30, 640, 200, 41)
        self.label_2.setFont(font)
        self.label_2.setObjectName("label_2")
        self.label_2.setText("Shadow")
        self.label_2.setStyleSheet(self.get_label_style())

        # أزرار Shadow
        self.Enable = QPushButton(self.centralwidget)
        self.Enable.setGeometry(20, 690, 161, 51)
        self.Enable.setFont(font)
        self.Enable.setObjectName("Enable")
        self.Enable.setText("Enable")
        self.Enable.setStyleSheet(self.get_button_style())
        self.Enable.clicked.connect(lambda: self.select_option("shadow", "Enable"))
        self.interactive_buttons.append(self.Enable)

        self.Disable = QPushButton(self.centralwidget)
        self.Disable.setGeometry(190, 690, 161, 51)
        self.Disable.setFont(font)
        self.Disable.setObjectName("Disable")
        self.Disable.setText("Disable")
        self.Disable.setStyleSheet(self.get_button_style())
        self.Disable.clicked.connect(lambda: self.select_option("shadow", "Disable"))
        self.interactive_buttons.append(self.Disable)

        # زر PUBG بدون دائرة مع أنيميشن ضغط
        self.Launch_PUBG_Mobile = QPushButton(self.centralwidget)
        self.Launch_PUBG_Mobile.setGeometry(130, 790, 51, 51)
        self.Launch_PUBG_Mobile.setText("")
        self.Launch_PUBG_Mobile.setObjectName("Launch_PUBG_Mobile")
        self.Launch_PUBG_Mobile.setStyleSheet("""
            QPushButton {
                background-color: #2A2A2A;
                border: 2px solid #ecb440;
                border-radius: 8px;
                padding: 2px;
                min-width: 47px;
                max-width: 47px;
                min-height: 47px;
                max-height: 47px;
            }
            QPushButton:hover {
                background-color: #ecb440;
                border: 2px solid #ffd966;
                color: #000000;
            }
            QPushButton:pressed {
                background-color: #d4a73a;
                border: 2px solid #b8941f;
                padding: 4px 2px 0px 4px;
            }
        """)
        self.Launch_PUBG_Mobile.clicked.connect(self.launch_pubg)
        self.Launch_PUBG_Mobile.always_active = True
        self.interactive_buttons.append(self.Launch_PUBG_Mobile)

        # تحميل أيقونة PUBG
        pubg_icon = self.load_asset("pubg_icon", (43, 43))
        if pubg_icon:
            self.Launch_PUBG_Mobile.setIcon(QIcon(pubg_icon))
            self.Launch_PUBG_Mobile.setIconSize(QSize(43, 43))

    def get_style_button_style(self, selected=False):
        """إرجاع نمط أزرار الستايل - إطار ذهبي للمحدد ورمادي خفيف للhover"""
        if selected:
            return """
                QPushButton {
                    background-color: #121212;
                    border: 3px solid #ecb440;
                    border-radius: 5px;
                    padding: 2px;
                }
                QPushButton:hover {
                    background-color: #1A1A1A;
                    border: 3px solid #ffd966;
                }
                QPushButton:pressed {
                    background-color: #2A2A2A;
                    border: 3px solid #d4a73a;
                }
                QPushButton:disabled {
                    background-color: #0A0A0A;
                    border: 3px solid #555555;
                }
            """
        else:
            return """
                QPushButton {
                    background-color: #121212;
                    border: none;
                    border-radius: 5px;
                    padding: 2px;
                }
                QPushButton:hover {
                    background-color: #1A1A1A;
                    border: 2px solid #555555;
                }
                QPushButton:pressed {
                    background-color: #2A2A2A;
                    border: 2px solid #666666;
                }
                QPushButton:disabled {
                    background-color: #0A0A0A;
                    border: 1px solid #333333;
                }
            """

    def load_style_images(self):
        """تحميل صور الأنماط مع إطار ذهبي للمحدد"""
        style_data = [
            ("Classic", "classic.png", "#8B4513"),
            ("Colorful", "colorful.png", "#FF4500"),
            ("Realistic", "realistic.png", "#2E8B57"),
            ("Soft", "soft.png", "#87CEEB"),
            ("Movie", "movie.png", "#4B0082"),
        ]

        for style_name, image_file, fallback_color in style_data:
            image_path = resource_path(os.path.join("assets", image_file))
            button = self.style_buttons[style_name]

            if os.path.exists(image_path):
                pixmap = QPixmap(image_path)
                pixmap = pixmap.scaled(158, 158, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                button.setIcon(QIcon(pixmap))
                button.setIconSize(QSize(158, 158))
            else:
                # إذا لم تكن الصورة موجودة، نستخدم لون خلفية
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {fallback_color};
                        border: 1px solid #2A2A2A;
                        border-radius: 5px;
                        padding: 2px;
                    }}
                    QPushButton:hover {{
                        background-color: #1A1A1A;
                        border: 2px solid #ecb440;
                    }}
                    QPushButton:pressed {{
                        background-color: #2A2A2A;
                        border: 2px solid #ecb440;
                    }}
                    QPushButton:disabled {{
                        background-color: #0A0A0A;
                        border: 1px solid #555555;
                    }}
                """)

    def select_option(self, section, option):
        """تحديد خيار في قسم معين"""
        if not self.is_adb_connected:
            self.show_notification("يرجى الاتصال بالمحاكي أولاً عن طريق الضغط على زر ADB")
            return

        self.current_settings[section] = option
        self.update_button_colors(section)

    def select_style(self, style_name):
        """تحديد نمط الرسومات مع خطوط فوق وتحت"""
        if not self.is_adb_connected:
            self.show_notification("يرجى الاتصال بالمحاكي أولاً عن طريق الضغط على زر ADB")
            return

        # إعادة تعيين النمط السابق
        if self.selected_style and self.selected_style in self.style_buttons:
            old_button = self.style_buttons[self.selected_style]
            old_button.setStyleSheet(self.get_style_button_style(False))

        # تحديد النمط الجديد
        self.selected_style = style_name
        self.current_settings["style"] = style_name
        new_button = self.style_buttons[style_name]
        new_button.setStyleSheet(self.get_style_button_style(True))

    def update_button_colors(self, section):
        """تحديث ألوان الأزرار بناءً على الاختيار - كل قسم منفصل"""
        # تحديث أزرار Graphics فقط
        if section == "graphics":
            graphics_buttons = {
                "super_smooth": "Super Smooth",
                "smooth": "Smooth",
                "Balansed": "Balanced",
                "HD": "HD",
                "HDR": "HDR",
                "Ultra_HD": "Ultra HD"
            }
            for widget in self.interactive_buttons:
                if hasattr(widget, "objectName") and widget.objectName() in graphics_buttons:
                    is_selected = graphics_buttons[widget.objectName()] == self.current_settings["graphics"]
                    widget.setStyleSheet(self.get_button_style(is_selected))

        # تحديث أزرار Frame Rate فقط
        elif section == "framerate":
            framerate_buttons = {
                "Low": "Low",
                "Medium": "Medium",
                "High": "High",
                "Ultra": "Ultra",
                "Extreme": "Extreme",
                "Extreme_2": "Extreme +",
                "Ultra_Extreme": "Ultra Extreme"
            }
            for widget in self.interactive_buttons:
                if hasattr(widget, "objectName") and widget.objectName() in framerate_buttons:
                    is_selected = framerate_buttons[widget.objectName()] == self.current_settings["framerate"]
                    widget.setStyleSheet(self.get_button_style(is_selected))

        # تحديث أزرار SFX فقط
        elif section == "sfx":
            sfx_buttons = {
                "Low_2": "Low",
                "High_2": "High",
                "Ultra_2": "Ultra"
            }
            for widget in self.interactive_buttons:
                if hasattr(widget, "objectName") and widget.objectName() in sfx_buttons:
                    is_selected = sfx_buttons[widget.objectName()] == self.current_settings["sfx"]
                    widget.setStyleSheet(self.get_button_style(is_selected))

        # تحديث أزرار Shadow فقط
        elif section == "shadow":
            shadow_buttons = {
                "Enable": "Enable",
                "Disable": "Disable"
            }
            for widget in self.interactive_buttons:
                if hasattr(widget, "objectName") and widget.objectName() in shadow_buttons:
                    is_selected = shadow_buttons[widget.objectName()] == self.current_settings["shadow"]
                    widget.setStyleSheet(self.get_button_style(is_selected))

    def toggle_ui_state(self, connected: bool):
        """تفعيل/تعطيل واجهة المستخدم بناءً على حالة الاتصال"""
        self.is_adb_connected = connected

        # تفعيل/تعطيل الأزرار التفاعلية
        for widget in self.interactive_buttons:
            if hasattr(widget, "always_active") and widget.always_active:
                continue
            try:
                widget.setEnabled(connected)
            except Exception:
                pass

        # تحديث مؤشر حالة الاتصال
        if connected:
            self.status_indicator.setStyleSheet("color: #00ff00; font-size: 40px; font-weight: bold;")
        else:
            self.status_indicator.setStyleSheet("color: #ff0000; font-size: 40px; font-weight: bold;")

    def execute_adb_command(self, arguments):
        """تنفيذ أوامر ADB"""
        cmd = [self.adb_path] + arguments.split()

        # إضافة خيارات لإخفاء نافذة الطرفية في Windows
        startupinfo = None
        if sys.platform.startswith('win'):
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = 0  # SW_HIDE

        proc = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            shell=False,
            startupinfo=startupinfo
        )

        stdout, stderr = proc.communicate()
        out_str = stdout.decode("utf-8").strip()
        err_str = stderr.decode("utf-8").strip().lower()
        if err_str and any(x in err_str for x in ["no such file", "error", "failed"]):
            raise Exception(stderr.decode("utf-8").strip())
        return out_str

    def find_adb_device(self):
        """البحث عن الأجهزة المتصلة"""
        output = self.execute_adb_command("devices")
        for line in output.splitlines():
            if "device" in line and "List" not in line:
                if "offline" not in line.lower():
                    self.adb_device = line.split()[0]
                    return self.adb_device
        return None

    def setup_adb(self):
        """إعداد ADB"""
        self.adb_path = get_adb_path()
        if not os.path.exists(self.adb_path):
            raise Exception("ADB not found in platform-tools folder.")
        try:
            self.execute_adb_command("kill-server")
        except Exception:
            pass
        time.sleep(0.5)
        self.execute_adb_command("start-server")
        time.sleep(0.5)
        return self.find_adb_device()

    def check_connection(self):
        """فحص الاتصال بالجهاز"""
        for _ in range(5):
            try:
                device = self.setup_adb()
                if device:
                    self.adb_device = device
                    self.status_indicator.setStyleSheet("color: #00ff00; font-size: 40px; font-weight: bold;")
                    self.show_notification(f"Device connected: {self.adb_device}")
                    self.toggle_ui_state(True)

                    # تشغيل قراءة الملف في خيط منفصل
                    threading.Thread(target=self.read_active_sav_file_directly, daemon=True).start()
                    return
                else:
                    self.status_indicator.setStyleSheet("color: #ff0000; font-size: 40px; font-weight: bold;")
                    self.show_notification("No device connected. Retrying...")
            except Exception as e:
                self.status_indicator.setStyleSheet("color: #ff0000; font-size: 40px; font-weight: bold;")
                self.show_notification("Connection error: " + str(e))
            time.sleep(0.3)

        # تعطيل واجهة المستخدم إذا فشل الاتصال
        self.toggle_ui_state(False)

    def show_notification(self, message):
        """عرض الإشعارات"""
        print(message)  # للتصحيح

    def launch_pubg(self):
        """تشغيل PUBG Mobile"""
        if self.emulator_path and os.path.exists(self.emulator_path):
            command = f'"{self.emulator_path}" -vm 100'

            # إضافة خيارات لإخفاء نافذة الطرفية في Windows
            startupinfo = None
            if sys.platform.startswith('win'):
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = 0  # SW_HIDE

            subprocess.Popen(command, shell=True, startupinfo=startupinfo)
            self.show_notification("Emulator starting...")
        else:
            self.show_notification("Emulator not found.")

    def read_active_sav_file_directly(self):
        """قراءة ملف Active.sav مباشرة"""
        sav_remote = "/sdcard/Android/data/com.tencent.ig/files/UE4Game/ShadowTrackerExtra/ShadowTrackerExtra/Saved/SaveGames/Active.sav"
        local_sav = os.path.join(os.environ.get("TEMP", "/tmp"), "Active.sav")
        try:
            self.execute_adb_command(f"-s {self.adb_device} pull {sav_remote} {local_sav}")
            if os.path.exists(local_sav):
                self.activeSavContent = bytearray(open(local_sav, "rb").read())
                self.show_notification("Active.sav loaded successfully.")
            else:
                self.show_notification("Failed to load Active.sav.")
        except Exception as e:
            self.show_notification("Error loading Active.sav: " + str(e))

    def get_graphics_file(self):
        """قراءة ملفات الإعدادات من الجهاز"""
        if not self.adb_device:
            self.show_notification("لا يوجد اتصال بالمحاكي. يرجى الضغط على زر ADB للاتصال أولاً.")
            QMessageBox.warning(self, "تنبيه", "لا يوجد اتصال بالمحاكي. يرجى الضغط على زر ADB للاتصال أولاً.")
            return
        try:
            sav_remote = "/sdcard/Android/data/com.tencent.ig/files/UE4Game/ShadowTrackerExtra/ShadowTrackerExtra/Saved/SaveGames/Active.sav"
            local_sav = os.path.join(os.environ.get("TEMP", "/tmp"), "Active.sav")
            self.execute_adb_command(f"-s {self.adb_device} pull {sav_remote} {local_sav}")
            if os.path.exists(local_sav):
                self.activeSavContent = bytearray(open(local_sav, "rb").read())
                self.show_notification("Active.sav loaded. Parsing current settings...")
                self.parse_active_sav_settings()
            else:
                self.show_notification("Failed to load Active.sav.")

            ini_remote = "/sdcard/Android/data/com.tencent.ig/files/UE4Game/ShadowTrackerExtra/ShadowTrackerExtra/Saved/Config/Android/UserCustom.ini"
            local_ini = os.path.join(os.environ.get("TEMP", "/tmp"), "UserCustom.ini")
            self.execute_adb_command(f"-s {self.adb_device} pull {ini_remote} {local_ini}")
            if os.path.exists(local_ini):
                self.parse_shadow_settings(local_ini)

            settings_remote = "/sdcard/Android/data/com.tencent.ig/files/UE4Game/ShadowTrackerExtra/ShadowTrackerExtra/Saved/Config/Android/UserSettings.ini"
            local_settings = os.path.join(os.environ.get("TEMP", "/tmp"), "UserSettings.ini")
            self.execute_adb_command(f"-s {self.adb_device} pull {settings_remote} {local_settings}")
            if os.path.exists(local_settings):
                self.parse_sfx_settings(local_settings)

            self.update_button_colors("graphics")
            self.update_button_colors("framerate")
            self.update_button_colors("style")
            self.update_button_colors("sfx")
            self.update_button_colors("shadow")
        except Exception as e:
            self.show_notification(f"Error reading files: {e}")

    def parse_active_sav_settings(self):
        """تحليل إعدادات ملف Active.sav"""
        def get_property_value(prop_name):
            search_bytes = (prop_name + "\0\f\0\0\0IntProperty\0\u0004\0\0\0\0\0\0\0\0").encode("utf-8")
            idx = self.index_of_bytes(self.activeSavContent, search_bytes)
            if idx != -1:
                return self.activeSavContent[idx + len(search_bytes)]
            return None

        val_g = get_property_value("LobbyRenderQuality")
        if val_g is None:
            val_g = get_property_value("BattleRenderQuality")
        if val_g:
            mapping_g = {1: "Smooth", 2: "Balanced", 3: "HD", 4: "HDR", 5: "Ultra HD", 6: "Super Smooth"}
            self.current_settings["graphics"] = mapping_g.get(val_g, None)

        val_f = get_property_value("FPSLevel")
        if val_f is None:
            val_f = get_property_value("BattleFPS")
        if val_f is None:
            val_f = get_property_value("LobbyFPS")
        if val_f:
            mapping_f = {2: "Low", 3: "Medium", 4: "High", 5: "Ultra", 6: "Extreme", 7: "Extreme +", 8: "Ultra Extreme"}
            self.current_settings["framerate"] = mapping_f.get(val_f, None)

        val_s = get_property_value("BattleRenderStyle")
        if val_s is None:
            val_s = get_property_value("LobbyRenderStyle")
        if val_s:
            mapping_s = {1: "Classic", 2: "Colorful", 3: "Realistic", 4: "Soft", 6: "Movie"}
            self.current_settings["style"] = mapping_s.get(val_s, None)
            if self.current_settings["style"]:
                self.select_style(self.current_settings["style"])

        print("Parsed from Active.sav =>", self.current_settings)

    def parse_shadow_settings(self, ini_path):
        """تحليل إعدادات الظلال"""
        with open(ini_path, "r", encoding="utf-8") as f:
            lines = f.readlines()
        shadow_val = None
        for line in lines:
            if line.startswith("+CVars=0B572A11181D160E280C1815100D0044") or \
               line.startswith("+CVars=0B572C0A1C0B2A11181D160E2A0E100D1A1144"):
                last2 = line.strip()[-2:]
                if last2 == "48":
                    shadow_val = "Enable"
                elif last2 == "49":
                    shadow_val = "Disable"
                break
        if shadow_val:
            self.current_settings["shadow"] = shadow_val

    def parse_sfx_settings(self, ini_path):
        """تحليل إعدادات الصوت"""
        with open(ini_path, "r", encoding="utf-8") as f:
            lines = f.readlines()
        sfx_val = None
        for line in lines:
            if line.startswith("+CVars=SoundQualityType="):
                val = line.strip().split("=")[-1]
                if val == "0":
                    sfx_val = "Low"
                elif val == "1":
                    sfx_val = "High"
                elif val == "2":
                    sfx_val = "Ultra"
                break
        if sfx_val:
            self.current_settings["sfx"] = sfx_val

    def index_of_bytes(self, source, pattern):
        """البحث عن نمط في البايتات"""
        for i in range(len(source) - len(pattern) + 1):
            if source[i:i+len(pattern)] == pattern:
                return i
        return -1

    def apply_settings(self):
        """تطبيق الإعدادات على الجهاز"""
        if not self.adb_device:
            self.show_notification("لا يوجد اتصال بالمحاكي. يرجى الضغط على زر ADB للاتصال أولاً.")
            QMessageBox.warning(self, "تنبيه", "لا يوجد اتصال بالمحاكي. يرجى الضغط على زر ADB للاتصال أولاً.")
            return
        try:
            self.execute_adb_command(f"-s {self.adb_device} shell am force-stop com.tencent.ig")
            time.sleep(0.5)

            sav_remote = "/sdcard/Android/data/com.tencent.ig/files/UE4Game/ShadowTrackerExtra/ShadowTrackerExtra/Saved/SaveGames/Active.sav"
            local_sav = os.path.join(os.environ.get("TEMP", "/tmp"), "Active.sav")
            self.execute_adb_command(f"-s {self.adb_device} pull {sav_remote} {local_sav}")
            if not os.path.exists(local_sav):
                raise Exception("Failed to pull Active.sav from device.")
            self.activeSavContent = bytearray(open(local_sav, "rb").read())

            self.update_fps_settings()
            self.update_graphics_settings()
            self.update_art_quality_settings()

            open(local_sav, "wb").write(self.activeSavContent)
            self.execute_adb_command(f"-s {self.adb_device} push {local_sav} {sav_remote}")

            ini_remote = "/sdcard/Android/data/com.tencent.ig/files/UE4Game/ShadowTrackerExtra/ShadowTrackerExtra/Saved/Config/Android/UserCustom.ini"
            local_ini = os.path.join(os.environ.get("TEMP", "/tmp"), "UserCustom.ini")
            self.execute_adb_command(f"-s {self.adb_device} pull {ini_remote} {local_ini}")
            if not os.path.exists(local_ini):
                raise Exception("Failed to pull UserCustom.ini.")
            self.update_shadow_settings(local_ini)
            self.execute_adb_command(f"-s {self.adb_device} push {local_ini} {ini_remote}")

            settings_remote = "/sdcard/Android/data/com.tencent.ig/files/UE4Game/ShadowTrackerExtra/ShadowTrackerExtra/Saved/Config/Android/UserSettings.ini"
            local_settings = os.path.join(os.environ.get("TEMP", "/tmp"), "UserSettings.ini")
            self.execute_adb_command(f"-s {self.adb_device} pull {settings_remote} {local_settings}")
            if not os.path.exists(local_settings):
                raise Exception("Failed to pull UserSettings.ini.")
            self.update_sfx_settings(local_settings)
            self.execute_adb_command(f"-s {self.adb_device} push {local_settings} {settings_remote}")

            self.execute_adb_command(f"-s {self.adb_device} shell monkey -p com.tencent.ig -c android.intent.category.LAUNCHER 1")
            self.show_notification("Settings applied successfully.")
        except Exception as e:
            QMessageBox.critical(self, "Error", str(e))

    def update_fps_settings(self):
        """تحديث إعدادات معدل الإطارات"""
        fps_option = self.current_settings.get("framerate")
        mapping = {
            "Low": 2,
            "Medium": 3,
            "High": 4,
            "Ultra": 5,
            "Extreme": 6,
            "Extreme +": 7,
            "Ultra Extreme": 8
        }
        new_value = mapping.get(fps_option, 4)
        for prop in ["FPSLevel", "BattleFPS", "LobbyFPS"]:
            self.update_property(prop, new_value)

    def update_graphics_settings(self):
        """تحديث إعدادات الرسومات"""
        graphics_option = self.current_settings.get("graphics")
        mapping = {
            "Smooth": 1,
            "Balanced": 2,
            "HD": 3,
            "HDR": 4,
            "Ultra HD": 5,
            "Super Smooth": 6
        }
        new_value = mapping.get(graphics_option, 2)
        for prop in ["LobbyRenderQuality", "BattleRenderQuality"]:
            self.update_property(prop, new_value)

    def update_art_quality_settings(self):
        """تحديث إعدادات جودة الفن"""
        style_option = self.current_settings.get("style")
        mapping = {
            "Classic": 1,
            "Colorful": 2,
            "Realistic": 3,
            "Soft": 4,
            "Movie": 6
        }
        new_value = mapping.get(style_option, 1)
        for prop in ["BattleRenderStyle", "LobbyRenderStyle"]:
            self.update_property(prop, new_value)

    def update_shadow_settings(self, file_path):
        """تحديث إعدادات الظلال"""
        with open(file_path, "r", encoding="utf-8") as f:
            lines = f.readlines()
        selected = self.current_settings.get("shadow")
        new_value = "48" if selected == "Enable" else "49"
        for i, line in enumerate(lines):
            if line.startswith("+CVars=0B572A11181D160E280C1815100D0044"):
                lines[i] = f"+CVars=0B572A11181D160E280C1815100D0044{new_value}\n"
            elif line.startswith("+CVars=0B572C0A1C0B2A11181D160E2A0E100D1A1144"):
                lines[i] = f"+CVars=0B572C0A1C0B2A11181D160E2A0E100D1A1144{new_value}\n"
        with open(file_path, "w", encoding="utf-8") as f:
            f.writelines(lines)

    def update_sfx_settings(self, file_path):
        """تحديث إعدادات الصوت"""
        with open(file_path, "r", encoding="utf-8") as f:
            lines = f.readlines()
        selected = self.current_settings.get("sfx")
        mapping = {"Low": "0", "High": "1", "Ultra": "2"}
        new_val = mapping.get(selected, "0")
        for i, line in enumerate(lines):
            if line.startswith("+CVars=SoundQualityType="):
                lines[i] = f"+CVars=SoundQualityType={new_val}\n"
        with open(file_path, "w", encoding="utf-8") as f:
            f.writelines(lines)

    def update_property(self, property_name, new_value):
        """تحديث خاصية في ملف Active.sav"""
        search_bytes = (property_name + "\0\f\0\0\0IntProperty\0\u0004\0\0\0\0\0\0\0\0").encode("utf-8")
        idx = self.index_of_bytes(self.activeSavContent, search_bytes)
        if idx != -1:
            pos = idx + len(search_bytes)
            self.activeSavContent[pos] = new_value
        else:
            print(f"Property {property_name} not found.")




if __name__ == "__main__":
    app = QApplication(sys.argv)
    gfx_tool = GFX()
    gfx_tool.show()
    sys.exit(app.exec_())
