#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📈 مدير المراقبة الاحترافي - Professional Monitoring Manager
مسؤول عن مراقبة الأداء والنظام في الوقت الفعلي
"""

import time
import threading
import psutil
import requests
from datetime import datetime

class MonitoringManager:
    """مدير المراقبة الاحترافي"""
    
    def __init__(self, parent):
        """تهيئة مدير المراقبة"""
        self.parent = parent
        self.monitoring_active = False
        self.monitoring_thread = None
        self.monitoring_interval = 5  # ثواني
        
        self.parent.logger.info("📈 تم تهيئة مدير المراقبة الاحترافي")
        
    def start_monitoring(self):
        """بدء المراقبة"""
        if not self.monitoring_active:
            self.monitoring_active = True
            self.monitoring_thread = threading.Thread(target=self.monitoring_loop, daemon=True)
            self.monitoring_thread.start()
            self.parent.logger.info("▶️ تم بدء المراقبة")
            
    def stop_monitoring(self):
        """إيقاف المراقبة"""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        self.parent.logger.info("⏹️ تم إيقاف المراقبة")
        
    def monitoring_loop(self):
        """حلقة المراقبة الرئيسية"""
        while self.monitoring_active:
            try:
                # جمع بيانات الأداء
                performance_data = self.collect_performance_data()
                
                # حفظ البيانات في قاعدة البيانات
                self.save_performance_data(performance_data)
                
                # فحص التنبيهات
                self.check_alerts(performance_data)
                
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                self.parent.logger.error(f"❌ خطأ في حلقة المراقبة: {str(e)}")
                time.sleep(self.monitoring_interval)
                
    def collect_performance_data(self):
        """جمع بيانات الأداء"""
        try:
            data = {
                'timestamp': datetime.now(),
                'cpu_usage': psutil.cpu_percent(interval=1),
                'memory_usage': psutil.virtual_memory().percent,
                'disk_usage': psutil.disk_usage('/').percent,
                'network_latency': self.measure_network_latency(),
                'server_status': self.check_server_status(),
                'gfx_status': self.check_gfx_status(),
                'connected_devices': 0,  # سيتم تحديثها لاحقاً
                'active_processes': len(psutil.pids())
            }
            
            return data
            
        except Exception as e:
            self.parent.logger.error(f"❌ فشل جمع بيانات الأداء: {str(e)}")
            return None
            
    def measure_network_latency(self):
        """قياس زمن استجابة الشبكة"""
        try:
            start_time = time.time()
            response = requests.get(self.parent.connection_config['server_url'], timeout=5)
            end_time = time.time()
            
            if response.status_code == 200:
                return (end_time - start_time) * 1000  # بالميلي ثانية
            else:
                return -1
                
        except Exception:
            return -1
            
    def check_server_status(self):
        """فحص حالة السيرفر"""
        try:
            response = requests.get(self.parent.connection_config['server_url'], timeout=5)
            return "online" if response.status_code == 200 else "offline"
        except Exception:
            return "offline"
            
    def check_gfx_status(self):
        """فحص حالة GFX"""
        # سيتم تنفيذها لاحقاً
        return "unknown"
        
    def save_performance_data(self, data):
        """حفظ بيانات الأداء"""
        if data:
            try:
                self.parent.db_manager.record_performance_metric(
                    cpu_usage=data['cpu_usage'],
                    memory_usage=data['memory_usage'],
                    disk_usage=data['disk_usage'],
                    network_latency=data['network_latency'],
                    server_status=data['server_status'],
                    gfx_status=data['gfx_status'],
                    connected_devices=data['connected_devices'],
                    active_processes=data['active_processes']
                )
            except Exception as e:
                self.parent.logger.error(f"❌ فشل حفظ بيانات الأداء: {str(e)}")
                
    def check_alerts(self, data):
        """فحص التنبيهات"""
        if not data:
            return
            
        # تنبيهات استخدام المعالج
        if data['cpu_usage'] > 90:
            self.send_alert("تحذير", f"استخدام المعالج مرتفع: {data['cpu_usage']:.1f}%", "warning")
            
        # تنبيهات استخدام الذاكرة
        if data['memory_usage'] > 90:
            self.send_alert("تحذير", f"استخدام الذاكرة مرتفع: {data['memory_usage']:.1f}%", "warning")
            
        # تنبيهات حالة السيرفر
        if data['server_status'] == "offline":
            self.send_alert("خطأ", "السيرفر غير متاح", "error")
            
    def send_alert(self, title, message, alert_type):
        """إرسال تنبيه"""
        try:
            # إرسال إشعار عبر مدير الإشعارات
            if hasattr(self.parent, 'notification_manager'):
                self.parent.notification_manager.send_notification(title, message, alert_type)
                
            # تسجيل التنبيه
            self.parent.logger.warning(f"⚠️ تنبيه: {title} - {message}")
            
        except Exception as e:
            self.parent.logger.error(f"❌ فشل إرسال التنبيه: {str(e)}")
            
    def get_performance_summary(self, hours=24):
        """الحصول على ملخص الأداء"""
        try:
            data = self.parent.db_manager.get_performance_data(hours)
            
            if not data:
                return None
                
            # حساب الإحصائيات
            cpu_values = [row[2] for row in data if row[2] is not None]
            memory_values = [row[3] for row in data if row[3] is not None]
            
            summary = {
                'period_hours': hours,
                'total_records': len(data),
                'cpu_avg': sum(cpu_values) / len(cpu_values) if cpu_values else 0,
                'cpu_max': max(cpu_values) if cpu_values else 0,
                'memory_avg': sum(memory_values) / len(memory_values) if memory_values else 0,
                'memory_max': max(memory_values) if memory_values else 0,
                'last_update': data[0][1] if data else None
            }
            
            return summary
            
        except Exception as e:
            self.parent.logger.error(f"❌ فشل الحصول على ملخص الأداء: {str(e)}")
            return None
