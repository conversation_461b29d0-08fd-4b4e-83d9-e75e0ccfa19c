const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const config = require('../config/config');

/**
 * تشفير كلمة المرور
 * @param {string} password - كلمة المرور النصية
 * @returns {Promise<string>} - كلمة المرور المشفرة
 */
exports.hashPassword = async (password) => {
  return await bcrypt.hash(password, config.security.bcryptSaltRounds);
};

/**
 * مقارنة كلمة المرور بالهاش المخزن
 * @param {string} password - كلمة المرور النصية
 * @param {string} hashedPassword - كلمة المرور المشفرة المخزنة
 * @returns {Promise<boolean>} - نتيجة المقارنة
 */
exports.comparePassword = async (password, hashedPassword) => {
  return await bcrypt.compare(password, hashedPassword);
};

/**
 * إنشاء توكن JWT
 * @param {Object} payload - البيانات المراد تضمينها في التوكن
 * @returns {string} - توكن JWT
 */
exports.generateToken = (payload) => {
  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.expiresIn
  });
};

/**
 * تشفير البيانات باستخدام AES-256-GCM
 * @param {string} text - النص المراد تشفيره
 * @param {string} secretKey - المفتاح السري (يجب أن يكون 32 بايت)
 * @returns {string} - النص المشفر بتنسيق hex
 */
exports.encrypt = (text, secretKey) => {
  // إنشاء متجه التهيئة (IV)
  const iv = crypto.randomBytes(16);
  
  // إنشاء مشفر
  const cipher = crypto.createCipheriv(
    'aes-256-gcm', 
    Buffer.from(secretKey, 'hex'), 
    iv
  );
  
  // تشفير النص
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  // الحصول على علامة المصادقة
  const authTag = cipher.getAuthTag().toString('hex');
  
  // دمج IV والنص المشفر وعلامة المصادقة
  return iv.toString('hex') + ':' + encrypted + ':' + authTag;
};

/**
 * فك تشفير البيانات المشفرة باستخدام AES-256-GCM
 * @param {string} encryptedText - النص المشفر بتنسيق hex
 * @param {string} secretKey - المفتاح السري (يجب أن يكون 32 بايت)
 * @returns {string} - النص الأصلي
 */
exports.decrypt = (encryptedText, secretKey) => {
  // تقسيم النص المشفر إلى IV والنص المشفر وعلامة المصادقة
  const parts = encryptedText.split(':');
  const iv = Buffer.from(parts[0], 'hex');
  const encrypted = parts[1];
  const authTag = Buffer.from(parts[2], 'hex');
  
  // إنشاء مفكك التشفير
  const decipher = crypto.createDecipheriv(
    'aes-256-gcm', 
    Buffer.from(secretKey, 'hex'), 
    iv
  );
  
  // تعيين علامة المصادقة
  decipher.setAuthTag(authTag);
  
  // فك تشفير النص
  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  
  return decrypted;
};

/**
 * إنشاء مفتاح تشفير عشوائي
 * @param {number} bytes - عدد البايتات (الافتراضي 32 للتشفير AES-256)
 * @returns {string} - مفتاح تشفير بتنسيق hex
 */
exports.generateEncryptionKey = (bytes = 32) => {
  return crypto.randomBytes(bytes).toString('hex');
};
