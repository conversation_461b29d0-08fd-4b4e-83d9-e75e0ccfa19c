const express = require('express');
const { authenticate, authorize } = require('../middleware/auth');
const logger = require('../utils/logger');
const router = express.Router();

/**
 * مسار اختبار عام
 * GET /api/public
 */
router.get('/public', (req, res) => {
  res.json({
    success: true,
    message: 'هذا مسار عام متاح للجميع',
    timestamp: new Date()
  });
});

/**
 * مسار محمي يتطلب المصادقة
 * GET /api/protected
 */
router.get('/protected', authenticate, (req, res) => {
  logger.info(`وصول مستخدم مصرح به: ${req.user.username}`);
  
  res.json({
    success: true,
    message: 'أنت مصرح لك بالوصول إلى هذا المسار',
    user: req.user,
    timestamp: new Date()
  });
});

/**
 * مسار للمسؤولين فقط
 * GET /api/admin
 */
router.get('/admin', authenticate, authorize('admin'), (req, res) => {
  logger.info(`وصول مسؤول: ${req.user.username}`);
  
  res.json({
    success: true,
    message: 'أنت مصرح لك بالوصول إلى لوحة تحكم المسؤول',
    user: req.user,
    timestamp: new Date()
  });
});

/**
 * مسار لاختبار الأمان
 * GET /api/security-test
 */
router.get('/security-test', (req, res) => {
  // إظهار معلومات الأمان
  const securityInfo = {
    headers: req.headers,
    ip: req.ip,
    secure: req.secure,
    protocol: req.protocol,
    method: req.method,
    path: req.path
  };
  
  res.json({
    success: true,
    message: 'معلومات الأمان',
    securityInfo
  });
});

module.exports = router;
