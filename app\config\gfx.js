/**
 * إعدادات GFX - PUBG Mobile Graphics Optimizer
 */

module.exports = {
  // مسارات ADB
  adb: {
    path: process.env.ADB_PATH || './platform-tools/adb.exe',
    timeout: parseInt(process.env.ADB_TIMEOUT) || 30000, // 30 ثانية
    retryAttempts: parseInt(process.env.ADB_RETRY_ATTEMPTS) || 3,
    retryDelay: parseInt(process.env.ADB_RETRY_DELAY) || 2000 // 2 ثانية
  },

  // مسارات PUBG Mobile
  pubg: {
    packageName: 'com.tencent.ig',
    activeSavPath: '/sdcard/Android/data/com.tencent.ig/files/UE4Game/ShadowTrackerExtra/ShadowTrackerExtra/Saved/SaveGames/Active.sav',
    userSettingsPath: '/sdcard/Android/data/com.tencent.ig/files/UE4Game/ShadowTrackerExtra/ShadowTrackerExtra/Saved/Config/Android/UserSettings.ini',
    
    // مسارات بديلة للإصدارات المختلفة
    alternativePaths: {
      global: {
        packageName: 'com.tencent.ig',
        activeSav: '/sdcard/Android/data/com.tencent.ig/files/UE4Game/ShadowTrackerExtra/ShadowTrackerExtra/Saved/SaveGames/Active.sav'
      },
      korean: {
        packageName: 'com.pubg.krmobile',
        activeSav: '/sdcard/Android/data/com.pubg.krmobile/files/UE4Game/ShadowTrackerExtra/ShadowTrackerExtra/Saved/SaveGames/Active.sav'
      },
      chinese: {
        packageName: 'com.tencent.tmgp.pubgmhd',
        activeSav: '/sdcard/Android/data/com.tencent.tmgp.pubgmhd/files/UE4Game/ShadowTrackerExtra/ShadowTrackerExtra/Saved/SaveGames/Active.sav'
      }
    }
  },

  // خريطة إعدادات الرسومات
  graphics: {
    mapping: {
      'Super Smooth': 6,
      'Smooth': 1,
      'Balanced': 2,
      'HD': 3,
      'HDR': 4,
      'Ultra HD': 5
    },
    properties: ['LobbyRenderQuality', 'BattleRenderQuality'],
    default: 'Balanced'
  },

  // خريطة إعدادات معدل الإطارات
  framerate: {
    mapping: {
      'Low': 1,
      'Medium': 2,
      'High': 3,
      'Ultra': 4
    },
    properties: ['LobbyFrameRate', 'BattleFrameRate'],
    default: 'Medium'
  },

  // خريطة إعدادات النمط
  style: {
    mapping: {
      'Classic': 1,
      'Colorful': 2,
      'Realistic': 3,
      'Soft': 4,
      'Movie': 6
    },
    properties: ['LobbyRenderStyle', 'BattleRenderStyle'],
    default: 'Classic'
  },

  // إعدادات الصوت
  sfx: {
    mapping: {
      'Low': 1,
      'Medium': 2,
      'High': 3,
      'Ultra': 4
    },
    properties: ['SFXQuality', 'AudioQuality'],
    default: 'Medium'
  },

  // إعدادات الظلال
  shadow: {
    mapping: {
      'Disable': 0,
      'Enable': 1
    },
    properties: ['ShadowQuality', 'DynamicShadow'],
    default: 'Enable'
  },

  // إعدادات متقدمة
  advanced: {
    // خصائص إضافية يمكن تعديلها
    additionalProperties: {
      'AntiAliasing': {
        'Off': 0,
        'Low': 1,
        'Medium': 2,
        'High': 3
      },
      'TextureQuality': {
        'Low': 1,
        'Medium': 2,
        'High': 3,
        'Ultra': 4
      },
      'ViewDistance': {
        'Low': 1,
        'Medium': 2,
        'High': 3,
        'Ultra': 4
      },
      'EffectsQuality': {
        'Low': 1,
        'Medium': 2,
        'High': 3,
        'Ultra': 4
      }
    },

    // إعدادات مخصصة للمحاكيات المختلفة
    emulatorOptimizations: {
      'BlueStacks': {
        graphics: 'Balanced',
        framerate: 'Medium',
        style: 'Classic'
      },
      'NoxPlayer': {
        graphics: 'Smooth',
        framerate: 'Medium',
        style: 'Classic'
      },
      'LDPlayer': {
        graphics: 'Balanced',
        framerate: 'High',
        style: 'Colorful'
      },
      'MEmu': {
        graphics: 'Smooth',
        framerate: 'Medium',
        style: 'Classic'
      }
    }
  },

  // إعدادات الأمان والتحقق
  security: {
    // التحقق من وجود PUBG Mobile قبل التطبيق
    verifyPubgInstalled: true,
    
    // إنشاء نسخة احتياطية قبل التعديل
    createBackup: true,
    
    // مجلد النسخ الاحتياطية
    backupDirectory: './backups/gfx',
    
    // الحد الأقصى لعدد النسخ الاحتياطية
    maxBackups: 10,
    
    // التحقق من سلامة الملفات بعد التعديل
    verifyFileIntegrity: true
  },

  // إعدادات السجلات
  logging: {
    // تسجيل عمليات ADB
    logAdbCommands: true,
    
    // تسجيل تغييرات الملفات
    logFileChanges: true,
    
    // مستوى التفاصيل
    verboseLevel: process.env.GFX_VERBOSE_LEVEL || 'info', // debug, info, warn, error
    
    // حفظ سجلات GFX في ملف منفصل
    separateLogFile: true,
    logFileName: 'gfx.log'
  },

  // إعدادات الأداء
  performance: {
    // مهلة انتظار عمليات الملفات
    fileOperationTimeout: 10000, // 10 ثواني
    
    // حجم buffer للقراءة/الكتابة
    bufferSize: 64 * 1024, // 64KB
    
    // تمكين التخزين المؤقت للإعدادات
    enableSettingsCache: true,
    
    // مدة التخزين المؤقت (بالمللي ثانية)
    cacheTimeout: 5 * 60 * 1000, // 5 دقائق
    
    // عدد المحاولات المتزامنة القصوى
    maxConcurrentOperations: 3
  },

  // رسائل المستخدم
  messages: {
    ar: {
      connecting: 'جاري الاتصال بالجهاز...',
      connected: 'تم الاتصال بنجاح',
      disconnected: 'تم قطع الاتصال',
      readingSettings: 'جاري قراءة الإعدادات...',
      applyingSettings: 'جاري تطبيق الإعدادات...',
      settingsApplied: 'تم تطبيق الإعدادات بنجاح',
      launchingPubg: 'جاري تشغيل PUBG Mobile...',
      pubgLaunched: 'تم تشغيل PUBG Mobile بنجاح',
      error: 'حدث خطأ',
      deviceNotFound: 'لم يتم العثور على الجهاز',
      pubgNotInstalled: 'PUBG Mobile غير مثبت على الجهاز'
    },
    en: {
      connecting: 'Connecting to device...',
      connected: 'Connected successfully',
      disconnected: 'Disconnected',
      readingSettings: 'Reading settings...',
      applyingSettings: 'Applying settings...',
      settingsApplied: 'Settings applied successfully',
      launchingPubg: 'Launching PUBG Mobile...',
      pubgLaunched: 'PUBG Mobile launched successfully',
      error: 'An error occurred',
      deviceNotFound: 'Device not found',
      pubgNotInstalled: 'PUBG Mobile is not installed on the device'
    }
  },

  // إعدادات التطوير والتجربة
  development: {
    // تمكين وضع المحاكاة (بدون ADB حقيقي)
    simulationMode: process.env.GFX_SIMULATION_MODE === 'true',
    
    // تمكين التجربة الآمنة (بدون تعديل ملفات حقيقية)
    safeMode: process.env.GFX_SAFE_MODE === 'true',
    
    // إظهار معلومات تشخيصية إضافية
    enableDiagnostics: process.env.NODE_ENV === 'development',
    
    // تمكين API endpoints للتجربة
    enableTestEndpoints: process.env.NODE_ENV === 'development'
  }
};
