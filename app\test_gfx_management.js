/**
 * ملف اختبار إدارة برنامج GFX
 * يختبر تشغيل وإيقاف وإدارة برنامج GFX كأداة في السيرفر
 */

const axios = require('axios');

// إعدادات الاختبار
const BASE_URL = 'http://localhost:3000';
const TEST_USER = {
  email: '<EMAIL>',
  password: 'admin123'
};

let authToken = null;

/**
 * تسجيل الدخول والحصول على التوكن
 */
async function login() {
  try {
    console.log('🔐 تسجيل الدخول...');
    
    const response = await axios.post(`${BASE_URL}/api/auth/login`, TEST_USER);
    
    if (response.data.success) {
      authToken = response.data.token;
      console.log('✅ تم تسجيل الدخول بنجاح');
      return true;
    } else {
      console.error('❌ فشل تسجيل الدخول:', response.data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في تسجيل الدخول:', error.message);
    return false;
  }
}

/**
 * إنشاء headers مع التوكن
 */
function getHeaders() {
  return {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };
}

/**
 * اختبار حالة برنامج GFX
 */
async function testGFXStatus() {
  try {
    console.log('\n📊 اختبار حالة برنامج GFX...');
    
    const response = await axios.get(`${BASE_URL}/api/tool/gfx/status`, {
      headers: getHeaders()
    });
    
    console.log('✅ حالة GFX:', response.data.status);
    console.log('🆔 معرف العملية:', response.data.processId || 'غير شغال');
    console.log('🔄 شغال:', response.data.isRunning ? 'نعم' : 'لا');
    
    return response.data;
    
  } catch (error) {
    console.error('❌ خطأ في حالة GFX:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * اختبار تشغيل برنامج GFX
 */
async function testStartGFX() {
  try {
    console.log('\n🚀 اختبار تشغيل برنامج GFX...');
    
    const response = await axios.post(`${BASE_URL}/api/tool/gfx/start`, {}, {
      headers: getHeaders()
    });
    
    if (response.data.success) {
      console.log('✅ تم تشغيل GFX بنجاح');
      console.log('🆔 معرف العملية:', response.data.processId);
      console.log('📊 الحالة:', response.data.status);
      return response.data;
    } else {
      console.log('❌ فشل تشغيل GFX:', response.data.message);
      return null;
    }
    
  } catch (error) {
    console.error('❌ خطأ في تشغيل GFX:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * اختبار إيقاف برنامج GFX
 */
async function testStopGFX() {
  try {
    console.log('\n🛑 اختبار إيقاف برنامج GFX...');
    
    const response = await axios.post(`${BASE_URL}/api/tool/gfx/stop`, {}, {
      headers: getHeaders()
    });
    
    if (response.data.success) {
      console.log('✅ تم إيقاف GFX بنجاح');
      console.log('📊 الحالة:', response.data.status);
      return response.data;
    } else {
      console.log('❌ فشل إيقاف GFX:', response.data.message);
      return null;
    }
    
  } catch (error) {
    console.error('❌ خطأ في إيقاف GFX:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * اختبار إعادة تشغيل برنامج GFX
 */
async function testRestartGFX() {
  try {
    console.log('\n🔄 اختبار إعادة تشغيل برنامج GFX...');
    
    const response = await axios.post(`${BASE_URL}/api/tool/gfx/restart`, {}, {
      headers: getHeaders()
    });
    
    if (response.data.success) {
      console.log('✅ تم إعادة تشغيل GFX بنجاح');
      console.log('🆔 معرف العملية الجديد:', response.data.processId);
      console.log('📊 الحالة:', response.data.status);
      return response.data;
    } else {
      console.log('❌ فشل إعادة تشغيل GFX:', response.data.message);
      return null;
    }
    
  } catch (error) {
    console.error('❌ خطأ في إعادة تشغيل GFX:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * اختبار تحديث ملفات GFX
 */
async function testUpdateGFXFiles() {
  try {
    console.log('\n📝 اختبار تحديث ملفات GFX...');
    
    // محاولة تحديث ملف README.md
    const newContent = `# 🎮 GFX Tool - PUBG Mobile Graphics Optimizer

## 📋 نظرة عامة
أداة تحسين رسومات PUBG Mobile المدمجة في السيرفر.

## 🕒 آخر تحديث: ${new Date().toLocaleString('ar-EG')}

## 📁 محتويات المجلد:
- \`GFX.py\` - الكود الأساسي للأداة
- \`GUI.py\` - واجهة المستخدم PyQt5
- \`JO_GAME_TOOL.exe\` - الملف التنفيذي
- \`assets/\` - الصور والأيقونات
- \`platform-tools/\` - أدوات ADB
- \`build/\` - ملفات البناء

## 🔧 إدارة الأداة:
يمكن إدارة هذه الأداة من خلال:
- تشغيل/إيقاف البرنامج
- تحديث الملفات
- مراقبة الحالة
- إدارة الإعدادات

## 🌐 API Endpoints:
- \`POST /api/tool/gfx/start\` - تشغيل الأداة
- \`POST /api/tool/gfx/stop\` - إيقاف الأداة
- \`POST /api/tool/gfx/restart\` - إعادة تشغيل الأداة
- \`GET /api/tool/gfx/status\` - حالة الأداة
- \`PUT /api/tool/gfx/files\` - تحديث الملفات

## ✅ تم اختبار النظام بنجاح!
`;
    
    const response = await axios.put(`${BASE_URL}/api/tool/gfx/files`, {
      fileId: 6, // README.md
      fileContent: newContent
    }, {
      headers: getHeaders()
    });
    
    if (response.data.success) {
      console.log('✅ تم تحديث الملف بنجاح');
      console.log('📄 الملف:', response.data.file.name);
      console.log('📦 الحجم الجديد:', response.data.file.size, 'بايت');
      console.log('💾 النسخة الاحتياطية:', response.data.backupPath);
      return response.data;
    } else {
      console.log('❌ فشل تحديث الملف:', response.data.message);
      return null;
    }
    
  } catch (error) {
    console.error('❌ خطأ في تحديث الملف:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * اختبار حالة الأداة العامة
 */
async function testToolStatus() {
  try {
    console.log('\n🔧 اختبار حالة الأداة العامة...');
    
    const response = await axios.get(`${BASE_URL}/api/tool/status`, {
      headers: getHeaders()
    });
    
    console.log('✅ اسم الأداة:', response.data.tool.name);
    console.log('📦 الإصدار:', response.data.tool.version);
    console.log('📊 الحالة:', response.data.tool.status);
    console.log('📁 عدد الملفات:', response.data.tool.filesCount);
    console.log('🎮 GFX مفعل:', response.data.tool.config.gfxEnabled ? 'نعم' : 'لا');
    
    return response.data;
    
  } catch (error) {
    console.error('❌ خطأ في حالة الأداة:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * تشغيل جميع اختبارات إدارة GFX
 */
async function runAllGFXManagementTests() {
  console.log('🧪 بدء اختبار إدارة برنامج GFX...\n');
  
  // تسجيل الدخول
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('❌ فشل تسجيل الدخول. إنهاء الاختبارات.');
    return;
  }
  
  // اختبار حالة الأداة العامة
  await testToolStatus();
  
  // اختبار حالة GFX
  await testGFXStatus();
  
  // اختبار تشغيل GFX
  const startResult = await testStartGFX();
  
  if (startResult && startResult.success) {
    // انتظار قليل للتأكد من التشغيل
    console.log('\n⏳ انتظار 3 ثواني...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // اختبار حالة GFX بعد التشغيل
    await testGFXStatus();
    
    // اختبار إيقاف GFX
    await testStopGFX();
    
    // انتظار قليل للتأكد من الإيقاف
    console.log('\n⏳ انتظار ثانيتين...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // اختبار حالة GFX بعد الإيقاف
    await testGFXStatus();
    
    // اختبار إعادة التشغيل
    await testRestartGFX();
    
    // انتظار قليل
    console.log('\n⏳ انتظار 3 ثواني...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // إيقاف نهائي
    await testStopGFX();
  }
  
  // اختبار تحديث الملفات
  await testUpdateGFXFiles();
  
  // اختبار حالة الأداة النهائية
  await testToolStatus();
  
  console.log('\n🎉 انتهت جميع اختبارات إدارة GFX!');
  console.log('\n📋 ملخص الاختبارات:');
  console.log('✅ تسجيل الدخول');
  console.log('✅ حالة الأداة');
  console.log('✅ تشغيل GFX');
  console.log('✅ إيقاف GFX');
  console.log('✅ إعادة تشغيل GFX');
  console.log('✅ تحديث الملفات');
  console.log('\n🎮 برنامج GFX جاهز للاستخدام من خلال السيرفر!');
}

// تشغيل الاختبارات إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  runAllGFXManagementTests().catch(console.error);
}

module.exports = {
  login,
  testGFXStatus,
  testStartGFX,
  testStopGFX,
  testRestartGFX,
  testUpdateGFXFiles,
  testToolStatus,
  runAllGFXManagementTests
};
