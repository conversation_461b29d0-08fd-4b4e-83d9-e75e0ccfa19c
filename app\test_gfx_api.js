/**
 * ملف اختبار API الخاص بـ GFX
 * يمكن تشغيله لاختبار جميع endpoints الجديدة
 */

const axios = require('axios');

// إعدادات الاختبار
const BASE_URL = 'http://localhost:3000';
const TEST_USER = {
  email: '<EMAIL>',
  password: 'admin123'
};

let authToken = null;

/**
 * تسجيل الدخول والحصول على التوكن
 */
async function login() {
  try {
    console.log('🔐 تسجيل الدخول...');
    
    const response = await axios.post(`${BASE_URL}/api/auth/login`, TEST_USER);
    
    if (response.data.success) {
      authToken = response.data.token;
      console.log('✅ تم تسجيل الدخول بنجاح');
      return true;
    } else {
      console.error('❌ فشل تسجيل الدخول:', response.data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في تسجيل الدخول:', error.message);
    return false;
  }
}

/**
 * إنشاء headers مع التوكن
 */
function getHeaders() {
  return {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };
}

/**
 * اختبار حالة الأداة
 */
async function testToolStatus() {
  try {
    console.log('\n📊 اختبار حالة الأداة...');
    
    const response = await axios.get(`${BASE_URL}/api/tool/status`, {
      headers: getHeaders()
    });
    
    console.log('✅ حالة الأداة:', response.data.tool.name);
    console.log('📦 عدد الملفات:', response.data.tool.filesCount);
    console.log('🔧 إعدادات GFX:', response.data.tool.gfxSettings);
    
  } catch (error) {
    console.error('❌ خطأ في حالة الأداة:', error.response?.data?.message || error.message);
  }
}

/**
 * اختبار قائمة الأجهزة المتصلة
 */
async function testGetDevices() {
  try {
    console.log('\n📱 اختبار قائمة الأجهزة...');
    
    const response = await axios.get(`${BASE_URL}/api/tool/adb/devices`, {
      headers: getHeaders()
    });
    
    console.log('✅ عدد الأجهزة المتصلة:', response.data.count);
    console.log('📋 قائمة الأجهزة:', response.data.devices);
    
    return response.data.devices;
    
  } catch (error) {
    console.error('❌ خطأ في قائمة الأجهزة:', error.response?.data?.message || error.message);
    return [];
  }
}

/**
 * اختبار الاتصال بـ ADB
 */
async function testConnectADB(deviceId = null) {
  try {
    console.log('\n🔌 اختبار الاتصال بـ ADB...');
    
    const requestData = deviceId ? { deviceId } : {};
    
    const response = await axios.post(`${BASE_URL}/api/tool/adb/connect`, requestData, {
      headers: getHeaders()
    });
    
    if (response.data.success) {
      console.log('✅ تم الاتصال بالجهاز:', response.data.device?.id);
      return response.data.device;
    } else {
      console.log('❌ فشل الاتصال:', response.data.message);
      return null;
    }
    
  } catch (error) {
    console.error('❌ خطأ في الاتصال:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * اختبار حالة الاتصال
 */
async function testADBStatus() {
  try {
    console.log('\n📡 اختبار حالة الاتصال...');
    
    const response = await axios.get(`${BASE_URL}/api/tool/adb/status`, {
      headers: getHeaders()
    });
    
    console.log('✅ حالة الاتصال:', response.data.adbConnection.isConnected ? 'متصل' : 'غير متصل');
    console.log('🆔 معرف الجهاز:', response.data.adbConnection.deviceId);
    
    return response.data.adbConnection;
    
  } catch (error) {
    console.error('❌ خطأ في حالة الاتصال:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * اختبار الخيارات المتاحة
 */
async function testGraphicsOptions() {
  try {
    console.log('\n🎨 اختبار الخيارات المتاحة...');
    
    const response = await axios.get(`${BASE_URL}/api/tool/graphics/options`, {
      headers: getHeaders()
    });
    
    console.log('✅ خيارات الرسومات:', response.data.options.graphics);
    console.log('🎬 خيارات معدل الإطارات:', response.data.options.framerate);
    console.log('🎭 خيارات النمط:', response.data.options.style);
    
    return response.data.options;
    
  } catch (error) {
    console.error('❌ خطأ في الخيارات:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * اختبار قراءة إعدادات الرسومات
 */
async function testGetGraphicsSettings() {
  try {
    console.log('\n📖 اختبار قراءة إعدادات الرسومات...');
    
    const response = await axios.get(`${BASE_URL}/api/tool/graphics/settings`, {
      headers: getHeaders()
    });
    
    if (response.data.success) {
      console.log('✅ الإعدادات الحالية:', response.data.settings);
      return response.data.settings;
    } else {
      console.log('❌ فشل قراءة الإعدادات:', response.data.message);
      return null;
    }
    
  } catch (error) {
    console.error('❌ خطأ في قراءة الإعدادات:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * اختبار تحديث إعدادات الرسومات
 */
async function testUpdateGraphicsSettings() {
  try {
    console.log('\n✏️ اختبار تحديث إعدادات الرسومات...');
    
    const newSettings = {
      graphics: 'Ultra HD',
      framerate: 'Ultra',
      style: 'Realistic',
      shadow: 'Enable'
    };
    
    const response = await axios.put(`${BASE_URL}/api/tool/graphics/settings`, newSettings, {
      headers: getHeaders()
    });
    
    if (response.data.success) {
      console.log('✅ تم تحديث الإعدادات:', response.data.settings);
      return response.data.settings;
    } else {
      console.log('❌ فشل تحديث الإعدادات:', response.data.message);
      return null;
    }
    
  } catch (error) {
    console.error('❌ خطأ في تحديث الإعدادات:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * اختبار تطبيق الإعدادات
 */
async function testApplyGraphicsSettings() {
  try {
    console.log('\n🚀 اختبار تطبيق الإعدادات...');
    
    const response = await axios.post(`${BASE_URL}/api/tool/graphics/apply`, {}, {
      headers: getHeaders()
    });
    
    if (response.data.success) {
      console.log('✅ تم تطبيق الإعدادات بنجاح');
      return true;
    } else {
      console.log('❌ فشل تطبيق الإعدادات:', response.data.message);
      return false;
    }
    
  } catch (error) {
    console.error('❌ خطأ في تطبيق الإعدادات:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * اختبار تشغيل PUBG Mobile
 */
async function testLaunchPUBG() {
  try {
    console.log('\n🎮 اختبار تشغيل PUBG Mobile...');
    
    const response = await axios.post(`${BASE_URL}/api/tool/pubg/launch`, {}, {
      headers: getHeaders()
    });
    
    if (response.data.success) {
      console.log('✅ تم تشغيل PUBG Mobile بنجاح');
      return true;
    } else {
      console.log('❌ فشل تشغيل PUBG Mobile:', response.data.message);
      return false;
    }
    
  } catch (error) {
    console.error('❌ خطأ في تشغيل PUBG Mobile:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * تشغيل جميع الاختبارات
 */
async function runAllTests() {
  console.log('🧪 بدء اختبار GFX API...\n');
  
  // تسجيل الدخول
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('❌ فشل تسجيل الدخول. إنهاء الاختبارات.');
    return;
  }
  
  // اختبار حالة الأداة
  await testToolStatus();
  
  // اختبار قائمة الأجهزة
  const devices = await testGetDevices();
  
  // اختبار الاتصال بـ ADB
  let connectedDevice = null;
  if (devices.length > 0) {
    connectedDevice = await testConnectADB(devices[0].id);
  } else {
    connectedDevice = await testConnectADB(); // محاولة الاتصال بأي جهاز
  }
  
  // اختبار حالة الاتصال
  await testADBStatus();
  
  // اختبار الخيارات المتاحة
  await testGraphicsOptions();
  
  // إذا كان هناك اتصال، اختبر باقي الوظائف
  if (connectedDevice) {
    await testGetGraphicsSettings();
    await testUpdateGraphicsSettings();
    await testApplyGraphicsSettings();
    await testLaunchPUBG();
  } else {
    console.log('\n⚠️ لا يوجد اتصال بجهاز. تم تخطي اختبارات الرسومات.');
  }
  
  console.log('\n🎉 انتهت جميع الاختبارات!');
}

// تشغيل الاختبارات إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  login,
  testToolStatus,
  testGetDevices,
  testConnectADB,
  testADBStatus,
  testGraphicsOptions,
  testGetGraphicsSettings,
  testUpdateGraphicsSettings,
  testApplyGraphicsSettings,
  testLaunchPUBG,
  runAllTests
};
