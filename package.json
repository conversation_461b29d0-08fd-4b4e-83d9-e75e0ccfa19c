{"name": "secure-server", "version": "1.0.0", "description": "سيرفر آمن ومشفر يعمل 24 ساعة", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "prod": "pm2 start pm2.config.js", "monitor": "pm2 monit", "logs": "pm2 logs", "restart": "pm2 restart secure-server", "stop": "pm2 stop secure-server", "delete": "pm2 delete secure-server", "startup": "pm2 startup && pm2 save", "setup": "node setup_gfx.js", "test-gfx": "node app/test_gfx_api.js", "quick-test": "node quick_test.js"}, "dependencies": {"bcrypt": "^5.1.1", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "csurf": "^1.11.0", "dotenv": "^16.3.1", "express": "^4.21.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "pm2": "^5.3.0", "winston": "^3.11.0", "express-slow-down": "^1.6.0", "http2-express-bridge": "^1.0.7", "spdy": "^4.0.2", "redis": "^4.6.10", "express-brute": "^1.0.1", "express-brute-redis": "^0.0.1", "rate-limit-redis": "^3.0.2", "memcached": "^2.2.2", "cluster": "^0.7.7", "express-status-monitor": "^1.3.4", "ddos": "^0.2.1", "express-enforces-ssl": "^1.1.0", "hpp": "^0.2.3", "content-security-policy": "^0.3.4", "node-cache": "^5.1.2", "express-minify": "^1.0.0", "brotli": "^1.3.3", "shrink-ray-current": "^4.1.3", "i18n": "^0.15.1", "moment-timezone": "^0.5.43", "cloudflare": "^2.9.1", "aws-sdk": "^2.1502.0", "geoip-lite": "^1.4.7", "express-useragent": "^1.0.15"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}