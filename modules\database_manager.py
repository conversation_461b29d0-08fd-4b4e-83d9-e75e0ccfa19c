#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
💾 مدير قاعدة البيانات الاحترافية - Professional Database Manager
مسؤول عن إدارة قاعدة البيانات المحلية وجميع العمليات المتعلقة بها
"""

import sqlite3
import json
import hashlib
import threading
from datetime import datetime, timedelta
from pathlib import Path

class DatabaseManager:
    """مدير قاعدة البيانات الاحترافية"""
    
    def __init__(self, parent):
        """تهيئة مدير قاعدة البيانات"""
        self.parent = parent
        self.db_path = "data/server_controller_pro.db"
        self.connection = None
        self.cursor = None
        self.lock = threading.Lock()
        
        # إعداد قاعدة البيانات
        self.setup_database()
        
        self.parent.logger.info("💾 تم تهيئة مدير قاعدة البيانات")
        
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            # إنشاء مجلد البيانات
            Path("data").mkdir(exist_ok=True)
            
            # الاتصال بقاعدة البيانات
            self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
            self.cursor = self.connection.cursor()
            
            # تفعيل المفاتيح الخارجية
            self.cursor.execute("PRAGMA foreign_keys = ON")
            
            # إنشاء الجداول
            self.create_tables()
            
            # إنشاء الفهارس
            self.create_indexes()
            
            # إدراج البيانات الافتراضية
            self.insert_default_data()
            
            self.parent.logger.info("✅ تم إعداد قاعدة البيانات بنجاح")
            
        except Exception as e:
            self.parent.logger.error(f"❌ فشل إعداد قاعدة البيانات: {str(e)}")
            raise
            
    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        
        # جدول المستخدمين
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                salt TEXT NOT NULL,
                role TEXT DEFAULT 'user',
                is_active BOOLEAN DEFAULT 1,
                last_login DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الجلسات
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                session_token TEXT UNIQUE NOT NULL,
                expires_at DATETIME NOT NULL,
                ip_address TEXT,
                user_agent TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # جدول الاتصالات
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS connections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                server_url TEXT NOT NULL,
                username TEXT,
                connection_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                disconnection_time DATETIME,
                status TEXT NOT NULL,
                response_time REAL,
                error_message TEXT,
                session_duration INTEGER,
                data_transferred INTEGER DEFAULT 0
            )
        ''')
        
        # جدول بيانات الأداء
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS performance_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                cpu_usage REAL,
                memory_usage REAL,
                disk_usage REAL,
                network_latency REAL,
                server_status TEXT,
                gfx_status TEXT,
                connected_devices INTEGER DEFAULT 0,
                active_processes INTEGER DEFAULT 0
            )
        ''')
        
        # جدول السجلات
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS activity_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                user_id INTEGER,
                action TEXT NOT NULL,
                category TEXT NOT NULL,
                details TEXT,
                status TEXT NOT NULL,
                ip_address TEXT,
                user_agent TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # جدول الإعدادات
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                category TEXT NOT NULL,
                key TEXT NOT NULL,
                value TEXT,
                data_type TEXT DEFAULT 'string',
                description TEXT,
                is_encrypted BOOLEAN DEFAULT 0,
                updated_by INTEGER,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (updated_by) REFERENCES users (id),
                UNIQUE(category, key)
            )
        ''')
        
        # جدول النسخ الاحتياطية
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS backups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename TEXT NOT NULL,
                file_path TEXT NOT NULL,
                file_size INTEGER,
                backup_type TEXT NOT NULL,
                compression_type TEXT,
                checksum TEXT,
                created_by INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                restored_at DATETIME,
                is_encrypted BOOLEAN DEFAULT 0,
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        ''')
        
        # جدول الملفات المرفوعة
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS uploaded_files (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                original_filename TEXT NOT NULL,
                stored_filename TEXT NOT NULL,
                file_path TEXT NOT NULL,
                file_size INTEGER,
                file_type TEXT,
                mime_type TEXT,
                checksum TEXT,
                uploaded_by INTEGER,
                uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_accessed DATETIME,
                download_count INTEGER DEFAULT 0,
                FOREIGN KEY (uploaded_by) REFERENCES users (id)
            )
        ''')
        
        # جدول الإشعارات
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS notifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                title TEXT NOT NULL,
                message TEXT NOT NULL,
                type TEXT DEFAULT 'info',
                priority INTEGER DEFAULT 1,
                is_read BOOLEAN DEFAULT 0,
                action_url TEXT,
                expires_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                read_at DATETIME,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # جدول التقارير
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                report_type TEXT NOT NULL,
                parameters TEXT,
                generated_by INTEGER,
                generated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                file_path TEXT,
                file_size INTEGER,
                format TEXT DEFAULT 'json',
                FOREIGN KEY (generated_by) REFERENCES users (id)
            )
        ''')
        
        # حفظ التغييرات
        self.connection.commit()
        
    def create_indexes(self):
        """إنشاء الفهارس لتحسين الأداء"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
            "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
            "CREATE INDEX IF NOT EXISTS idx_sessions_token ON sessions(session_token)",
            "CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_connections_timestamp ON connections(connection_time)",
            "CREATE INDEX IF NOT EXISTS idx_performance_timestamp ON performance_metrics(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON activity_logs(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_logs_user_id ON activity_logs(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_logs_category ON activity_logs(category)",
            "CREATE INDEX IF NOT EXISTS idx_settings_category ON settings(category)",
            "CREATE INDEX IF NOT EXISTS idx_backups_created_at ON backups(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_files_uploaded_at ON uploaded_files(uploaded_at)",
            "CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at)"
        ]
        
        for index in indexes:
            self.cursor.execute(index)
            
        self.connection.commit()
        
    def insert_default_data(self):
        """إدراج البيانات الافتراضية"""
        # إنشاء مستخدم افتراضي
        self.create_default_admin()
        
        # إدراج الإعدادات الافتراضية
        self.insert_default_settings()
        
    def create_default_admin(self):
        """إنشاء مستخدم المدير الافتراضي"""
        # التحقق من وجود مستخدم مدير
        self.cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'admin'")
        admin_count = self.cursor.fetchone()[0]
        
        if admin_count == 0:
            # إنشاء مستخدم مدير افتراضي
            salt = self.generate_salt()
            password_hash = self.hash_password("admin123", salt)
            
            self.cursor.execute('''
                INSERT INTO users (username, email, password_hash, salt, role)
                VALUES (?, ?, ?, ?, ?)
            ''', ("admin", "<EMAIL>", password_hash, salt, "admin"))
            
            self.connection.commit()
            self.parent.logger.info("👤 تم إنشاء مستخدم المدير الافتراضي")
            
    def insert_default_settings(self):
        """إدراج الإعدادات الافتراضية"""
        default_settings = [
            ("connection", "server_url", "http://localhost:3000", "string", "عنوان السيرفر الافتراضي"),
            ("connection", "timeout", "30", "integer", "مهلة الاتصال بالثواني"),
            ("connection", "retry_attempts", "3", "integer", "عدد محاولات إعادة الاتصال"),
            ("security", "session_timeout", "3600", "integer", "مهلة انتهاء الجلسة بالثواني"),
            ("security", "max_login_attempts", "3", "integer", "الحد الأقصى لمحاولات تسجيل الدخول"),
            ("ui", "theme", "professional_dark", "string", "سمة الواجهة"),
            ("ui", "language", "ar", "string", "لغة الواجهة"),
            ("performance", "cache_enabled", "true", "boolean", "تفعيل التخزين المؤقت"),
            ("performance", "cache_timeout", "300", "integer", "مهلة التخزين المؤقت بالثواني"),
            ("backup", "auto_backup", "true", "boolean", "النسخ الاحتياطي التلقائي"),
            ("backup", "backup_interval", "24", "integer", "فترة النسخ الاحتياطي بالساعات"),
            ("monitoring", "auto_monitoring", "true", "boolean", "المراقبة التلقائية"),
            ("monitoring", "monitoring_interval", "5", "integer", "فترة المراقبة بالثواني")
        ]
        
        for category, key, value, data_type, description in default_settings:
            self.cursor.execute('''
                INSERT OR IGNORE INTO settings (category, key, value, data_type, description)
                VALUES (?, ?, ?, ?, ?)
            ''', (category, key, value, data_type, description))
            
        self.connection.commit()
        
    # ===== وظائف إدارة المستخدمين =====
    
    def create_user(self, username, email, password, role="user"):
        """إنشاء مستخدم جديد"""
        with self.lock:
            try:
                salt = self.generate_salt()
                password_hash = self.hash_password(password, salt)
                
                self.cursor.execute('''
                    INSERT INTO users (username, email, password_hash, salt, role)
                    VALUES (?, ?, ?, ?, ?)
                ''', (username, email, password_hash, salt, role))
                
                user_id = self.cursor.lastrowid
                self.connection.commit()
                
                self.log_activity(user_id, "user_created", "user_management", 
                                f"تم إنشاء مستخدم جديد: {username}", "success")
                
                return user_id
                
            except sqlite3.IntegrityError as e:
                if "username" in str(e):
                    raise ValueError("اسم المستخدم موجود بالفعل")
                elif "email" in str(e):
                    raise ValueError("البريد الإلكتروني موجود بالفعل")
                else:
                    raise ValueError("خطأ في إنشاء المستخدم")
                    
    def authenticate_user(self, email, password):
        """التحقق من صحة بيانات المستخدم"""
        with self.lock:
            self.cursor.execute('''
                SELECT id, username, password_hash, salt, role, is_active
                FROM users WHERE email = ?
            ''', (email,))
            
            user = self.cursor.fetchone()
            
            if user and user[5]:  # is_active
                user_id, username, stored_hash, salt, role, is_active = user
                
                # التحقق من كلمة المرور
                if self.verify_password(password, stored_hash, salt):
                    # تحديث آخر تسجيل دخول
                    self.cursor.execute('''
                        UPDATE users SET last_login = CURRENT_TIMESTAMP
                        WHERE id = ?
                    ''', (user_id,))
                    self.connection.commit()
                    
                    self.log_activity(user_id, "login_success", "authentication", 
                                    f"تسجيل دخول ناجح للمستخدم: {username}", "success")
                    
                    return {
                        'id': user_id,
                        'username': username,
                        'email': email,
                        'role': role
                    }
                else:
                    self.log_activity(None, "login_failed", "authentication", 
                                    f"محاولة تسجيل دخول فاشلة للبريد: {email}", "error")
                    
            return None
            
    def generate_salt(self):
        """توليد salt عشوائي"""
        import os
        return os.urandom(32).hex()
        
    def hash_password(self, password, salt):
        """تشفير كلمة المرور"""
        return hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000).hex()
        
    def verify_password(self, password, stored_hash, salt):
        """التحقق من كلمة المرور"""
        return self.hash_password(password, salt) == stored_hash
        
    # ===== وظائف إدارة الجلسات =====
    
    def create_session(self, user_id, ip_address=None, user_agent=None):
        """إنشاء جلسة جديدة"""
        import secrets
        
        with self.lock:
            # توليد رمز الجلسة
            session_token = secrets.token_urlsafe(32)
            
            # تحديد وقت انتهاء الجلسة
            expires_at = datetime.now() + timedelta(seconds=self.parent.security_config['session_timeout'])
            
            self.cursor.execute('''
                INSERT INTO sessions (user_id, session_token, expires_at, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?)
            ''', (user_id, session_token, expires_at, ip_address, user_agent))
            
            self.connection.commit()
            
            return session_token
            
    def validate_session(self, session_token):
        """التحقق من صحة الجلسة"""
        with self.lock:
            self.cursor.execute('''
                SELECT s.user_id, u.username, u.email, u.role
                FROM sessions s
                JOIN users u ON s.user_id = u.id
                WHERE s.session_token = ? AND s.expires_at > CURRENT_TIMESTAMP AND s.is_active = 1
            ''', (session_token,))
            
            session = self.cursor.fetchone()
            
            if session:
                return {
                    'user_id': session[0],
                    'username': session[1],
                    'email': session[2],
                    'role': session[3]
                }
                
            return None
            
    def invalidate_session(self, session_token):
        """إلغاء الجلسة"""
        with self.lock:
            self.cursor.execute('''
                UPDATE sessions SET is_active = 0 WHERE session_token = ?
            ''', (session_token,))
            
            self.connection.commit()
            
    # ===== وظائف إدارة الإعدادات =====
    
    def get_setting(self, category, key, default=None):
        """الحصول على إعداد"""
        with self.lock:
            self.cursor.execute('''
                SELECT value, data_type FROM settings WHERE category = ? AND key = ?
            ''', (category, key))
            
            result = self.cursor.fetchone()
            
            if result:
                value, data_type = result
                
                # تحويل القيمة حسب النوع
                if data_type == 'integer':
                    return int(value)
                elif data_type == 'float':
                    return float(value)
                elif data_type == 'boolean':
                    return value.lower() == 'true'
                else:
                    return value
                    
            return default
            
    def set_setting(self, category, key, value, data_type='string', description=None, user_id=None):
        """تعيين إعداد"""
        with self.lock:
            # تحويل القيمة إلى نص
            if isinstance(value, bool):
                value = 'true' if value else 'false'
                data_type = 'boolean'
            elif isinstance(value, int):
                value = str(value)
                data_type = 'integer'
            elif isinstance(value, float):
                value = str(value)
                data_type = 'float'
            else:
                value = str(value)
                
            self.cursor.execute('''
                INSERT OR REPLACE INTO settings 
                (category, key, value, data_type, description, updated_by, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            ''', (category, key, value, data_type, description, user_id))
            
            self.connection.commit()
            
    # ===== وظائف السجلات =====
    
    def log_activity(self, user_id, action, category, details, status, ip_address=None, user_agent=None):
        """تسجيل نشاط"""
        with self.lock:
            self.cursor.execute('''
                INSERT INTO activity_logs 
                (user_id, action, category, details, status, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (user_id, action, category, details, status, ip_address, user_agent))
            
            self.connection.commit()
            
    def get_recent_activities(self, limit=50, user_id=None, category=None):
        """الحصول على الأنشطة الأخيرة"""
        with self.lock:
            query = '''
                SELECT al.timestamp, u.username, al.action, al.category, al.details, al.status
                FROM activity_logs al
                LEFT JOIN users u ON al.user_id = u.id
                WHERE 1=1
            '''
            params = []
            
            if user_id:
                query += " AND al.user_id = ?"
                params.append(user_id)
                
            if category:
                query += " AND al.category = ?"
                params.append(category)
                
            query += " ORDER BY al.timestamp DESC LIMIT ?"
            params.append(limit)
            
            self.cursor.execute(query, params)
            return self.cursor.fetchall()
            
    # ===== وظائف الأداء =====
    
    def record_performance_metric(self, cpu_usage=None, memory_usage=None, disk_usage=None, 
                                 network_latency=None, server_status=None, gfx_status=None,
                                 connected_devices=0, active_processes=0):
        """تسجيل مقياس أداء"""
        with self.lock:
            self.cursor.execute('''
                INSERT INTO performance_metrics 
                (cpu_usage, memory_usage, disk_usage, network_latency, server_status, 
                 gfx_status, connected_devices, active_processes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (cpu_usage, memory_usage, disk_usage, network_latency, server_status,
                  gfx_status, connected_devices, active_processes))
            
            self.connection.commit()
            
    def get_performance_data(self, hours=24):
        """الحصول على بيانات الأداء"""
        with self.lock:
            self.cursor.execute('''
                SELECT * FROM performance_metrics 
                WHERE timestamp > datetime('now', '-{} hours')
                ORDER BY timestamp DESC
            '''.format(hours))
            
            return self.cursor.fetchall()
            
    # ===== وظائف النسخ الاحتياطية =====
    
    def record_backup(self, filename, file_path, file_size, backup_type, 
                     compression_type=None, checksum=None, user_id=None, is_encrypted=False):
        """تسجيل نسخة احتياطية"""
        with self.lock:
            self.cursor.execute('''
                INSERT INTO backups 
                (filename, file_path, file_size, backup_type, compression_type, 
                 checksum, created_by, is_encrypted)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (filename, file_path, file_size, backup_type, compression_type,
                  checksum, user_id, is_encrypted))
            
            backup_id = self.cursor.lastrowid
            self.connection.commit()
            
            return backup_id
            
    def get_backups(self, limit=50):
        """الحصول على قائمة النسخ الاحتياطية"""
        with self.lock:
            self.cursor.execute('''
                SELECT b.*, u.username
                FROM backups b
                LEFT JOIN users u ON b.created_by = u.id
                ORDER BY b.created_at DESC
                LIMIT ?
            ''', (limit,))
            
            return self.cursor.fetchall()
            
    # ===== وظائف الإغلاق =====
    
    def close(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
            self.parent.logger.info("💾 تم إغلاق اتصال قاعدة البيانات")
            
    def __del__(self):
        """مدمر الكائن"""
        self.close()
