// تحميل المكتبات الأساسية
const express = require('express');
const http = require('http');
const crypto = require('crypto');
const path = require('path');
const fs = require('fs');

// إنشاء تطبيق Express
const app = express();
const PORT = process.env.PORT || 3000;

// وسائط أساسية
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// بيانات تجريبية
const tools = [
  {
    id: 1,
    name: 'SuperTool',
    version: '1.0.0',
    status: 'stopped',
    files: [
      { id: 1, name: 'core.dll', size: '1.2MB', lastUpdated: new Date() },
      { id: 2, name: 'config.json', size: '4KB', lastUpdated: new Date() },
      { id: 3, name: 'data.bin', size: '8.5MB', lastUpdated: new Date() }
    ]
  }
];

const licenses = [
  {
    id: 'LIC-12345-ABCDE',
    key: 'abcde-12345-fghij-67890',
    userId: 1,
    type: 'premium',
    isActive: true,
    expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // بعد 30 يوم
    features: ['feature1', 'feature2', 'feature3']
  }
];

const updates = [
  {
    id: 1,
    version: '1.1.0',
    releaseNotes: 'تحسينات في الأداء والأمان، إصلاح بعض الأخطاء',
    size: '15.2MB',
    isRequired: false,
    status: 'available'
  }
];

const users = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    password: '$2b$10$X7SLlL9K9X9J9X9X9X9X9OX9X9X9X9X9X9X9X9X9X9X9X9X9X9X9X9', // "admin123"
    role: 'admin'
  },
  {
    id: 2,
    username: 'user',
    email: '<EMAIL>',
    password: '$2b$10$X7SLlL9K9X9J9X9X9X9X9OX9X9X9X9X9X9X9X9X9X9X9X9X9X9X9X9', // "user123"
    role: 'user'
  }
];

// وظائف مساعدة
function generateToken(userId, username, role) {
  // في الإنتاج، استخدم مكتبة JWT
  const payload = { userId, username, role };
  return Buffer.from(JSON.stringify(payload)).toString('base64');
}

function verifyToken(token) {
  try {
    // في الإنتاج، استخدم مكتبة JWT
    return JSON.parse(Buffer.from(token, 'base64').toString());
  } catch (error) {
    return null;
  }
}

// وسيط المصادقة
function authenticate(req, res, next) {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      message: 'غير مصرح به - يرجى تسجيل الدخول'
    });
  }

  const token = authHeader.split(' ')[1];
  const user = verifyToken(token);

  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'غير مصرح به - توكن غير صالح'
    });
  }

  req.user = user;
  next();
}

// وسيط التحقق من الصلاحيات
function authorize(role) {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'غير مصرح به - يرجى تسجيل الدخول'
      });
    }

    if (req.user.role !== role) {
      return res.status(403).json({
        success: false,
        message: 'محظور - ليس لديك صلاحية للوصول إلى هذا المورد'
      });
    }

    next();
  };
}

// وسيط تسجيل الطلبات
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// مسار الصفحة الرئيسية
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'مرحبًا بك في السيرفر الآمن والمشفر',
    version: '1.0.0',
    status: 'متصل',
    timestamp: new Date()
  });
});

// ===== قسم المصادقة =====

// تسجيل الدخول
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;

  // في الإنتاج، تحقق من كلمة المرور باستخدام bcrypt
  const user = users.find(u => u.email === email);

  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'بيانات الاعتماد غير صالحة'
    });
  }

  // إنشاء توكن
  const token = generateToken(user.id, user.username, user.role);

  res.json({
    success: true,
    message: 'تم تسجيل الدخول بنجاح',
    token,
    user: {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role
    }
  });
});

// ===== قسم الأداة =====

// الحصول على حالة الأداة
app.get('/api/tool/status', (req, res) => {
  const tool = tools[0];
  
  res.json({
    success: true,
    message: 'تم استرجاع حالة الأداة بنجاح',
    tool: {
      id: tool.id,
      name: tool.name,
      version: tool.version,
      status: tool.status,
      filesCount: tool.files.length
    }
  });
});

// الحصول على قائمة ملفات الأداة
app.get('/api/tool/files', authenticate, (req, res) => {
  const tool = tools[0];
  
  res.json({
    success: true,
    message: 'تم استرجاع قائمة ملفات الأداة بنجاح',
    files: tool.files
  });
});

// تشغيل الأداة
app.post('/api/tool/start', authenticate, (req, res) => {
  const tool = tools[0];
  tool.status = 'running';
  
  res.json({
    success: true,
    message: 'تم تشغيل الأداة بنجاح',
    tool: {
      id: tool.id,
      name: tool.name,
      version: tool.version,
      status: tool.status
    }
  });
});

// إيقاف الأداة
app.post('/api/tool/stop', authenticate, (req, res) => {
  const tool = tools[0];
  tool.status = 'stopped';
  
  res.json({
    success: true,
    message: 'تم إيقاف الأداة بنجاح',
    tool: {
      id: tool.id,
      name: tool.name,
      version: tool.version,
      status: tool.status
    }
  });
});

// تنفيذ أمر في الأداة
app.post('/api/tool/execute', authenticate, (req, res) => {
  const { command } = req.body;
  
  if (!command) {
    return res.status(400).json({
      success: false,
      message: 'الأمر مطلوب'
    });
  }
  
  res.json({
    success: true,
    message: 'تم تنفيذ الأمر بنجاح',
    result: `تم تنفيذ الأمر: ${command}`,
    timestamp: new Date()
  });
});

// ===== قسم الترخيص =====

// التحقق من حالة الترخيص
app.get('/api/license/status', authenticate, (req, res) => {
  const license = licenses.find(lic => lic.userId === req.user.userId) || licenses[0];
  
  res.json({
    success: true,
    message: 'تم التحقق من حالة الترخيص بنجاح',
    license: {
      id: license.id,
      type: license.type,
      isActive: license.isActive,
      expiresAt: license.expiresAt,
      features: license.features
    }
  });
});

// التحقق من صحة مفتاح الترخيص
app.post('/api/license/validate', (req, res) => {
  const { licenseKey } = req.body;
  
  if (!licenseKey) {
    return res.status(400).json({
      success: false,
      message: 'مفتاح الترخيص مطلوب'
    });
  }
  
  // التحقق من صحة مفتاح الترخيص (للتجربة فقط)
  const isValid = licenseKey.length >= 10;
  
  if (!isValid) {
    return res.status(400).json({
      success: false,
      message: 'مفتاح الترخيص غير صالح'
    });
  }
  
  res.json({
    success: true,
    message: 'مفتاح الترخيص صالح',
    licenseDetails: {
      type: 'premium',
      expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // بعد سنة
      features: ['feature1', 'feature2', 'feature3', 'feature4', 'feature5']
    }
  });
});

// تفعيل الترخيص
app.post('/api/license/activate', authenticate, (req, res) => {
  const { licenseKey } = req.body;
  
  if (!licenseKey) {
    return res.status(400).json({
      success: false,
      message: 'مفتاح الترخيص مطلوب'
    });
  }
  
  // إنشاء ترخيص جديد (للتجربة فقط)
  const newLicense = {
    id: 'LIC-' + Math.floor(10000 + Math.random() * 90000) + '-' + licenseKey.substring(0, 5).toUpperCase(),
    key: licenseKey,
    userId: req.user.userId,
    type: 'premium',
    isActive: true,
    expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // بعد سنة
    features: ['feature1', 'feature2', 'feature3', 'feature4', 'feature5'],
    activatedAt: new Date()
  };
  
  // إضافة الترخيص الجديد أو تحديث الترخيص الحالي
  const existingIndex = licenses.findIndex(lic => lic.userId === req.user.userId);
  if (existingIndex !== -1) {
    licenses[existingIndex] = newLicense;
  } else {
    licenses.push(newLicense);
  }
  
  res.json({
    success: true,
    message: 'تم تفعيل الترخيص بنجاح',
    license: {
      id: newLicense.id,
      type: newLicense.type,
      isActive: newLicense.isActive,
      expiresAt: newLicense.expiresAt,
      features: newLicense.features
    }
  });
});

// ===== قسم الإدارة =====

// الحصول على حالة النظام
app.get('/api/admin/status', authenticate, authorize('admin'), (req, res) => {
  const systemStatus = {
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    connections: 125, // قيمة تجريبية
    activeUsers: 45, // قيمة تجريبية
    version: '1.0.0',
    lastRestart: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000) // قبل يومين
  };
  
  res.json({
    success: true,
    message: 'تم استرجاع حالة النظام بنجاح',
    systemStatus
  });
});

// التحكم في تشغيل/إيقاف الأداة
app.post('/api/admin/tool/control', authenticate, authorize('admin'), (req, res) => {
  const { action } = req.body;
  
  if (!action || !['start', 'stop', 'restart'].includes(action)) {
    return res.status(400).json({
      success: false,
      message: 'الإجراء غير صالح. الإجراءات المتاحة: start, stop, restart'
    });
  }
  
  const tool = tools[0];
  
  switch (action) {
    case 'start':
      tool.status = 'running';
      res.json({
        success: true,
        message: 'تم تشغيل الأداة بنجاح',
        timestamp: new Date()
      });
      break;
    case 'stop':
      tool.status = 'stopped';
      res.json({
        success: true,
        message: 'تم إيقاف الأداة بنجاح',
        timestamp: new Date()
      });
      break;
    case 'restart':
      tool.status = 'restarting';
      setTimeout(() => {
        tool.status = 'running';
      }, 2000);
      res.json({
        success: true,
        message: 'تم إعادة تشغيل الأداة بنجاح',
        timestamp: new Date()
      });
      break;
  }
});

// التحقق من وجود تحديثات جديدة
app.get('/api/admin/updates/check', authenticate, authorize('admin'), (req, res) => {
  const availableUpdates = updates.filter(update => update.status === 'available');
  
  if (availableUpdates.length === 0) {
    return res.json({
      success: true,
      message: 'النظام محدث',
      hasUpdates: false
    });
  }
  
  res.json({
    success: true,
    message: 'يوجد تحديثات جديدة متاحة',
    hasUpdates: true,
    updates: availableUpdates
  });
});

// تثبيت التحديث
app.post('/api/admin/update', authenticate, authorize('admin'), (req, res) => {
  const { updateId } = req.body;
  
  if (!updateId) {
    return res.status(400).json({
      success: false,
      message: 'معرف التحديث مطلوب'
    });
  }
  
  const update = updates.find(upd => upd.id === parseInt(updateId));
  
  if (!update) {
    return res.status(404).json({
      success: false,
      message: 'لم يتم العثور على التحديث'
    });
  }
  
  update.status = 'installing';
  
  // محاكاة عملية التثبيت
  setTimeout(() => {
    update.status = 'completed';
    tools[0].version = update.version;
  }, 5000);
  
  res.json({
    success: true,
    message: `تم بدء عملية التحديث إلى الإصدار ${update.version}`,
    updateInfo: {
      startedAt: new Date(),
      estimatedCompletionTime: new Date(Date.now() + 5 * 60 * 1000), // بعد 5 دقائق
      status: 'in_progress'
    }
  });
});

// مسار عام للاختبار
app.get('/api/public', (req, res) => {
  res.json({
    success: true,
    message: 'هذا مسار عام متاح للجميع',
    timestamp: new Date()
  });
});

// معالجة المسارات غير الموجودة
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: 'المورد غير موجود'
  });
});

// معالجة الأخطاء
app.use((err, req, res, next) => {
  console.error(err);
  
  res.status(500).json({
    success: false,
    message: 'حدث خطأ في الخادم',
    error: process.env.NODE_ENV === 'production' ? undefined : err.message
  });
});

// بدء تشغيل السيرفر
const server = http.createServer(app);

server.listen(PORT, () => {
  console.log(`السيرفر يعمل على المنفذ ${PORT}`);
  console.log(`تاريخ البدء: ${new Date().toISOString()}`);
  console.log('الأقسام المتاحة:');
  console.log('1. قسم الأداة وملفاتها: /api/tool');
  console.log('2. قسم بيانات الترخيص: /api/license');
  console.log('3. قسم التحديثات والتحكم: /api/admin');
});

// التعامل مع إشارات الإنهاء
process.on('SIGTERM', () => {
  console.log('تم استلام إشارة SIGTERM، يتم إغلاق السيرفر بأمان');
  server.close(() => {
    console.log('تم إغلاق السيرفر');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('تم استلام إشارة SIGINT، يتم إغلاق السيرفر بأمان');
  server.close(() => {
    console.log('تم إغلاق السيرفر');
    process.exit(0);
  });
});
