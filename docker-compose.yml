version: '3'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    restart: always
    environment:
      - NODE_ENV=production
      - PORT=3000
      - JWT_SECRET=your_super_secure_jwt_secret_key_change_this_in_production
    volumes:
      - ./logs:/app/logs
      - ./ssl:/app/ssl
    networks:
      - app-network

  redis:
    image: redis:alpine
    restart: always
    networks:
      - app-network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: always
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
